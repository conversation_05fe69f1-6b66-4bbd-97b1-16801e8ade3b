# 🛠️ Hopen Backend — Development Environment

This document describes how the **development** backend stack is run locally via Docker Compose on macOS (Apple Silicon or Intel) and which endpoints, ports, and credentials the Flutter client expects when `ENVIRONMENT=development`.

---

## 1. Quick-Start

```bash
cd hopenbackend
cp .env.local.example .env            # customise if needed
export DOCKER_HOST_IP=$(ipconfig getifaddr en0)  # Mac LAN IP
docker-compose up -d
```

*All containers bind to `0.0.0.0`; mobile devices on the same Wi-Fi can reach the host via **$DOCKER_HOST_IP***.

---

## 2. Exposed Services & Ports

| Service | URL / Host | Port(s) | Notes |
|---------|-----------|---------|-------|
| API Gateway (HTTP/3) | `$DOCKER_HOST_IP` | **4000/udp** (QUIC) <br/>**4000/tcp** (HTTP/2 fallback) | Env var `API_PORT` |
| Kratos Public API    | `$DOCKER_HOST_IP` | **4433** | Identity flows |
| Kratos Admin API     | `$DOCKER_HOST_IP` | **4434** | Privileged admin |
| Hydra Public API     | `$DOCKER_HOST_IP` | **4444** | OAuth2/OIDC |
| Hydra Admin API      | `$DOCKER_HOST_IP` | **4445** | Token lifecycle |
| MinIO (S3)           | `$DOCKER_HOST_IP` | **9000** | Access key `minioadmin` |
| EMQX (MQTT 5)        | `$DOCKER_HOST_IP` | **1883** (TCP) <br/>**8083** (WebSocket) | User auth via JWT |
| Valkey (Redis)       | `$DOCKER_HOST_IP` | **6379** | Caching / rate-limit |
| PostgreSQL           | `$DOCKER_HOST_IP` | **5432** | `postgres/postgres` |
| Coturn (TURN/ICE)    | `$DOCKER_HOST_IP` | **3478**, **5349** | Username `kurento/kurento` |

---

## 3. Environment Variables Consumed by Services

```
# .env (excerpt)
POSTGRES_PASSWORD=postgres
MINIO_ROOT_USER=minioadmin
MINIO_ROOT_PASSWORD=minioadmin
EMQX_DASHBOARD_DEFAULT_USER=admin
EMQX_DASHBOARD_DEFAULT_PASSWORD=public
RATE_LIMIT_REQUESTS_PER_MIN=120
```

> **Security Note** All secrets are development-grade defaults; never deploy them outside local setups.

---

## 4. TLS / Certificates

* Local stack uses **self-signed** certificates generated via `ssl/generate-certs.sh`.
* Client certificate pinning **skips verification** when the SPKI hash list is empty (dev convenience).

---

## 5. CI Integration

Running `docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d` inside GitHub Actions boots the same stack for integration tests.

---

## 6. Data Volumes & Reset

```bash
# Remove all dev data (Postgres, MinIO, Valkey, EMQX, logs)
docker-compose down -v
```

---

## 7. Troubleshooting Checklist

1. **Mobile cannot reach backend** → confirm phone on same subnet and `DOCKER_HOST_IP` correct in Flutter run command.
2. **HTTP/3 fails** → Firewall blocking UDP 4000; client auto-falls back to HTTP/2.
3. **MQTT TLS errors** → Delete pinned hashes or accept self-signed.

---

© 2025 Hopen Inc.  Development environment only — do not distribute. 