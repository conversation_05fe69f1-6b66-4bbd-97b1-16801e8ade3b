# To do next

**UI MODIFICATIONS NEEDED**

- We need to fix the ui and the latency of the profile picture picker dialog (profile_picture_widget.dart)

- the "Close" button on the bubble history dialog is a bit higher than the "Close" button on the notification alert dialog

- the central button on the bottom nav bar should have the same size as the hopen logo on hopen_button_demo_dialog

- WE NEED TO VERIFY THAT THE architecture.md file for the backend correctly signifies these uses :
dialog_service_impl.dart - Likely handles dialog management
notification_service.dart - General notification service
real_time_notification_service.dart - Real-time notifications

- NEED TO IMPLEMENT PROPER PAGE TRANSITION :
https://api.flutter.dev/flutter/material/FadeForwardsPageTransitionsBuilder-class.html
https://api.flutter.dev/flutter/material/PageTransitionsTheme-class.html

https://codewithandrea.com/articles/flutter-riverpod-data-caching-providers-lifecycle/#caching-with-timeout

**PROBLEMS AT LAUNCH (HOPEN APP)**

Note: Some input files use or override a deprecated API.
Note: Recompile with -Xlint:deprecation for details.
Note: Some input files use unchecked or unsafe operations.
Note: Recompile with -Xlint:unchecked for details.

**Areas to watch / next steps**

Add OpenAPI/Swagger spec for automatic contract validation.
Integrate OpenTelemetry traces end-to-end (client → backend).
Strengthen unit tests around adapters (search schema mapping).
Apply strict linting (golangci-lint all linters) in CI pipeline.
Enforce CSP nonces once dynamic scripts are introduced.

⚠️ Areas for Improvement

1. Complexity Management
•  File Count: 300+ Dart files may indicate over-engineering
•  BLoC Proliferation: 18+ BLoCs might be excessive for some features
•  Deep Nesting: Some directory structures are very deep

2. Technical Debt
•  Generated Files: Some .g.dart files in source control
•  Configuration Hardcoding: Some API endpoints hardcoded
•  Error Recovery: Could improve offline/error state handling

3. Performance Considerations
•  Memory Usage: Multiple simultaneous BLoCs may impact performance
•  Database Queries: Complex joins could be optimized
•  Asset Loading: Large asset directory structure

🎯 Core Value Proposition

Hopen appears to be a "Spontaneous Social Communication" platform that enables:

1. Temporary Social Groups: Create time-limited "bubbles" for focused conversations
2. Multi-modal Communication: Text, voice, and video in one platform
3. Real-time Interactions: Immediate notifications and live updates
4. Privacy-focused: Temporary nature provides ephemeral communication

📊 Technical Assessment Score

| Category | Score | Notes |
|----------|--------|-------|
| Architecture | 9/10 | Excellent Clean Architecture implementation |
| Code Quality | 8/10 | Well-structured, but some complexity concerns |
| Testing | 9/10 | Comprehensive test coverage |
| Performance | 7/10 | Good foundation, some optimization opportunities |
| Maintainability | 8/10 | Clean separation, but high complexity |
| Scalability | 8/10 | Modular design supports growth |

🚀 Recommendations

Short-term
1. Consolidate BLoCs: Merge related BLoCs to reduce complexity
2. Optimize Database: Add query indexing and caching
3. Error Boundaries: Improve error state handling

Medium-term
1. Performance Profiling: Analyze memory usage and optimize
2. Code Generation: Move all generated files to build output
3. Configuration Management: Externalize environment configs

Long-term
1. Microservice Architecture: Consider backend service separation
2. Real-time Optimization: Implement connection pooling
3. Analytics Integration: Add comprehensive user behavior tracking


| **Real-time Calls** | WebRTC audio/video | ⚠️ Signalling placeholders (`call_repository_impl.dart` L694+) – requires backend signalling endpoints |
| **Push Notifications** | FCM token registration / topic-based pushes | ⚠️ Token update `TODO` (`fcm_service.dart` L300) – backend topic orchestration absent |
| **Local Persistence** | Drift DB for offline cache | ✅ DB + DAO present, migration strategy `TODO` note L215 |
| **Analytics** | PostHog event logging | ❌ No client or server instrumentation detected |
| **Security** | HTTPS via Nginx + LetsEncrypt | ⚠️ Config stub in `docs/technical/deployment.md` (rejected) but no repo Nginx conf committed |

*PACKAGES TO ADD*
https://pub.dev/packages/chewie OR https://pub.dev/packages/video_player
https://pub.dev/packages/infinite_scroll_pagination
https://pub.dev/packages/url_launcher
https://pub.dev/packages/battery_plus
https://github.com/fzyzcjy/flutter_smooth
https://pub.dev/packages/sign_in_with_apple
https://github.com/google/osv-scanner
https://pub.dev/packages/upgrader
https://pub.dev/packages/freerasp

https://pub.dev/packages/wakelock_plus (maybe)
https://pub.dev/packages/device_info_plus (maybe)
https://pub.dev/packages/sqflite (maybe)
https://pub.dev/packages/just_audio (maybe)
https://pub.dev/packages/auto_size_text (maybe)




VERY EXTREME TODO FOR BACKEND

A. Schema & partitioning
Bucket messages by (conversation_id, month) or by a rolling
sequence shard (e.g. conversation_id + (msg_id % N)).
Cap ≤ 5 – 10 k messages per partition; keep partitions < 100 MB.

B. TTL strategy
Short uniform TTL (e.g. 30 days) + TimeWindowCompactionStrategy.
Avoid deletions; rely on TTL whenever possible.

C. Operational controls
Daily nodetool garbagecollect during low-traffic windows.
Dashboards for tombstone_scanned / tombstone_live ratio.
Use LOCAL_ONE reads + Valkey cache for “hot” chat fetches.

D.For Compression & chunked delivery, Use HTTP/3 range requests; deliver only the byte-range the player seeks.

E. enable “SmartGraphs” to shrink cross-shard look-ups (arangodb)
use Citus (open-source) to turn the postgresql cluster into a distributed Postgres with online repartition-ing and reference tables.

F. Add an adaptive protocol selector in the client SDK: QUIC → TCP/TLS → WSS.
Switch to “state change only” heartbeats plus “I’m alive” every 30 s when state is unchanged; saves ~80 % overhead.



TECHNICAL DEBT :

Current Status 🔍
Backend (Go):
HTTP/3 is partially implemented using Quic-Go
Main REST API endpoints support HTTP/3
Basic QUIC transport layer is in place
Flutter App (Client):
Currently using standard HTTP/2
WebSocket over TCP
MQTT over TCP
Standard WebRTC implementation
Should we implement QUIC? 📊
Pros of QUIC Implementation:

Connection migration (great for mobile)
Reduced latency (0-RTT connections)
Better performance on poor networks
Future-proof architecture
Cons to Consider:

Added complexity
Limited Flutter support
Development overhead
Potential stability issues