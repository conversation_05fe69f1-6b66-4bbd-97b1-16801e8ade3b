#!/usr/bin/env dart

import 'dart:io';

/// <PERSON><PERSON><PERSON> to validate the four-layer dependency rule in the Hopen app
///
/// Four-layer dependency rule:
/// 1. Presentation layer can only import from Business Logic layer
/// 2. Business Logic layer can only import from Repository layer
/// 3. Repository layer can only import from Provider layer
/// 4. Provider layer can only import from external packages
///
/// Usage: dart scripts/validate_architecture.dart

void main() {
  print('🔍 Validating Four-Layer Dependency Rule...\n');

  final violations = <String>[];

  // Check presentation layer violations
  violations.addAll(_checkPresentationLayer());

  // Check business logic layer violations
  violations.addAll(_checkBusinessLogicLayer());

  // Check repository layer violations
  violations.addAll(_checkRepositoryLayer());

  // Check provider layer violations
  violations.addAll(_checkProviderLayer());

  if (violations.isEmpty) {
    print('✅ All layers comply with the four-layer dependency rule!');
    exit(0);
  } else {
    print('❌ Found ${violations.length} dependency rule violations:\n');
    for (final violation in violations) {
      print('  • $violation');
    }
    print('\nPlease fix these violations to maintain clean architecture.');
    exit(1);
  }
}

List<String> _checkPresentationLayer() {
  final violations = <String>[];
  final presentationDir = Directory('lib/presentation');

  if (!presentationDir.existsSync()) return violations;

  final dartFiles = presentationDir
      .listSync(recursive: true)
      .whereType<File>()
      .where((file) => file.path.endsWith('.dart'));

  for (final file in dartFiles) {
    final content = file.readAsStringSync();
    final lines = content.split('\n');

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();

      // Check for direct repository imports (bypassing business logic)
      if (line.startsWith('import') && line.contains('repositories/')) {
        violations.add(
          '${file.path}:${i + 1} - Presentation layer directly imports repository: $line',
        );
      }

      // Check for direct provider imports (bypassing business logic and repository)
      // Exclude external packages (package:provider, package:path_provider, etc.)
      if (line.startsWith('import') &&
          line.contains('provider/') &&
          !line.contains('package:')) {
        violations.add(
          '${file.path}:${i + 1} - Presentation layer directly imports provider: $line',
        );
      }
    }
  }

  return violations;
}

List<String> _checkBusinessLogicLayer() {
  final violations = <String>[];
  final businessLogicDir = Directory('lib/statefulbusinesslogic');

  if (!businessLogicDir.existsSync()) return violations;

  final dartFiles = businessLogicDir
      .listSync(recursive: true)
      .whereType<File>()
      .where((file) => file.path.endsWith('.dart'));

  for (final file in dartFiles) {
    final content = file.readAsStringSync();
    final lines = content.split('\n');

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();

      // Check for direct provider imports (bypassing repository)
      // Exclude external packages (package:provider, package:path_provider, etc.)
      if (line.startsWith('import') &&
          line.contains('provider/') &&
          !line.contains('package:')) {
        violations.add(
          '${file.path}:${i + 1} - Business logic layer directly imports provider: $line',
        );
      }

      // Check for presentation layer imports (reverse dependency)
      if (line.startsWith('import') && line.contains('presentation/')) {
        violations.add(
          '${file.path}:${i + 1} - Business logic layer imports presentation layer: $line',
        );
      }
    }
  }

  return violations;
}

List<String> _checkRepositoryLayer() {
  final violations = <String>[];
  final repositoryDir = Directory('lib/repositories');

  if (!repositoryDir.existsSync()) return violations;

  final dartFiles = repositoryDir
      .listSync(recursive: true)
      .whereType<File>()
      .where((file) => file.path.endsWith('.dart'));

  for (final file in dartFiles) {
    final content = file.readAsStringSync();
    final lines = content.split('\n');

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();

      // Check for presentation layer imports (reverse dependency)
      if (line.startsWith('import') && line.contains('presentation/')) {
        violations.add(
          '${file.path}:${i + 1} - Repository layer imports presentation layer: $line',
        );
      }

      // Check for business logic imports (reverse dependency, except for models, errors, database, and repository interfaces)
      if (line.startsWith('import') &&
          line.contains('statefulbusinesslogic/') &&
          !line.contains('core/models/') &&
          !line.contains('core/error/') &&
          !line.contains('core/db/') &&
          !line.contains('core/repositories/') &&
          !line.contains('core/events/mqtt_event_interface.dart')) {
        violations.add(
          '${file.path}:${i + 1} - Repository layer imports business logic (not models/errors/db): $line',
        );
      }
    }
  }

  return violations;
}

List<String> _checkProviderLayer() {
  final violations = <String>[];
  final providerDir = Directory('lib/provider');

  if (!providerDir.existsSync()) return violations;

  final dartFiles = providerDir
      .listSync(recursive: true)
      .whereType<File>()
      .where((file) => file.path.endsWith('.dart'));

  for (final file in dartFiles) {
    final content = file.readAsStringSync();
    final lines = content.split('\n');

    // Check if this is a bridge service implementation (allowed to import BLoCs and presentation widgets)
    final isBridgeService =
        file.path.contains('call_notification_service_impl.dart') ||
        file.path.contains('call_state_service_impl.dart') ||
        file.path.contains('bloc_notification_service_impl.dart') ||
        file.path.contains('dialog_service_impl.dart');

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();

      // Check for presentation layer imports (reverse dependency, except for bridge services)
      if (line.startsWith('import') &&
          line.contains('presentation/') &&
          !isBridgeService) {
        violations.add(
          '${file.path}:${i + 1} - Provider layer imports presentation layer: $line',
        );
      }

      // Check for business logic imports (reverse dependency, except for models, errors, logging service, service interfaces, and bridge services)
      if (line.startsWith('import') &&
          line.contains('statefulbusinesslogic/') &&
          !line.contains('core/models/') &&
          !line.contains('core/error/') &&
          !line.contains('core/services/logging_service.dart') &&
          !line.contains('core/services/connectivity_service.dart') &&
          !line.contains('core/services/call_notification_service.dart') &&
          !line.contains('core/services/call_state_service.dart') &&
          !line.contains('core/services/bloc_notification_service.dart') &&
          !line.contains('core/services/dialog_service.dart') &&
          !line.contains(
            'core/services/notification_orchestrator_interface.dart',
          ) &&
          !line.contains('core/services/profile_picture_service.dart') &&
          !line.contains('core/events/mqtt_event_interface.dart') &&
          !line.contains('core/db/app_database.dart') &&
          !line.contains(
            'core/repositories/cache_first_repository_interface.dart',
          ) &&
          !line.contains('core/notifiers/') &&
          !isBridgeService) {
        violations.add(
          '${file.path}:${i + 1} - Provider layer imports business logic (not models/errors/logging/interfaces): $line',
        );
      }
    }
  }

  return violations;
}
