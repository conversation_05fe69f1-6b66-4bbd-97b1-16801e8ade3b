{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9882ac2f24a6780437b2028d196a7c5427", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cf7534d13f133094e6ccc876a1e8d662", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c11f950ff67fa7fff3f95716a8ccd01e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9818b2fe74bc3ce6653b01b7c14a36947e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c11f950ff67fa7fff3f95716a8ccd01e", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ca1a03cebf5760adeb923b9489a665e3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98659727ee5585e896f1412dee4ac814a5", "guid": "bfdfe7dc352907fc980b868725387e985c01f7eedf0d4fe2c06dee4e03ecc842", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880db300f129b24b8efe2c6de8919cdd0", "guid": "bfdfe7dc352907fc980b868725387e980625d38fd0be80f8566544bf5a81bc29", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1bf5e9d7c975797498127836e7127d3", "guid": "bfdfe7dc352907fc980b868725387e983f89641837b667355f505babf0a411cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893483939bef1efc36b682aa8938d3e90", "guid": "bfdfe7dc352907fc980b868725387e9894f7ede39fd83548573db4f66cf4eab2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860de9f7fb25c69abe250ca2553f230b9", "guid": "bfdfe7dc352907fc980b868725387e988e85305739613932bdbf1b0d38af70b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c222fd1ffab174e89b1501d8ff471d15", "guid": "bfdfe7dc352907fc980b868725387e9898842b9e1d7d043c8a7987cc07847769", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9ff8d2e037b5590abfc2d88a89702e0", "guid": "bfdfe7dc352907fc980b868725387e9863cea5b71d6535dda146df6f7ba4f194", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c7aa5ebd44555d7f07185999b08c02e", "guid": "bfdfe7dc352907fc980b868725387e985a55ef37e2544aa48eeff16916ee8b8c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d362039c00f901d99f5e64d52c2effd", "guid": "bfdfe7dc352907fc980b868725387e98226c4fd3118cde3e887c9efe19dbada5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98566cf4d634f5eff7e0c84f432260f638", "guid": "bfdfe7dc352907fc980b868725387e985384634663b1ceb4fd6e8c3192d5d48a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d97339069b7613f42dfa427476bc7cc", "guid": "bfdfe7dc352907fc980b868725387e980f1a9f2e07d03b68a73aa34841bd0195", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878e861bb7be10a57f5618a7a35e4e0ce", "guid": "bfdfe7dc352907fc980b868725387e98e47bfa15bc3e1214c434cff90e959ba9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0b82c3779a385e82d6c8a57ea6f2afb", "guid": "bfdfe7dc352907fc980b868725387e98e30f9b2c514485e1efdea48ee537b9aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98472f66ea53b6470aa4d89ccf5ee0dc6c", "guid": "bfdfe7dc352907fc980b868725387e988478b23520bf24d9f7922eb19ed387f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829cd906734cb7b301ccf3e1a6c77ac59", "guid": "bfdfe7dc352907fc980b868725387e984ce2245b417ae689f9f127080d0b56a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cab885d2336e69ec00a45fe5ba9988ee", "guid": "bfdfe7dc352907fc980b868725387e98a901c51cf6610d7cc56060ca232274e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862592c481a8d0bcf8261d5ddf381c68a", "guid": "bfdfe7dc352907fc980b868725387e98ee215fa40209a11395ece73ed76560c7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b552cd383d34780baa4dee4a22d1f7a", "guid": "bfdfe7dc352907fc980b868725387e98b72da7f88553ab57cfda873d6135ddcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cf596ab7f3c980e2763fa54d90a5705", "guid": "bfdfe7dc352907fc980b868725387e98888ad0e1754e476e6ed210b0e76c285a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dae522cd79c7b346b01552292a33983", "guid": "bfdfe7dc352907fc980b868725387e9805dcbb80273b8c77c9ee444e29f7c552", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c24655128d6ec170d151825ddb8487a", "guid": "bfdfe7dc352907fc980b868725387e982a4c03a34c5df2ffc2b1259283b6fac9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98856af9afe2747d5892f813e390eee34e", "guid": "bfdfe7dc352907fc980b868725387e98b45c6713bb5b0db0d1cccd872d470ae7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e91bf19369749810ad62141512eadc28", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98355c006fb4ece806d9e945d76b298c62", "guid": "bfdfe7dc352907fc980b868725387e98c62b73be802b926ac554720775e5fcc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d8c78408cc38332adf05a5748fe88ea", "guid": "bfdfe7dc352907fc980b868725387e982ae74bdcf907632489b77fd12eca5da7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98724b27bd940f2d5f21184787f45558d7", "guid": "bfdfe7dc352907fc980b868725387e980955f5e3605238f7dfdf210233f7c23b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcdca8ce52cf296737c73a0eba20a7b7", "guid": "bfdfe7dc352907fc980b868725387e98392c6a67db6736b7843a2eee92cd0dc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831781355f010ee33dd96c524127bf6c6", "guid": "bfdfe7dc352907fc980b868725387e985e7d6fac8002ff4fd8d508bb9e0f6fd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac8cd12e8d30dc53a6135ca15d879424", "guid": "bfdfe7dc352907fc980b868725387e98f9f23e14bb917513c7fef01df72532b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a78c75109e46176494f22ec886e0cc0e", "guid": "bfdfe7dc352907fc980b868725387e988e38fcfa8d2148bdbd8e93f5a52482d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850d65fd8e02634279f73c3e932d3b631", "guid": "bfdfe7dc352907fc980b868725387e98b5fcc40fcdeea01b103b20376322838e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ceabd50f5667ab0f57705b58b5959d0", "guid": "bfdfe7dc352907fc980b868725387e98dcf3a18bd6a13f350ac69bbf87bf052b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e06cb52878cf134522b3ac9513ff1f42", "guid": "bfdfe7dc352907fc980b868725387e98f509edc9c89611345352a29c129570f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812aee5a613447ada9cbb04ae477eb75d", "guid": "bfdfe7dc352907fc980b868725387e9863b48bb482ce876f596baaec233984bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9ba5d592e197421f944daccfde0f237", "guid": "bfdfe7dc352907fc980b868725387e98920b4888fc65bb4dac729f22c2de1e07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a221cc8c8751c87d9a3c65773164493e", "guid": "bfdfe7dc352907fc980b868725387e98d30390b3be8b22d88638de8737dd4193"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cef326288e17064b127cb330480fd57", "guid": "bfdfe7dc352907fc980b868725387e98a677966f4d9a6a8022ce6d0acccb4591"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f4219d32ba6b9931306221c96f1a63e", "guid": "bfdfe7dc352907fc980b868725387e980e81a74c5fb2aeefe7d5dc8213eb493a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986646c1d5428863e485000ac0113b01e2", "guid": "bfdfe7dc352907fc980b868725387e9865786cdd9431b2339f9a2da13215e07e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981891397c99a5b7d39895a814a7f64a4f", "guid": "bfdfe7dc352907fc980b868725387e98566f649912da9c2114fb8133aa472dbc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982af4e24e9615676268dd9e51ff10446d", "guid": "bfdfe7dc352907fc980b868725387e986209699fe5121d31e41c4bfad81fd8ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a28cb7d4a0b8c129ea9a7e03434aa229", "guid": "bfdfe7dc352907fc980b868725387e98ab4d9ba06cbc4967ab0cc1a061ac5a32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819dddf00c480f89fb36371dfb2159325", "guid": "bfdfe7dc352907fc980b868725387e989bc5440e5cd5b09fb5bf9c2788d68f8d"}], "guid": "bfdfe7dc352907fc980b868725387e98a8fea8a1e3952373afab52d187a01e0f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98fb546a30171540359c077361ad348cb6"}], "guid": "bfdfe7dc352907fc980b868725387e98bbd5ff3be8e7de66539bced665dc50a3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8082c605311cf9b3b7a59362cdbc28b", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e986b5af9031bdcfd5d3949741986d6310d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}