{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98698f693e58be873f70621860f479d0f2", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9868fa76f15815c5a2085336d654d153d5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9868fa76f15815c5a2085336d654d153d5", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d9d76a1c29f9011635b0409b0009570e", "guid": "bfdfe7dc352907fc980b868725387e98fe92b5afa8fe58bae3c9229d058e616a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828a50029349a89755136384558a17fe5", "guid": "bfdfe7dc352907fc980b868725387e985965519b3ec2b9053365fa530bf643bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d05ffbd3aab88c0b7ad0cd9f24c8a6ea", "guid": "bfdfe7dc352907fc980b868725387e98fa9d97f54a76579d59e404ebe634bcdc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acadd61c942064f4886f697bb57f8131", "guid": "bfdfe7dc352907fc980b868725387e9850bdaa7011aa3c91740c213b1a7fa2cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba2154940014df14db731867a03adf11", "guid": "bfdfe7dc352907fc980b868725387e98b69297aee86961e47e29414c45e849ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2341e307e7894293ec17e57e8937762", "guid": "bfdfe7dc352907fc980b868725387e985ca3c30a7f4851eb9b4ef33952a3f482", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d97001a246e46bd22565df0f69f8b7f5", "guid": "bfdfe7dc352907fc980b868725387e9812df9d9973c9f97679f7de972b48f817", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982098bdc1fd6daeaa400fa13912f506b1", "guid": "bfdfe7dc352907fc980b868725387e98abf8d1a3dd0a4ead2fb6398cea2425ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d19e20298569b803c5294c4f998824aa", "guid": "bfdfe7dc352907fc980b868725387e986340d5835ff3d4930a87aff596993c8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9a8e12221c8b45e702bff9982efa245", "guid": "bfdfe7dc352907fc980b868725387e98282e717420c34bd36656734c015ab0db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4f8e864c6e2e603b28e64d44ff172e8", "guid": "bfdfe7dc352907fc980b868725387e98f79617ce3eea92130c3e8deb24ac6052", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826803e8afc0012560d2efa22d20d81d9", "guid": "bfdfe7dc352907fc980b868725387e989a9c98af54d171ffc5dcfcd328a9f656", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2c53557d1953dbb04f3788515804b55", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988229b482aa1caa58264ec2f62beecb77", "guid": "bfdfe7dc352907fc980b868725387e98892ab7216e572450585dbb7fe836780a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810ca6202c1097e912653ff1a1d85a182", "guid": "bfdfe7dc352907fc980b868725387e983d12f44f6ad546c0e51ad40a38a62758", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af10f8911c0f903f251b1685ae2b9095", "guid": "bfdfe7dc352907fc980b868725387e98a42139f6edf79b3b7cbcf3d1471e4431", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98277d47b322b535cde2a6c35fe5386db7", "guid": "bfdfe7dc352907fc980b868725387e98d954eaec4de1c4988534c19c3b497a42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8349a0c0a36a0a0cdfeee1c3470f4b9", "guid": "bfdfe7dc352907fc980b868725387e98a1ebbf17f73356fde3a4c9c9ccaaf541", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98449cfc501c9162b7ba5a58b289338589", "guid": "bfdfe7dc352907fc980b868725387e9821e54dc62d1296156df602834f20fcc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b296ae438915a849b2fb220594558630", "guid": "bfdfe7dc352907fc980b868725387e986061ae92e4bce152885cc5c3a1c0e4d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899c665a1d8dbd95d05edd780f0f05b07", "guid": "bfdfe7dc352907fc980b868725387e98e7e268d654b1604ef998bf4cc75336d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1afb9a801146c6e8f78d562085c4725", "guid": "bfdfe7dc352907fc980b868725387e9854f174ef0b05263e6dcd47023ed0496c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e02850fc217692c003aa0f54c80ea829", "guid": "bfdfe7dc352907fc980b868725387e98a3f25623979afa23466422cd7a073ec5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f8359fffd47908f2e4e2cb33ec9b1a62", "guid": "bfdfe7dc352907fc980b868725387e98ffd488b77237b8fc440d41413454bb07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e82eb6428637c1ce83535c174ef40e33", "guid": "bfdfe7dc352907fc980b868725387e98867e367d9e5822a1ed3da2e5d879f4ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98968f4c0de3bcf8f53718d8be315ec9a9", "guid": "bfdfe7dc352907fc980b868725387e98f4e8406eff8c37b5cf57678723c2865d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c2d1fadb250e84978efbf7134171f42", "guid": "bfdfe7dc352907fc980b868725387e986a9a5442fb214160e10f186b31f4351e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f788f6131bbc82da9ee674d7c13941c7", "guid": "bfdfe7dc352907fc980b868725387e9856735a0df7a10f65de67a62396d1be8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c79be96bb998ee894d63099b5fc4232", "guid": "bfdfe7dc352907fc980b868725387e98d490619ce99b46bd5cde304795058c5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdf23adfd20ac05d0a9de2a53b108df2", "guid": "bfdfe7dc352907fc980b868725387e985b6b972d31abdb37d83e0217390f1111"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876c2f96322e263fa573ee17025532cc4", "guid": "bfdfe7dc352907fc980b868725387e9826e5537c184a98a817fabddd8b1af46b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c81d15ab3235a8f6ea56017334be30cf", "guid": "bfdfe7dc352907fc980b868725387e98a6f139e04c42be401173e09b8eb64304"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3a464b4d254560e88cb5fd75c653a0d", "guid": "bfdfe7dc352907fc980b868725387e98bba9b3f581dbe27be7f77e7dba39135c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984509d247c76c5f47a2263f419f7632e0", "guid": "bfdfe7dc352907fc980b868725387e98f5a18a02b63f8074b1935286ce097e7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98162d1597de4359598bb2e4a3816f3153", "guid": "bfdfe7dc352907fc980b868725387e982a4d902c47d2e168ad1b9faadb826b40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ffe6a6d5d3519dec7c42686a417eb0e", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab5bbfcfacd73f5d5b803618ed5c94b9", "guid": "bfdfe7dc352907fc980b868725387e98c7209acb4bbc11473400b95ba264e570"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa506d03431648b1f8b447c69b789c67", "guid": "bfdfe7dc352907fc980b868725387e98b9ad21760386409e6a8418d260c875a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839b98f22d224fda30ec5a4f540cd69d7", "guid": "bfdfe7dc352907fc980b868725387e9869daee10844bdc1377101bb06b07af6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831196bae1e03b6829c9c8852b2185864", "guid": "bfdfe7dc352907fc980b868725387e9880b83d2e94cb821fc34990ec5eb08f30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98960121e79af5c654d2cba002c50d976f", "guid": "bfdfe7dc352907fc980b868725387e983fc554d229dec51832d227dc3c6cd4f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dc3ef588b8aa78569758b5c3d8bedfb", "guid": "bfdfe7dc352907fc980b868725387e984d92ce2a6a775539ad9cd12b7b3c8702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882fc9ab101f49c395b5e29f2762e3f6f", "guid": "bfdfe7dc352907fc980b868725387e98b55b1eb31da0207c73caefaa35380afd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98658b79aceb59adcc8d109b144fbbf36f", "guid": "bfdfe7dc352907fc980b868725387e982a5ad3c71031968c83cf1f0dd2275285"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}