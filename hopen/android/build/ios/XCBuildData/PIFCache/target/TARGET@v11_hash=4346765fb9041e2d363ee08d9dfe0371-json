{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988b78009ff6ec4194bc67b65b88e4ceee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6da72224a0002e2c8d4ad6a4140a101", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9890539440db962d51d5c575a32df4ec13", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cab3693a69eb1c553c791758183685f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9890539440db962d51d5c575a32df4ec13", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f480631d57f584ae82569893436b3d19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f86cf34137d4124fb32e06f3112b28e3", "guid": "bfdfe7dc352907fc980b868725387e98faa2d77c0ef7eb87f7f8421e51c1b40a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e3dac75ef5081293a3cce12f493338", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e6cbc2c866c0862671676e09f0c52f0b", "guid": "bfdfe7dc352907fc980b868725387e98fa3e3f0bc11484509bf86e3d64bd157a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985595bdef0b54c3079b03aa44f600cd22", "guid": "bfdfe7dc352907fc980b868725387e9864c8953fa94e569827e0c657001ce70a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bde746ec9181a24a55b6f15aef9acc4a", "guid": "bfdfe7dc352907fc980b868725387e9859a84cf3bffd3871a87345f41608c313"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c9982b6b2e4609fa65d0a55d4e7f99a", "guid": "bfdfe7dc352907fc980b868725387e98792a83978ba12cc47c981203ceb0a6a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ed528aeaabff3f5a43791d51ec23ab5", "guid": "bfdfe7dc352907fc980b868725387e98e3c05ef736d87c4ecfde73d2c69a09f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c26815fe9ce1bb76bff332e826ddb0f7", "guid": "bfdfe7dc352907fc980b868725387e98c10f8ad0991db25cc36797815b22e879"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98772e83659f9429f8375d11855201946f", "guid": "bfdfe7dc352907fc980b868725387e98fcd5b2a5b5d74bc16671776b56c1bad3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898b80d92b8653f6df1278bfb76386c7b", "guid": "bfdfe7dc352907fc980b868725387e98161cebf82ef2cee41561b189b81457fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98774620ce1a3793ab2214c0c504169814", "guid": "bfdfe7dc352907fc980b868725387e98c6084da3d95092052f6052b4f30910b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824de911cbe4fd1260612f739651556dd", "guid": "bfdfe7dc352907fc980b868725387e987efe2b04a5728dac6bfe88fa9c080326"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890ab347cdf4fa9d43ffb5924c8f0ea9c", "guid": "bfdfe7dc352907fc980b868725387e98976038e4c84d06f0e4aa9b6d4707def3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818abe4a8201a4aa7eb910ea73b8b518d", "guid": "bfdfe7dc352907fc980b868725387e9832c48f37803ddf2a992dca32d23f3b09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e36191733ce4d519d08fc36b36b4a65b", "guid": "bfdfe7dc352907fc980b868725387e9813d30f72b4787879a62ed8b98594f6f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e103e76cbcbeb3c7dd96b0907c38795", "guid": "bfdfe7dc352907fc980b868725387e98d452709e9698d3cd41f9725c5b6e7c2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884d66df84d6c0e47853765e294f18347", "guid": "bfdfe7dc352907fc980b868725387e9842b079d5c587e71174380ff58ebd66e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98903a413e1d420e2d7527d6a94bb662bb", "guid": "bfdfe7dc352907fc980b868725387e98de127be58b3d579fbc556dc154d7c431"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f9715100bf4ada959c34c5def1085ad", "guid": "bfdfe7dc352907fc980b868725387e9874dcf20b567789aff666819d3294e2e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c8864c3438fed6ac74c41266689c433", "guid": "bfdfe7dc352907fc980b868725387e98cf77ec6605af85b4d6736fcaed9c378c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812f03b6339d8ea4dddfca194ea51108e", "guid": "bfdfe7dc352907fc980b868725387e981735885e06877f15b983026304b4c223"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cb3717a55aa51866a0a2a5325a347ff", "guid": "bfdfe7dc352907fc980b868725387e9899751671a043fc9bf2598917f035eac2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecfb99b1f639a88aec7d6df9b51010f1", "guid": "bfdfe7dc352907fc980b868725387e98939a608b2817e7e0a24e8f8b7e42764b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d964adb5f8426c7e6f43f159aa1e48d3", "guid": "bfdfe7dc352907fc980b868725387e988b4c0e1b73013da3777b7d3f73517d90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa8c2680fe63c290760a4f82586bc1e7", "guid": "bfdfe7dc352907fc980b868725387e98575fa82d6f345c4fafa668b9585aa5da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816ce74c9e0a1bb6344bdee4b21d16281", "guid": "bfdfe7dc352907fc980b868725387e98a0e0172d9e297206367817d37a477649"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98198a409188ae5132dc4d9869d2947867", "guid": "bfdfe7dc352907fc980b868725387e98f18aa597d0aefe31e160c1199693e392"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f477ad80cf092f565fbdb54ef5b8da99", "guid": "bfdfe7dc352907fc980b868725387e98981c2c0161408add3b761cca3d644e1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebae77ca7b9465ba834d6b22911f6936", "guid": "bfdfe7dc352907fc980b868725387e98ae3a1978e9194f232c5e1a2dc6fae9f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1824826b938d142f1c14a4638556e7b", "guid": "bfdfe7dc352907fc980b868725387e986f97b912b680c0434c19b6e373208345"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a97d3189d60f1b8c61949cdf684d6693", "guid": "bfdfe7dc352907fc980b868725387e98468845043fc5833491c9332b4d68b8fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832b4f8176dcd7dc884ffdbe11e019d42", "guid": "bfdfe7dc352907fc980b868725387e98cf171f881233b5242074709693683913"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea5b20bdcd54f8f45ea9df3ef7a3102e", "guid": "bfdfe7dc352907fc980b868725387e9839dfd4acc232f395c8fb617d576dfe4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862c528313ebc4fdc02258fadf92b8286", "guid": "bfdfe7dc352907fc980b868725387e98641f05bd371a222692dc2c654038ca85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a270c04b44534307e4b91220c537bd8e", "guid": "bfdfe7dc352907fc980b868725387e98cd212e177b9c42a1a6233cec7c2cc5f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853596386311a969c92e7b2402cf2a4c9", "guid": "bfdfe7dc352907fc980b868725387e98254ff070edfbe0041ecefa3e49d71831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984825b221c523519f4021bc2fae187cd1", "guid": "bfdfe7dc352907fc980b868725387e98f24ba2c13805a88817c4a81ef046368f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988361569ccf3fb38328f856da11813915", "guid": "bfdfe7dc352907fc980b868725387e9858e39c751c0f377fad78c329515a7244"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a53c0e42de893b90df773d466ae9fe66", "guid": "bfdfe7dc352907fc980b868725387e98eb9874e5f4175bd9dee2bde42465d6a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880138c1ec532c2727b038ba904d5f908", "guid": "bfdfe7dc352907fc980b868725387e988b6340e5bfe7cc86f95f98a9031566d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7bfa974fa2099874987cd9b4f06942d", "guid": "bfdfe7dc352907fc980b868725387e98d259083229b4eed28de4a364a94222ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b261808db0ee1da2fb759cb8440ecf", "guid": "bfdfe7dc352907fc980b868725387e98abdbdc46c4777a258e87db72ae55c2e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ecfa89e0a931451140d1bea00e590a9", "guid": "bfdfe7dc352907fc980b868725387e98ff93ab63383edd2228232f5abc7a7ace"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98575bada069c07aa57a6051e7096d5321", "guid": "bfdfe7dc352907fc980b868725387e98f3c9d9ba8b83859b2f08d288e052253f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ab0ff4f23057bd6d03550adaaa8d23b", "guid": "bfdfe7dc352907fc980b868725387e98369441872a80d2673fc8a519160a5750"}], "guid": "bfdfe7dc352907fc980b868725387e980055fbf3a11bc15129d28ebbe5cc1480", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e987ead9e15fc09a570554fa963eed1137d"}], "guid": "bfdfe7dc352907fc980b868725387e98da9cd099e7190758cfc42156c0f4e548", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8defae1b39d33d1c1b6a7ec3e8cf51c", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e987fd8694c1d36b88c99f78feb0d04dd25", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}