{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fa11c64af30d72ccc50d70a17c8456f9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9818629d6603430b0b3967e9ba795f5157", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9871232820b1f607b11eabcf58348fb78e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e8319addb897251758815d041e7a8543", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9871232820b1f607b11eabcf58348fb78e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/camera_avfoundation/camera_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/camera_avfoundation/camera_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "camera_avfoundation", "PRODUCT_NAME": "camera_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c6ec5ed6e90c86e61a1821622409c1e", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b8b365ec38700a1a1de3711521e48151", "guid": "bfdfe7dc352907fc980b868725387e9860e67ed67227a0a4f0cb34fc6d730026", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880fcf528385a73d9292d824da48b13b5", "guid": "bfdfe7dc352907fc980b868725387e988e0b6d9262f07d6928dbfcae12989bbc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842a3d769c46e40a6bb0701a85441a8b4", "guid": "bfdfe7dc352907fc980b868725387e98e8c795916e378879930d3794fa0b7009", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bb1eb31320ea44128eff38154d8a6a1", "guid": "bfdfe7dc352907fc980b868725387e9844d0bf83dfaf6bf192ca04a4ebec51a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98401f86dff4148ed6739cadb8da86a02c", "guid": "bfdfe7dc352907fc980b868725387e9880b3105b947b220b494e157d15db063b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f72eae3370b8488a2df628bde8f488e", "guid": "bfdfe7dc352907fc980b868725387e9889f8478a2c07af865b650f793308f28f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee0025a7fd2328dd42231a343f036cb", "guid": "bfdfe7dc352907fc980b868725387e9826163bca1a3411aa1fcffc38813df253", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856361d1fbd07db12254e1d227ba8bd62", "guid": "bfdfe7dc352907fc980b868725387e9869991bd9293bca05952ee21c717143db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa51c3cf054d7fa86ddca91bb2070af7", "guid": "bfdfe7dc352907fc980b868725387e98d0346820b02f16a49c7082c9ae081da9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988700c2865aad6f7c64aa33a345ec0748", "guid": "bfdfe7dc352907fc980b868725387e988405fbb349d38eb53b896f38bb517810", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c17edb5ad9cd0e46d173432cbbb61ae3", "guid": "bfdfe7dc352907fc980b868725387e9830a6837f17bc379c19b10e12f06f94fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d4d491c994fabc2d3565a17edc41831", "guid": "bfdfe7dc352907fc980b868725387e98ef16fd950af81bc9ff63984f19444691", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829c5a302c971c6c51e42dd58f888bb74", "guid": "bfdfe7dc352907fc980b868725387e9854ed864b6db7260c3b916adabda9b501", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d6853d7f646c7de86a8b5ee90fd971", "guid": "bfdfe7dc352907fc980b868725387e98bb990799d5d840de1d6cee5a9a66ddb9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c49b8c66c866e260f149d79b8e52b54b", "guid": "bfdfe7dc352907fc980b868725387e983c0f0710533b65eccc24f8848dae5082", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df0931858523c5fbb5d229c3802295cd", "guid": "bfdfe7dc352907fc980b868725387e98455558d987ca2769d91f0a59dc99683d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6c35133565fd34a444944f8ef8b0152", "guid": "bfdfe7dc352907fc980b868725387e9820e3eefb2ed08e931e2bdef920ea2a18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e6be917f25673f9f2714383389f387f", "guid": "bfdfe7dc352907fc980b868725387e9861f19da55516e31c8de7b9b22316c3a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea00434222c57caa665c81ea46c35148", "guid": "bfdfe7dc352907fc980b868725387e9883ba5637c2ca84d8de9c46256d73f76d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ea51b7a450ed5923a3076f646c3ad1f", "guid": "bfdfe7dc352907fc980b868725387e986cd1f953b912922ff5c41027b0d2e253", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98280b72cf789d2a4e68653117585d15bf", "guid": "bfdfe7dc352907fc980b868725387e98ff4be5d47bf60841f1e25652d394e508", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a95e4747ffb3b1946e9fa94263e5659", "guid": "bfdfe7dc352907fc980b868725387e98c3b10c4d91f215d71217c8fae713684d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98333de57d28fd606f481a4c2fb94abb44", "guid": "bfdfe7dc352907fc980b868725387e98fd09ec336671c5eb9ce8401619a6983e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba22e98065520a81836796942707fd7e", "guid": "bfdfe7dc352907fc980b868725387e983698b9a91f46d361946aa26a2840c771", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98439bdf163dad1cca7429b3fcb85a45b7", "guid": "bfdfe7dc352907fc980b868725387e9867a5da6b1cabed97574cbcee386f69b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861e7f36ce9c4619c819f47331b981f7b", "guid": "bfdfe7dc352907fc980b868725387e985753f8adbfdc18a303c139835e3c18b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872a2b7661964a5cd721f235995d77d8a", "guid": "bfdfe7dc352907fc980b868725387e988b54ce4a9f7a075fb39a5251153de707", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986afb656b91ec2da41932050452a9e20e", "guid": "bfdfe7dc352907fc980b868725387e987f24653cb73cfcc54e317cc3ba29e636", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a83cab10db6a544bfbf93710a04643ba", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98028172da92b1b4fce241ed5b99564cba", "guid": "bfdfe7dc352907fc980b868725387e98e165e39a0401425e8f9df0f151d6bff1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4f04a378466a5c1fe85ac0f1c101402", "guid": "bfdfe7dc352907fc980b868725387e98c99f26a40343d9bd89c11218e46043f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980541c2254a3a4b5a7ed4ee9674bcf528", "guid": "bfdfe7dc352907fc980b868725387e98294402b843b350a76012e2e9dad561e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0e86ef3dc0ee20abad40959e5235355", "guid": "bfdfe7dc352907fc980b868725387e988419b5fe193144039cf5531195ddf843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b76a870aa7cbd078752a3c85cb47c826", "guid": "bfdfe7dc352907fc980b868725387e985f0980fa11e1350ac529bc1193368b91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cf1c6839736f33d7a8c557ac76f867e", "guid": "bfdfe7dc352907fc980b868725387e98edb7c25dcb32311d09770308a13f9b6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98863f7cf8166ef13f4b6ede018f133bb3", "guid": "bfdfe7dc352907fc980b868725387e98bc8c2d9d57d51071901ff60499cdf180"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a9eaec418c5e18d825c90bd345d801b", "guid": "bfdfe7dc352907fc980b868725387e984187eb0520c3ab03f9b138f5eac5ee4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d519da0eb17e6776a824f402fdb702a", "guid": "bfdfe7dc352907fc980b868725387e98ce3c9b423aba2c04acc42693dbba562f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876e18d532be2ff5e907278a04792ba81", "guid": "bfdfe7dc352907fc980b868725387e98b2b786f8e30f4f0295f1fc715aa30f8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982799492c59955c0c4a5ff4f69911ab7a", "guid": "bfdfe7dc352907fc980b868725387e9817ee75c9b9430deb759056ef0d6ba7b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982780c220c80fe9318a221c2a5f0b6cb8", "guid": "bfdfe7dc352907fc980b868725387e98cf05ade7bc11abf07d489865ad310714"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988096867f2bdc8d46ae10d37beece8e4e", "guid": "bfdfe7dc352907fc980b868725387e98827ffc202bcd75547ea215833f31d831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b0960b15282902c8b0ac65d4a184592", "guid": "bfdfe7dc352907fc980b868725387e98546fb1a217f2405dc6bf94f06815fd8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faa955dc3668fb95c982bdc31302d841", "guid": "bfdfe7dc352907fc980b868725387e98608e830f26612424f2319921f74ff741"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877e0ae9c574cbc52f39bd76962f8aebe", "guid": "bfdfe7dc352907fc980b868725387e98b55f1074f1e5e615c0f04199745d7520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835beb3f41ae06837fc13932f2a2b62f1", "guid": "bfdfe7dc352907fc980b868725387e9846c63738fdd0349134fdc5a7699b8529"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b439674a9c4eb5b804cd6b15f9392e9", "guid": "bfdfe7dc352907fc980b868725387e98d66712f675932769d0f0e677ec2ca57f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d0cd20032a1f444e5b6525a5702de5c", "guid": "bfdfe7dc352907fc980b868725387e98b524a29452036a60e044d7d78feffb43"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98669dc35eb114aed2be98326c9303052a", "guid": "bfdfe7dc352907fc980b868725387e989336ba25a775ad98824ccf1afbc56b17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981090c447d744abdeba32300255a2122d", "guid": "bfdfe7dc352907fc980b868725387e98a3bd4cde6f2f77a4e061f11668f6e37f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984906c61af014816e07562fd8626ddcf7", "guid": "bfdfe7dc352907fc980b868725387e9869350723e1690d450f2df2103a05a611"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98613bbc5ba424e91fc62fd1ba2c7b70f4", "guid": "bfdfe7dc352907fc980b868725387e98bbd9f584a9f9ac94e42e5865c449e955"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aac2887f44df85d09b13424b380f02d1", "guid": "bfdfe7dc352907fc980b868725387e98acf9737f017ab5d168c9f30a0faa5f90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98044787614de5c92fe0843e500402b857", "guid": "bfdfe7dc352907fc980b868725387e98ae4fe099fe56a44462e6388770eb7124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859843933f36c1cc8c7d9bacdd0f8b88c", "guid": "bfdfe7dc352907fc980b868725387e984f1b971599272f77f45811332ea42c13"}], "guid": "bfdfe7dc352907fc980b868725387e981471165fa844fa1faa3347093109f47e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e9874931db5d8dbb437369dfcf894dc68e2"}], "guid": "bfdfe7dc352907fc980b868725387e9888e32946d8019edf8bd28d10364dc9d5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9867ce76049bbd164e30060a283f08bba4", "targetReference": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158"}], "guid": "bfdfe7dc352907fc980b868725387e983a8a5e1cfed65dc4530dce11917c56f4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98b9038e7e871b75150c57ed973218b158", "name": "camera_avfoundation-camera_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f08a09402d437c098acddc7bcf497e64", "name": "camera_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983903b9d6299cde09dc2b081ad04abafe", "name": "camera_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}