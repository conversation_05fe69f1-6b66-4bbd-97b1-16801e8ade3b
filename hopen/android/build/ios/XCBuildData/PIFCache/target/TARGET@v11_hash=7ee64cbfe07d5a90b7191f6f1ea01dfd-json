{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d3a330fd01940717c7b916d9cfd591a4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7817127e2bdf38599034cbace1e5e2c", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98399d556efc0298feeb5ff98cd7578299", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c9cfb530fdd1617ff99200e4b4ec6d2a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98399d556efc0298feeb5ff98cd7578299", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983ec3d2cdb3df53cceceb0ddde76f8b80", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b36623dfff3d26e3253e846a38813d5", "guid": "bfdfe7dc352907fc980b868725387e98d3f45c75a37638cc555a7ddfa19416af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98207caab6ef9b742759d75c85f13d3821", "guid": "bfdfe7dc352907fc980b868725387e98d029667696609cbb3e38fab1bac9da19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fedc47e4146d6f61f44bac63d556df2", "guid": "bfdfe7dc352907fc980b868725387e98789610238e431350f2e77a60190a9e75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae2c15d2ca562a85d222536366974117", "guid": "bfdfe7dc352907fc980b868725387e984005b4116e52907767f5b05b6ce82dd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecfcc838bc7a8e1e90112ca5a3bb2a0d", "guid": "bfdfe7dc352907fc980b868725387e98388cc1bf5e7e4d5eb0beb81698aef381"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ef8ced7a2f3d5b1d158430b9c099444", "guid": "bfdfe7dc352907fc980b868725387e9850c416ddbc157185d7b5defa602e8be7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd7079b9e1ed7d778d44ef06c75b26ca", "guid": "bfdfe7dc352907fc980b868725387e98019cfc8665186a1e76499672931bfc64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890353002c13e0ded852b8ebd15a05e0c", "guid": "bfdfe7dc352907fc980b868725387e98bbe5105cc058210d89d1624d0ea3d333", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c9d070baaa948afb08242e46c8f4729", "guid": "bfdfe7dc352907fc980b868725387e98cfdd1851414852963daeb22077f83513"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b09bf77b7cbe0f5c951f6df0990d1e9a", "guid": "bfdfe7dc352907fc980b868725387e985288a734cf540360d7196ae6ef150c56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986111cb2ee831969ad4d1356e6ccf87ad", "guid": "bfdfe7dc352907fc980b868725387e980cde5859433c20108c3511a432d07444", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d756e42edefd4f29d015471e2c342b4", "guid": "bfdfe7dc352907fc980b868725387e98d209a22f4715dbeca64eea95336b620e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cadb65a2496ead94a430775ba7ebeab2", "guid": "bfdfe7dc352907fc980b868725387e986ad7bf6f31686100df8cb70dd2cc441c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807af590cf5c396582ed2e5bc53cf3b61", "guid": "bfdfe7dc352907fc980b868725387e987d860aadabb90676914e2233daa4b9ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98932db14a785e89dc1471fab975c0d5e8", "guid": "bfdfe7dc352907fc980b868725387e98ed92696fea9aa2a8ddd89a7e5dfbd7b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7ae49b281fd9de28eab73927b277fd9", "guid": "bfdfe7dc352907fc980b868725387e9846788197ac3e251dcec40ef496eba423", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0b90bfad61209995eaae26242fb44f3", "guid": "bfdfe7dc352907fc980b868725387e98d85cdbbbca840f45a714ad115501c131"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d90fc14eaae7d7962ca554c34098e9e", "guid": "bfdfe7dc352907fc980b868725387e98d3d5bc6a43bc1d5d1dc64f6c9bcf8c05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f8cb60c4d541285ee53a48fc4acf310", "guid": "bfdfe7dc352907fc980b868725387e98b77bd5851f81237f94a0d50412b0346d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc3b081f821d82d4a88bdc762066caad", "guid": "bfdfe7dc352907fc980b868725387e98d6d6542ef665fe7427d9280883b83e6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2e12d0da18849f510a40c2548ca3652", "guid": "bfdfe7dc352907fc980b868725387e98e27e9b8f51354f58ca908378c91f4749"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d5b4ef988161ad7d7b6825f24e044b5", "guid": "bfdfe7dc352907fc980b868725387e985cd7bddd5e9e51a0f7f1e0421a084ad7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98714fa10ab325c231e6baa8d7d3659468", "guid": "bfdfe7dc352907fc980b868725387e98f22503cdf9081cbe6f01349555084a4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836b6ab2f9d17156e05f1097bb0c31a75", "guid": "bfdfe7dc352907fc980b868725387e98af561e0198c24562e98b7e2bee0967c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98163cc8aa018fca0f26d36429da50d25b", "guid": "bfdfe7dc352907fc980b868725387e98a3a2b7a6f55f3c1bb27797bd7788471c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849ee99a8c92308c3af9bef395a474838", "guid": "bfdfe7dc352907fc980b868725387e9861601de2aeec14efa8852169f156b7d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad3fe65b3f0bc6df791bdadec4324724", "guid": "bfdfe7dc352907fc980b868725387e9814a5c2bcb8d38b4e52979bf599d01744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddfaccbaf5d9cfe86f969deb4c8de56e", "guid": "bfdfe7dc352907fc980b868725387e981a24a560a802cd8b53779d69bb2568ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f76461b9cf5f42ad83ae49bda59813ec", "guid": "bfdfe7dc352907fc980b868725387e988d4e116e4b6924f72b9d1a3661e3d8c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982267e2e3b87b9c657223650889ab8d4c", "guid": "bfdfe7dc352907fc980b868725387e9890165a992fe4e210cd14907b53bb809a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8d674306e4ee7cd7101a992bf597cd3", "guid": "bfdfe7dc352907fc980b868725387e98b3ee91b24bc97f6baf4c6eb1964d21aa"}], "guid": "bfdfe7dc352907fc980b868725387e98752ccc02956757e38fa675213043a8e0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e220c788034677cc282f8c2697aa5336", "guid": "bfdfe7dc352907fc980b868725387e98a877b1549834d2ee33eafd450782742e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b846bb5e3f8e5eeb2574ddf773296775", "guid": "bfdfe7dc352907fc980b868725387e9800dc3659df70c488e3089efea529c04a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850f7283b8b535eecca79f92779a8f241", "guid": "bfdfe7dc352907fc980b868725387e987d5059f4083f3cf747efd3516f3983c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a3d18be0a7ac872ecc196f1c57a09f", "guid": "bfdfe7dc352907fc980b868725387e9832c1a19b5fbca8281e0d346aedd3aacf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98593a2401916481ff61f3b438cf72a5f9", "guid": "bfdfe7dc352907fc980b868725387e980b718fdfbac94c3a684878bbd037f672"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851f08f4478413225c494fa5b4f52c9e2", "guid": "bfdfe7dc352907fc980b868725387e9854c9fc7a61c03f6ce9f46d7fa9191294"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98245e4697d65900a82c0082d3d0949dac", "guid": "bfdfe7dc352907fc980b868725387e985d8f764007570d3c063ddc06ceccd8a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a276a7753e8736b03e4e232b5d3023e", "guid": "bfdfe7dc352907fc980b868725387e98d826659e1a94c681b7fb6fb51759a6f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98438a2f572a0d6ce0616c932ce3de5329", "guid": "bfdfe7dc352907fc980b868725387e988511ebf45ae299ded8b00b6ac4a090cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98468086a48e1ff11b148c78ae00ad56a0", "guid": "bfdfe7dc352907fc980b868725387e98d53bf2cd9ce099bea5566055c7f8bcf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869ea7d4dc179098920f3a7ca8267032a", "guid": "bfdfe7dc352907fc980b868725387e98c995702ead04d03d708d8ed1950a0798"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9426f2cf4c541f79e65b061382352dd", "guid": "bfdfe7dc352907fc980b868725387e983a34d48e7fedeead11d672035d7e95ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0a9de4b3b8c93187b3dddc644066a4d", "guid": "bfdfe7dc352907fc980b868725387e98e0962e337051d7d9b77d18151423b263"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a51f1b5a64a6439052e945876ad56e5", "guid": "bfdfe7dc352907fc980b868725387e984f27a2a8c59de8ec027884fa89c4d833"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98622f80bb1ad6e4cc21ed57e1b6f65623", "guid": "bfdfe7dc352907fc980b868725387e98acfe453bb13941ba887547831ac6f64e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867c15212e2314b8f8a00c529d4b193e4", "guid": "bfdfe7dc352907fc980b868725387e982bbab2eae4613438638e1c26d2ce6c6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849d133745a7ca1b1c9cf0fbc74057afe", "guid": "bfdfe7dc352907fc980b868725387e98930cb1a97e15ef9ee08d2b931e6d4cdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1bdc9eb161288aa068651d54fb5d041", "guid": "bfdfe7dc352907fc980b868725387e98c3bb9383e2bbd9e8fead861ea79cd26e"}], "guid": "bfdfe7dc352907fc980b868725387e9811bad71055054aa99b949f8f87fa487c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98663e04a9c05308a0c5b62f418e92e580"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98147ddd183486f54d4497ffb62c36358a", "guid": "bfdfe7dc352907fc980b868725387e98578919b30c153b80efb4f087ac93466c"}], "guid": "bfdfe7dc352907fc980b868725387e989b84fd5ac635f57493c99feaef4f61a3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d63a92b2fe4a259960adfa99ac9c1215", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9878a6ea0ad77a31c218d41b04dec5fb8b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}