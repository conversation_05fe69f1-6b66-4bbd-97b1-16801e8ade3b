{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983c520d7931c555c9d06ae3652fcfe507", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983a09559e25449ad12b52c60d111d6302", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a2bdf95bc6f9081b48bc6e050ea12988", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aaf05c98fd192183d2375939457615b2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a2bdf95bc6f9081b48bc6e050ea12988", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983b887e7b379a653c70aee7551b6b3a78", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cb2ac8bdb3cf76966e83d8ec40a442b2", "guid": "bfdfe7dc352907fc980b868725387e983d8195858149ee34c6595e239718158e", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9802df456b244c22473c9d9c243511aaf3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9882005108b514bf2873631fd950f99ac2", "guid": "bfdfe7dc352907fc980b868725387e9862e5b4ed04d568d12c5a8828c6f2aaad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5524854bcdf5e75bf3f20b32550dec7", "guid": "bfdfe7dc352907fc980b868725387e98b2370f26861d1e2dba8759e1e94ee664"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f3cf0874563fb3514133165d59d7dc2", "guid": "bfdfe7dc352907fc980b868725387e98dccd2f3054b66264d25fceb820bca66e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880bca1098a017385a50e42b337d8a8c6", "guid": "bfdfe7dc352907fc980b868725387e9847bfc407d6abcf111b2bf795db0bacf4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5d96436ebe782ef9a4ae1a75c26c55a", "guid": "bfdfe7dc352907fc980b868725387e9877351c3a53a168102223770c8915bbeb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bd86f8956a4420a4496d90779ff15d4", "guid": "bfdfe7dc352907fc980b868725387e983ece189d60686a84187ba16a412a0e69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981690fabe996967e76d3c1404454239b3", "guid": "bfdfe7dc352907fc980b868725387e98dec413e013fe2b9325e69b5a96bf12b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98060e2bf18de56fab8c54a30a35a611f3", "guid": "bfdfe7dc352907fc980b868725387e98557d06a78263ac9da09c4686edab8400"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98913faeac31ce8e2c9a0148db0ffb0ea5", "guid": "bfdfe7dc352907fc980b868725387e98fe71b382d79e0e73f874729780ab591e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98948548994e6e3f74b90ba2fb2484c37f", "guid": "bfdfe7dc352907fc980b868725387e980e0c012478e016a114a199aa43989969"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be94ee20c560d001ba77f40e0c6449dd", "guid": "bfdfe7dc352907fc980b868725387e9832240457bf0bd099b055795b78b1804d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f57beb49dedfd295e7820fe82eaa93be", "guid": "bfdfe7dc352907fc980b868725387e985d207537270a8b896eca0c0279992cf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d40356b9c2d5dd78f4db6c6acb2f7606", "guid": "bfdfe7dc352907fc980b868725387e983b98f02d8b01efaf96543b59c11beff9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984309de7ca44a72f5fb37b84b798f0551", "guid": "bfdfe7dc352907fc980b868725387e9885a687528a9a497c7f6196b90a989b42"}], "guid": "bfdfe7dc352907fc980b868725387e98991bfe5f747efda387f3113281f2b24b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98013c6e24e75aff4a2414ae93e434edd9"}], "guid": "bfdfe7dc352907fc980b868725387e98dfe078922376367ef60b5abadbfeb2aa", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980866a7163e916fdb995700e69c58af5f", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98eea9e080d6367906c73f5f8db80eb55e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}