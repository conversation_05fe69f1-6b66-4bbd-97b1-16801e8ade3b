{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980517bebfba6069451f797135d96f2506", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fbb57b27272ba3ce24f6b6d7c0aacb6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987ad610d46808a237842ed9860e99ee31", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984bf457bd3c814663eca47c53dcd424ce", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987ad610d46808a237842ed9860e99ee31", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f8563d7176fcc31e350cafc9024f60a1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d00f0114fb4ea6e5230191eded75cf1c", "guid": "bfdfe7dc352907fc980b868725387e98a8ec738d11a5c41009a00e64575f6e15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98534dbc2c939ba4a4b440943ededcde3a", "guid": "bfdfe7dc352907fc980b868725387e98bc3dd92ea338547ea5373f4f176fb5c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9564f3463abe43387456f8cef3f5750", "guid": "bfdfe7dc352907fc980b868725387e988d65b25c5518d6378630b34bb2ce74ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801b4af4cb62454cfe50e5beb9f4d6ed2", "guid": "bfdfe7dc352907fc980b868725387e98bdb13b32d9d87e23ed56587fd6d4f515"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98175b4376860bd6096b81802928e0d051", "guid": "bfdfe7dc352907fc980b868725387e98beac0872cab98dc855794657d583eeef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98679c4bb7a1fdda7002b229dfa8f98a2d", "guid": "bfdfe7dc352907fc980b868725387e98de0d9a206e0c4bc833b14bd467ca1067"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986350aa8af5b1f444b5b88c195006bb4d", "guid": "bfdfe7dc352907fc980b868725387e98ffb7ffaf841a51a0f9747ba9423e5658"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec2d768d510bb62a3d3f306fb480f3e1", "guid": "bfdfe7dc352907fc980b868725387e987eb51e616341c98574a74eba3decad2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982150c930142d8de1a2bfe0fd6075aaf2", "guid": "bfdfe7dc352907fc980b868725387e984bf69bc3d06c2689e00b62444ede3830", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a2a513ffc6edd802190317036b11670", "guid": "bfdfe7dc352907fc980b868725387e983efc9a2545c66b61d80b176a1e539fd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836bfb01bc487ba82c82220fb076181d1", "guid": "bfdfe7dc352907fc980b868725387e98c60598229c5feb95cadb0c61a90e448d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98972453b7b0c0c9859b67165609be0540", "guid": "bfdfe7dc352907fc980b868725387e98bd187a76e9364388a91e09fb5d384009", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e7a0fc2795e3d017787d6c0393d622b", "guid": "bfdfe7dc352907fc980b868725387e98b440e9d968f6be07ab80de0571871964"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f0a5ac9cbf8e1fb2e0b4ce6a15249fb", "guid": "bfdfe7dc352907fc980b868725387e98c34a30dfa985740063938395cdf72b92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3c10fb82759a422a19cb00e9efaeb8a", "guid": "bfdfe7dc352907fc980b868725387e98d005f791a0379f1b4bfc82e348a14a79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980351bf59902507813200d6cdc909c9e2", "guid": "bfdfe7dc352907fc980b868725387e983c99ffc3f003588dad1709bf772dbda2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98684b52ec99a6da2855e4c09a6a0a47ab", "guid": "bfdfe7dc352907fc980b868725387e98000cc500ed6378f46b42b6b532ad0120"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0c923054345aa0c4df4c00c74c6093c", "guid": "bfdfe7dc352907fc980b868725387e984f22ee2372587decac7dce3794975484", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0fb70cefd6091c3b182f2012fa70097", "guid": "bfdfe7dc352907fc980b868725387e9815b1fe207d10bfdf87ad150eb3740309", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd3a63d5fc6f493574d642aa439cf607", "guid": "bfdfe7dc352907fc980b868725387e982f1b2ccda00b05d3e7929fe3a0f94124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98116761efd791e5534286df352b3126f6", "guid": "bfdfe7dc352907fc980b868725387e9840b80d792b72958b57a44f45be105e3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbda69726f44b8b7e05dbcf135d3dfda", "guid": "bfdfe7dc352907fc980b868725387e98c36d40ae1b79cfc1c703911d19215a07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b20f15272dd827ced213f3d1658f8cf", "guid": "bfdfe7dc352907fc980b868725387e980e3a068939a8bc86f1b37b4db1a9fad5", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e6058865bb3c240619467f8ed1cf432c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983a7ed9dba07826879a70a68e44107b2b", "guid": "bfdfe7dc352907fc980b868725387e9850557671b780125a8b47e6d7bcc93302"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3f3ba036ca6fc98d0df7030590122e5", "guid": "bfdfe7dc352907fc980b868725387e98ed2b4d76f8af044131789cb2695e453e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c2cae74ab0593b9748958eda9d2f0a0", "guid": "bfdfe7dc352907fc980b868725387e983b64c12f8408e33f20e97968fccf7eff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b056aef6fcdd0df38dbe8cceaf9efb6a", "guid": "bfdfe7dc352907fc980b868725387e988a83a1d352e9ce5e7988dca0a7b967a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863a3532f93d6dd6b133dae1c5c41b439", "guid": "bfdfe7dc352907fc980b868725387e9819a76c859fbfd940babfba7d6a9371c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bdc5ef001976b32d211933e1865a2e0", "guid": "bfdfe7dc352907fc980b868725387e98b747c2572500f53c59dd382b2b77118e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980364b788f97bbc528c1ee95064d99329", "guid": "bfdfe7dc352907fc980b868725387e98c9608308d1dab014cecfde7d1e683284"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f17b6e1f25f7c3610d348cb349c1221", "guid": "bfdfe7dc352907fc980b868725387e9874afac3c6bef0a9a93078e902277b75f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830eaa0eaaa0ad18f273e427c91254745", "guid": "bfdfe7dc352907fc980b868725387e98e3b1ba592d02ec308ad2633a2bc593d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e12c9740fdc07f6e367261a7415ed4bd", "guid": "bfdfe7dc352907fc980b868725387e98a99473dc5bcf1df628850df604594dab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98588128a49cd1ef1bb091d0f4f2aaaf93", "guid": "bfdfe7dc352907fc980b868725387e980c6b715ed646c23c29cc99685c43b396"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835a106fc518e99d02256013cb3825a66", "guid": "bfdfe7dc352907fc980b868725387e9855dc78d3d53256c44a00e9ee7e0da014"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ff02070764ee46dae012d9de4bd15f2", "guid": "bfdfe7dc352907fc980b868725387e9814ed20231538950b415419ec2559fed0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b551aff77ccf6cfeb0e5119319c715f", "guid": "bfdfe7dc352907fc980b868725387e98bfd707d25b6a3327d8e291257b6d279e"}], "guid": "bfdfe7dc352907fc980b868725387e9881867fb7587352043233da14525baa76", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e987ca68e5e3ace0b5850fd6c955735cf89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981925823dade64e954318992e61d3ff24", "guid": "bfdfe7dc352907fc980b868725387e98eabe43e2d3eea28cc364c6030301e17b"}], "guid": "bfdfe7dc352907fc980b868725387e98e83ed2b40f0afacc9378ed5480f99ca4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98351247568f00b2df838f5203ede46113", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9858821b31c3e9330285069e5a2e5f4a34", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}