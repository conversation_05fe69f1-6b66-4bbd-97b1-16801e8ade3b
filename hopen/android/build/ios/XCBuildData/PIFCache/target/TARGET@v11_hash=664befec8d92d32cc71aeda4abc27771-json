{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a4511b1ca641abd8ddf21e5f8b1a516b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986e71c3c06cb4cec996e15b85da50ae46", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e95e3d2554e30142b100b919667e04b0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98eab02bd8a80d5cdbcdcb10a9c65818ec", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e95e3d2554e30142b100b919667e04b0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986d46d568c3be87112484b0003d3e5ad9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9872965160481cd229e12db035e61f6427", "guid": "bfdfe7dc352907fc980b868725387e9832387f5b9e27c2d1a8e7071c45be8c40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b832fccea8359a475d8b79181b7a201f", "guid": "bfdfe7dc352907fc980b868725387e983d5639749acdfe0a6b81c233cb963cab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826efe7b6df9443011e7d893e5eee8a87", "guid": "bfdfe7dc352907fc980b868725387e980394796615e8fe9bd09c6fbeef2cd75d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98147be45e667eb043d097242356e41889", "guid": "bfdfe7dc352907fc980b868725387e98bc48c290227cf940a6cc9f88f3f2e667", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c9db8e0f37bc510f7716d458d528fe5", "guid": "bfdfe7dc352907fc980b868725387e984b5e513a9d13dfde44a9d0e6ed6dc173", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d077f3b2409086fcaffdd239c3d572d", "guid": "bfdfe7dc352907fc980b868725387e98c4b47839152c9a8a41215f621fe0473a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a77aeac8bde9f1f46e76b1d20df9693a", "guid": "bfdfe7dc352907fc980b868725387e988653a08f4da669db194329665ab9ef7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98849d94e9c092ef978522f52c9b728ad0", "guid": "bfdfe7dc352907fc980b868725387e986049fe10dbcaea8258eea6b5ac6c92d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99e299e948a0fe3c6911243915f37cb", "guid": "bfdfe7dc352907fc980b868725387e9897fc298f6a9cf6e7375fe1474faa058c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9866aca9dd3f7990ff2d72899c35c67c8d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f2fd5d367fd26058d09da85fa84bab46", "guid": "bfdfe7dc352907fc980b868725387e986711bac8e24c3a493c2282271fa8a4fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f5497f1c6c7399a4cf72c3b79021ae3", "guid": "bfdfe7dc352907fc980b868725387e9820c74c30b7a9d86e37fc21c72c793024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980684bfe62ae956568f384ffa21a1284c", "guid": "bfdfe7dc352907fc980b868725387e980947485befc53fc30cddea588e64d16e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829d51aefbda1807b338a7824e224b3e9", "guid": "bfdfe7dc352907fc980b868725387e98f213b6228028b629729288e5016cbfc7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852438ce2536574e2934f017e05781584", "guid": "bfdfe7dc352907fc980b868725387e98768b0466e4f827b17b87ef2abc84a3a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebbf82b0c7d00d2b8974d8a768472ad9", "guid": "bfdfe7dc352907fc980b868725387e98e9d21d004495ac458aa495f8a384942d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fc167b9334d449d91c485057fe36103", "guid": "bfdfe7dc352907fc980b868725387e98fb726bf29ef1b860b347e1a4bc11677d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4ea1bc90c5310bc563aef125528dac9", "guid": "bfdfe7dc352907fc980b868725387e980569f9f9c1baf1f8f1cb2fc6450d1634"}], "guid": "bfdfe7dc352907fc980b868725387e98c1d6d6d34aadbb05660ff163e2b70026", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98f6efa9fc1c3e30f4f111638a6b05328e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98147ddd183486f54d4497ffb62c36358a", "guid": "bfdfe7dc352907fc980b868725387e989bfaf296aa6ad088efe84053ba1ad2c3"}], "guid": "bfdfe7dc352907fc980b868725387e98cb6f7e86731534673a6272c18d88a46a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98513a908e98310a5ee907e3bbac20898a", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e984d5a1df85053b4fe30d1881ea7ab2b24", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98fcafec483f8d77676155b315651fec2c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}