{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983b56aad13866352c31dbbb59236abea0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_webrtc/flutter_webrtc-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_webrtc/flutter_webrtc-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/flutter_webrtc/flutter_webrtc.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_webrtc", "PRODUCT_NAME": "flutter_webrtc", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98af07e5ecb16e6cd4430f252d1a8e9070", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba30c192a7999059cbfc7722dfc0a314", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_webrtc/flutter_webrtc-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_webrtc/flutter_webrtc-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/flutter_webrtc/flutter_webrtc.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_webrtc", "PRODUCT_NAME": "flutter_webrtc", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9881a8d494332ac8e4fcf6a10584e96dae", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ba30c192a7999059cbfc7722dfc0a314", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_webrtc/flutter_webrtc-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_webrtc/flutter_webrtc-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/flutter_webrtc/flutter_webrtc.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_webrtc", "PRODUCT_NAME": "flutter_webrtc", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98692165e0035f618fd142f8168a538709", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b7f5914462d50f8e71c9b31e41cba396", "guid": "bfdfe7dc352907fc980b868725387e9805065baf3061182e7501ee27935e4ff3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa8c8587eecb069718018e8e0140bf48", "guid": "bfdfe7dc352907fc980b868725387e9855b9ddac09c3d3cc6fe177b3d97598fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b87cead4f6ab2b7daa1f4713908f110", "guid": "bfdfe7dc352907fc980b868725387e984891058883ed9eb2a3fac9ee0469f089", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831e5e1c0e10ce1a913154bc1e13b2092", "guid": "bfdfe7dc352907fc980b868725387e98445afd1af3fe905f96a7c26a3a0ffcb3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986effe6681f6cb6f99281cb067cda6905", "guid": "bfdfe7dc352907fc980b868725387e9875207be993e82281994592dc8e512deb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870b180c8f008ba127683c200607af27b", "guid": "bfdfe7dc352907fc980b868725387e98d4906e920f418b7353ea40dd326acf9d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98672be456b089ace8834689c49eb361fc", "guid": "bfdfe7dc352907fc980b868725387e98f5805cd3cb8a2b281f5d4af9526b0fa6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a34d75323bab82547d19f715949cc996", "guid": "bfdfe7dc352907fc980b868725387e988d9e19090639f7d87e902a40d233779f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e7de84cd2a4203e85b38f9a86d4e876", "guid": "bfdfe7dc352907fc980b868725387e987411e5e9eaa6d67cd08581ca48cd00d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba146096b8d9f900d2043c5062b2ff25", "guid": "bfdfe7dc352907fc980b868725387e98721ea9afbeb24c05b583dc6eaf10320a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827f70dd560deba7497b4e75df9d16e6f", "guid": "bfdfe7dc352907fc980b868725387e98ce8c3e814953607940713b75cd396e9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb0f065111a002d6e04006ba2664d2ec", "guid": "bfdfe7dc352907fc980b868725387e98fe198f09b7150e2c7426587b686a0325", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc83157c77bb4a2a5dc0b8cf4b97388f", "guid": "bfdfe7dc352907fc980b868725387e980f7fab57a72d2ba6562c3b808b3916d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef1e3879f5ea87b81f81e58e10e13bfd", "guid": "bfdfe7dc352907fc980b868725387e9811076fcef647331cab208d97ee9ba58c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb68153c4383eaa9485b072fbad3e0df", "guid": "bfdfe7dc352907fc980b868725387e9824d94a88d15ba3bb768d647687753347", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ea527eba981f0ff80a4f494ca769b9d", "guid": "bfdfe7dc352907fc980b868725387e98e3869558b73930cc04f93a35e6a4a05c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b8e8d3e4a8420fb7e381698a2bbbdd6", "guid": "bfdfe7dc352907fc980b868725387e98a37ea2e402866ff870c2670933ef1047", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98babf943d87cc437c9a2fc66179e7655a", "guid": "bfdfe7dc352907fc980b868725387e98b054af7070099ac2c6321521ab227c54", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dfba07733d629ca74c2d2a1b09034e7", "guid": "bfdfe7dc352907fc980b868725387e98d9522e0a8de9f9e32ddb13fd103e878c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987207213533077fe22046eee75ff5fb71", "guid": "bfdfe7dc352907fc980b868725387e985d8cebdf6824b7eb21d635d8df3f194c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ca74d61a5ae42e425ee90e4edca08dd", "guid": "bfdfe7dc352907fc980b868725387e985c4e6e6f8eff5d17ef6c0cd8895f2b52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857d9c1a9483855b3c0970fa6406ea098", "guid": "bfdfe7dc352907fc980b868725387e98be355e636515d87a24fb06cdf3035a4d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d3bda6dfea0a808654626f2f0717df5", "guid": "bfdfe7dc352907fc980b868725387e985b6d47e6d7c8cdc7186fd832c4d3d70f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb175b17dd142dbd9a4d3ebce4a6d7da", "guid": "bfdfe7dc352907fc980b868725387e9854725ab84b118a6bca3ba6faddf02592", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e984e855bb0118e19bf170578ef0835f0e5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9871f0655d689f3bb8b3ec7c54ca188f96", "guid": "bfdfe7dc352907fc980b868725387e98798e3de701a2d7abfaf58fce49e8e661"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2106a70874b03c1c0434231f0d09791", "guid": "bfdfe7dc352907fc980b868725387e98f8c5c3b91de722b6a0de19381f41f883"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc1db66ae1661b70568cc9aa38cc990d", "guid": "bfdfe7dc352907fc980b868725387e982db7e6e25727abd5f011a6c20ea02691"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98223b5bdb630d698aa1e6152c9b785b35", "guid": "bfdfe7dc352907fc980b868725387e9821c1f0bdec77dcc52e312f8f7301e68e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98490e505ca9dab25696f191c3a566e47e", "guid": "bfdfe7dc352907fc980b868725387e98d61a36c877f2a66b8739e52326887599"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b0f26e7308f86f4b9a683ed6a9bdb8e", "guid": "bfdfe7dc352907fc980b868725387e98d4c8d57bb70a8f3af0cab0c776a03542"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cbb6080d6cbb3676034ed4523e69100", "guid": "bfdfe7dc352907fc980b868725387e98da3d6a04eed77f817420475a5cce7e49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836e547e1f9af0769f08147366c25cf0f", "guid": "bfdfe7dc352907fc980b868725387e9837cebf4c7f5be86c07992e39f7e797e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98123f26c9aac7eac71026f88c9073861a", "guid": "bfdfe7dc352907fc980b868725387e9852b6362f6cb4c170eb345c743b5f973e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980196146f89b3886b3be7ba7983e20395", "guid": "bfdfe7dc352907fc980b868725387e98185770b820f49df8c3d0a8fa6d91335e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b0854b4d7e354be6aab445e88cb9e00", "guid": "bfdfe7dc352907fc980b868725387e985ec3b2fec356074a1e518c290e7fed6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0945a031461a100fa48c733b8a405aa", "guid": "bfdfe7dc352907fc980b868725387e98121f0c596139e215ad6f34f323188350"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814c2781ba8be2d1b4651c76cd3771780", "guid": "bfdfe7dc352907fc980b868725387e982b90be1947d39aa5af2bb5a60257ea1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c84908f7df56bdb4222304a0d94d9ab", "guid": "bfdfe7dc352907fc980b868725387e986ee24831656703f0b19779203f365503"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2bd32ed81c7e9cb10556fe9cb802b6d", "guid": "bfdfe7dc352907fc980b868725387e9862f1da8e5f16c69ec3f8a5e7e7139776"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcb5492d137b11a4530b9370f6ee196e", "guid": "bfdfe7dc352907fc980b868725387e98ca77b4fab5a4a9c7c3b18e619c756a51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e6b197c289b7a2d2afa7b2d2355e7f8", "guid": "bfdfe7dc352907fc980b868725387e98fa73c37dad35e86a95bd07e5ae71f2d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814d917a84c9bbc0b65744bced1fefbf6", "guid": "bfdfe7dc352907fc980b868725387e98ee42f5d727b9fc0338926f3e3e481962"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982de2e4470db50247587f9de017bb64ce", "guid": "bfdfe7dc352907fc980b868725387e983c5fcaac93a341ddb7b0e21b03145e72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df35bedac99b3432be9e04aa5e88ded8", "guid": "bfdfe7dc352907fc980b868725387e98439a38dfb84f3572062dafe631ea9538"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af86e1c5204cd3c99edac4ff81078c3e", "guid": "bfdfe7dc352907fc980b868725387e98484f3d91d363acfb570e781f35592417"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7c2d05964b7a7d3d54768614b656a3f", "guid": "bfdfe7dc352907fc980b868725387e982a6b176288b4c7fd41918bf81529da86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d25b13def4ca2bdc9ef805dc148bcb72", "guid": "bfdfe7dc352907fc980b868725387e987b73e6563d50f9e00b32956d5a8646b9"}], "guid": "bfdfe7dc352907fc980b868725387e983734f35701e1786855f48277d55195c7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98f18108a3c08948514392aefddec9fe03"}], "guid": "bfdfe7dc352907fc980b868725387e989a0fcc2d6e48d8d4b87b973b59759ac0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9874f18975964a9aa976b69f3b8d841cd0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98bec3e8e51ef07fea7d0fe122f20f026f", "name": "WebRTC-SDK"}], "guid": "bfdfe7dc352907fc980b868725387e98c9f376d8de36ce84a842b6215bd86f17", "name": "flutter_webrtc", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c1a402b6de6cac4973444d7073a5006", "name": "flutter_webrtc.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}