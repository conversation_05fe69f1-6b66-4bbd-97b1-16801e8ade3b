{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a1aee45ddf1990590b4ee25b60ad801c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98380460d7b41df39c52001301ad66be14", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98113b3f4f3f575941af0135659281e941", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98913e20384adb2102ea80f3b7073b1e73", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98113b3f4f3f575941af0135659281e941", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad2e091c3b3189aaee8abd5dc3266398", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d26021cd3b23c3ff06b4ec5ff2520e5c", "guid": "bfdfe7dc352907fc980b868725387e988e2bb5e4bdfea5e84cda0abeca8a1c81", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98330ef045fc6ea86cf9cb45cc63d37d56", "guid": "bfdfe7dc352907fc980b868725387e9805b34b161d5c8907fc9653b97ee818eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845f1727f566ab592ddcc32287d138a45", "guid": "bfdfe7dc352907fc980b868725387e98a3f2223ddf32c17afa7240fcdf1ec99b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98103dc9f280e938a47e100ccaff451502", "guid": "bfdfe7dc352907fc980b868725387e9847a9d5a0ff25ea6de6c0f47733dfc193", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e250bf2ca2bb8d5b7e7ee3be0870c95", "guid": "bfdfe7dc352907fc980b868725387e981bab9e82092981b444f199bd44d3f58b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdb58c949c01a7d1777ef5de5282022a", "guid": "bfdfe7dc352907fc980b868725387e98ae2288580aa7e82a39da374b056fc66e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e57f2bc9d4ceb46deb3a0dec77e0a70", "guid": "bfdfe7dc352907fc980b868725387e980b08ad74972b76443ebf696f33cc68d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98953c823e4b3d262af15ff72ffd957e8a", "guid": "bfdfe7dc352907fc980b868725387e980123ee2535b233d684b42447b2bd747c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b660c94ee606bac1e6c6657393e8eaa", "guid": "bfdfe7dc352907fc980b868725387e989c3204ff553b256d299d178bce5de934"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822474492d01518c33f5e6dda96d6fc4e", "guid": "bfdfe7dc352907fc980b868725387e98c65bd808bb02183a21d5d3f03017ff75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889dcddaada715ae9cf473b7898474605", "guid": "bfdfe7dc352907fc980b868725387e982af72718304cbcb18a009998251dd119", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98672a6cecd4173d8843e5a84da5c0cf2f", "guid": "bfdfe7dc352907fc980b868725387e98ebd070e5460be2505d9e665e062e8cd3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c97429eee9ba89a9c795be0894dfc564", "guid": "bfdfe7dc352907fc980b868725387e98f5ab21716b1ab473f043a706c6242f73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6f908c7b78d33bc42c4e335fa7655d8", "guid": "bfdfe7dc352907fc980b868725387e980fd805001dd66ad120268bb76c509814", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dccb9a3abb237ad280975ec3768708c6", "guid": "bfdfe7dc352907fc980b868725387e9845bb211417e3f5204a3776aa2530c52f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1bd57f98f95df0bbc1ee005e19756f3", "guid": "bfdfe7dc352907fc980b868725387e98339e0c27261e83378daf0e3e9a155d76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98533cb0d739da7308cd448b2e6fd771de", "guid": "bfdfe7dc352907fc980b868725387e98d18543affaeab196f7432fd75e0c9290", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986432eeec4cbf1e89ff7e196ed348bf8b", "guid": "bfdfe7dc352907fc980b868725387e989bd315164c03d7b8334053e4feadfb57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7d0db26d1f0625d6baf683c7188aa21", "guid": "bfdfe7dc352907fc980b868725387e98bb24e8695fabd06a2eca344229d1c051", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1227cda7049c1cbce9336b3a8562e5c", "guid": "bfdfe7dc352907fc980b868725387e980d3959432617925ed5c436671b1e3600", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98832b1c9ea98e4342c09ea9f8aeff134b", "guid": "bfdfe7dc352907fc980b868725387e9809a46ed5daa73ad0b6ea0a0c3aedda15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983119356b1c115e2014bfd36ba24c8596", "guid": "bfdfe7dc352907fc980b868725387e98c96a59a6a3156cced8ab1519292a4ac6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de91aa4b14631047cff79ae48e0f7c54", "guid": "bfdfe7dc352907fc980b868725387e98fe239d4652f1849328c081c868f27ddb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98247d737baeaeb20f4b81beedb0a51375", "guid": "bfdfe7dc352907fc980b868725387e981afbfdec27d8f880da145285b718ff98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956b1f9afc24b3421e022545299d5ca8", "guid": "bfdfe7dc352907fc980b868725387e98429513d9901c79504a8c802ced5c2eea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98608b3a658ade5e7aaf9cff9bc1e89b2c", "guid": "bfdfe7dc352907fc980b868725387e985707995377ee6c0277bbd7d82aa2d4d6"}], "guid": "bfdfe7dc352907fc980b868725387e980020930b4c082c4c66ec257b0b1be679", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d57e3573b6f8428fae9cd7beaa8bb836", "guid": "bfdfe7dc352907fc980b868725387e98586501417c3194376f1dfafebd4b6e49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f142fcc9a0e50d9975603d69b21a8bc9", "guid": "bfdfe7dc352907fc980b868725387e988a51a5f180300744230d42f380ac73db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848638533bada6a625e100c8e2c4b6056", "guid": "bfdfe7dc352907fc980b868725387e98305c80a2e2957df08e0aea30880d93a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98231ecc6e0b0b022acece58e92e9a390b", "guid": "bfdfe7dc352907fc980b868725387e9877c05ab85af82d14e5d744516eb694b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98937a3840cba0bc876ab913cd12275a79", "guid": "bfdfe7dc352907fc980b868725387e98eb1ad8401acc35e93886c6fec0453189"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895a068045c419bda7c2fb47a180b47bf", "guid": "bfdfe7dc352907fc980b868725387e9881478318678c3e3d73f20d0b55a37365"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986de351e82e7704022ab6632b32818ca3", "guid": "bfdfe7dc352907fc980b868725387e98128c67a3e32c0d900b4357f2e42faf48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ede23389ae8b0c058a8d66d30c14261", "guid": "bfdfe7dc352907fc980b868725387e983b174414b9e8a83f18772e1f98b5a3b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d826b668f9507674f360add69d34017d", "guid": "bfdfe7dc352907fc980b868725387e98b11e991263b036e49da020affe00eaa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6dadf0c6b796069c77e4b553a0e24d8", "guid": "bfdfe7dc352907fc980b868725387e987dde398aaf5be8adfb46267ba4a66b44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840906a241f184345a405f2e802021ff9", "guid": "bfdfe7dc352907fc980b868725387e98b60c37864af7c7afc795602afb08880e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bade15ae0a7ecf52392b0c4ac20a1580", "guid": "bfdfe7dc352907fc980b868725387e980e5b635187fc60913bd910a196c931ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839c57266ca93e9a987e640ca4c118717", "guid": "bfdfe7dc352907fc980b868725387e98b93df83bb26608c90019915302e7d079"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984609a305925e8f1625b5e6d24a5aa9d4", "guid": "bfdfe7dc352907fc980b868725387e98878f70a1aa2d422225e860efc987e6dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec13be12682cb32e7eb92715f3392186", "guid": "bfdfe7dc352907fc980b868725387e98041dc87283e44a261a5e6d31f44d609d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897849f0fb16873b101c8e7b7ef388176", "guid": "bfdfe7dc352907fc980b868725387e985af09bdac2c3247d8612dc1bced91074"}], "guid": "bfdfe7dc352907fc980b868725387e9849775ce310a899c96d6c3fe68259aac8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e983888089e125fb45bdae889bcb2766024"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98147ddd183486f54d4497ffb62c36358a", "guid": "bfdfe7dc352907fc980b868725387e98b2c40702355b56b70a13c7916c3a795a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c78e810d18182c6b4b5037c8fb836571", "guid": "bfdfe7dc352907fc980b868725387e98aacb7d9c87757a1fa8ebca82d9e0c268"}], "guid": "bfdfe7dc352907fc980b868725387e989c4f96da4894306b4ece18551ad6079d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e980604ef7859216cf25ee5903a87c7467e", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9895d59f34fd2809cf02b23e65b30b7183", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}