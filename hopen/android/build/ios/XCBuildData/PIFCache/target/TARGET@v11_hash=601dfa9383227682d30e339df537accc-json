{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98974b9f187f6642d747dadf67729f4ef1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/workmanager/workmanager-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/workmanager/workmanager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/workmanager/workmanager.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "workmanager", "PRODUCT_NAME": "workmanager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9888dbef0f515c67be8e54e197fef2777d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9839bb99d38472f15d0d407da3e8443e85", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/workmanager/workmanager-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/workmanager/workmanager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/workmanager/workmanager.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "workmanager", "PRODUCT_NAME": "workmanager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984236bd6ec20e651ca9785fcf39afd184", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9839bb99d38472f15d0d407da3e8443e85", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/workmanager/workmanager-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/workmanager/workmanager-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/workmanager/workmanager.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "workmanager", "PRODUCT_NAME": "workmanager", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c08120d05611fd3e83c69846989e849f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bf5f05676dbd471834a9c16c29e3d3cf", "guid": "bfdfe7dc352907fc980b868725387e9819ca62b883e369f7800f916b33daffc0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7f5f60db6bb027ca80df61f8b86c01a", "guid": "bfdfe7dc352907fc980b868725387e987a4f826a9397c2b4d2fb915c83cf8783", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98484ac1c34e0f2ff823dfb643d07c96e5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dec55bd8a8aeff38db37f0a4530c38f5", "guid": "bfdfe7dc352907fc980b868725387e983480c00a70f0d75b61f12ef9878224a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866519b43aa44c9939606cdc1823b2a61", "guid": "bfdfe7dc352907fc980b868725387e98a941fb80573092e0e8502699f99a2ff7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d027f91a636bb5df3da73ca86d0ec9bf", "guid": "bfdfe7dc352907fc980b868725387e9858e1d6151d5c445c46f2adfaff371f79"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1eb54ab9e99d6facf24f6bbcdcc48e4", "guid": "bfdfe7dc352907fc980b868725387e981330a5514e189c65927fe65e3fc59eaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869713242a2b9bb8efc7ea092490a8ffd", "guid": "bfdfe7dc352907fc980b868725387e98b256b8828d2b575334f71c5bc9552f99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f014db31c0d1e3c8ddf0f274235657e", "guid": "bfdfe7dc352907fc980b868725387e987c47371644d26a4ed57bd31438d7ca86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f3a3fe5c5bbb8654ea45a6911c52bb7", "guid": "bfdfe7dc352907fc980b868725387e98feaca9c228292344930fa767139713af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9564af080868b7426f145775cab097c", "guid": "bfdfe7dc352907fc980b868725387e98ee4b810e3df565032c3472768a7da0b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895a745d82b5c809060581bdff7839b82", "guid": "bfdfe7dc352907fc980b868725387e985be67a1b557c6fc2c53df3af6f11a8e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984819c27f29d5ab6bbdde358eca5ed076", "guid": "bfdfe7dc352907fc980b868725387e98e766e44c8aa4d0dc99996b8a513e491d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98335c1b59434ed33a9e169ad0e128257d", "guid": "bfdfe7dc352907fc980b868725387e983886e1d37f8921221f3ae02e1cad588e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2c1c8449d8639d7fd9461eaa248e2dd", "guid": "bfdfe7dc352907fc980b868725387e9827e69f012feacd6f50d116a1f792a884"}], "guid": "bfdfe7dc352907fc980b868725387e98e463333eb1ab32cdf8e0da26b6c99451", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e9845c1176b6116b5e50bfcc823b16b8ee5"}], "guid": "bfdfe7dc352907fc980b868725387e987f3b018026bf085edae3f9adea4a30e6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98737f1d054a73d37fc9056b1def6713ea", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98d30548e869f9421b5032ac47aa33240c", "name": "workmanager", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e985e9645e5871319d8c096fea0544c01b7", "name": "workmanager.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}