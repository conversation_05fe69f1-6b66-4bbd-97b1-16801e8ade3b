{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fe7c78f57842bd83424b468ec6d645a4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7a99df6e6a4873f55b3ad33955c73d7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9841c46aad3ce964f7cd60ff0f36898a6b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98961e7c9baafe5b4e26a5c59132394731", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9841c46aad3ce964f7cd60ff0f36898a6b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5e2f4ec47caf1c35a822cf8398efdf7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98650b26ca80c5d5908f4ead66b1ff0e89", "guid": "bfdfe7dc352907fc980b868725387e983a7f56a8bda7f4a32b582f3e3543ec6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d28cc35128fe0171719df4f2ba58951", "guid": "bfdfe7dc352907fc980b868725387e98eefa1fbb52d5820fb2f74287e89fa5be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c617e30d33ee9bc08aad0ca52b2273b", "guid": "bfdfe7dc352907fc980b868725387e98731bf14971647468f98880f5ab2ecebd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6bc10a47d875499198a53f1f258bd4c", "guid": "bfdfe7dc352907fc980b868725387e985188a1987f7cf2a34653743fc646f39e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981783d7b7e6b7f6567b1231758fb147f7", "guid": "bfdfe7dc352907fc980b868725387e988f117cd19bea713411f7d886e3ee57f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892b946e51de35c7a8ed6ec8100b48e9d", "guid": "bfdfe7dc352907fc980b868725387e98f7013b8d08955f03cd9ad217b6575ec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db8cc418de0d9469ad242f6a1b17d9a9", "guid": "bfdfe7dc352907fc980b868725387e98c3717fce461d8c3c727f6ab1e1ef8dd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d7be9313eea042664712750370a6503", "guid": "bfdfe7dc352907fc980b868725387e98971d704d92f697bb1c3431227aeca581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98213b5addb20b0274b6035924e534e4d5", "guid": "bfdfe7dc352907fc980b868725387e987fb0a97b5514da31c86f2442dd9c5c3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1540e933f966177f518328b8c0847dd", "guid": "bfdfe7dc352907fc980b868725387e98557eecafb08f3d4c91d7a27a04cbc98f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98792d2cba96d2cb51da6445276efc6d1d", "guid": "bfdfe7dc352907fc980b868725387e9831a692eeee4b67a14159ea381967273a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8d03cd0a500edbf524aaf0bf05fd2a7", "guid": "bfdfe7dc352907fc980b868725387e986a533507042bc1b488b8d411b4e467dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab2b1739f069597a75f48d87fd02c3aa", "guid": "bfdfe7dc352907fc980b868725387e98ae7871cec43b2a430799debd34abfca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3ba78d2af872dd8d0b0161d2919a9c1", "guid": "bfdfe7dc352907fc980b868725387e9843848d640b95d66ae7b0609d40822a18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98623bb6a48e8c1ce8679a84297e8373f7", "guid": "bfdfe7dc352907fc980b868725387e985df37ff259fbebfcef0385a6ec8da199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869aa7d9d7550baae548e8cfbd966fed0", "guid": "bfdfe7dc352907fc980b868725387e98f8b9a4332a9d35d417da9e60a68e1e5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987572f3c916b3166679cf9a7efeeb5b96", "guid": "bfdfe7dc352907fc980b868725387e987016ef18ef8e69d7bc59d3c64c1062eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803e7c4e168faa782191ad521d7f8167b", "guid": "bfdfe7dc352907fc980b868725387e98638fdbf659b7db656daaabefeb0fa1e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6c2e9d5d7bf29399abf516a1ce5536e", "guid": "bfdfe7dc352907fc980b868725387e982366610e765f0b5c67b62d5d700c6ccd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5473dec5426d56eda7388cfed6574fe", "guid": "bfdfe7dc352907fc980b868725387e98201a1ca6306b21611dc8f609d62d8e10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989818760f512a824fbd73cca2f9f08bce", "guid": "bfdfe7dc352907fc980b868725387e9805d1768fe792537334f391c041c31f35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f41a62b181aa4ab4f0e9ca643b1855d5", "guid": "bfdfe7dc352907fc980b868725387e981ca2893936b4e0ab83db00de99e3fe4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98825685c52f9bd095738323d8c979bfd6", "guid": "bfdfe7dc352907fc980b868725387e98df05095938a6e6a95d183f2debb7eeca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c45430d74b2b1410b05d1f54202ffb91", "guid": "bfdfe7dc352907fc980b868725387e988f3992c3226d3fffed029e44a32d785f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98523ac4c6cdd802443bec964fc31d0d5f", "guid": "bfdfe7dc352907fc980b868725387e98325b799f84feca7cce3ae0c158b3d6d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a27a943ad3799ad5b206850469fa67c3", "guid": "bfdfe7dc352907fc980b868725387e98d73be45a5c15dbc80c5e29415079a21a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a47a817b60c687c279cfca48cb2bf695", "guid": "bfdfe7dc352907fc980b868725387e9895e853d89407c6a61308b8ed634f6c83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98317f513d9731286af97e74d73ecca290", "guid": "bfdfe7dc352907fc980b868725387e983f07250a53c4dcc69dfbcf67ebdc325b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848216c2631b6c3c51298cac278453d10", "guid": "bfdfe7dc352907fc980b868725387e987481fc11365e35e19c58f07f6f4b732c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f008de6db827e6a7e52973302cb4672d", "guid": "bfdfe7dc352907fc980b868725387e985b6c7b86e2e3ddab6f8772c5362a6fba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98043ecb978faa90cf6bd097fafb6f66d4", "guid": "bfdfe7dc352907fc980b868725387e988919c6af1499779a90e7598a5e84fabe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b37acd89d54319e121468ff118b5bda", "guid": "bfdfe7dc352907fc980b868725387e98d6d0fe9c6b6c404e0d62b0cbcd05dfdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf9158b9cde54eeea2bd1220cb7a106c", "guid": "bfdfe7dc352907fc980b868725387e9810d41d30cf3c158f9509077744b70505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba34b4a043b4be8d04200c8123040d47", "guid": "bfdfe7dc352907fc980b868725387e9865d6cb4f29a80f2f2ce4e60e0090a0bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e62fb98de3779c0e7cd454eb1adc1c8e", "guid": "bfdfe7dc352907fc980b868725387e98ea323f1a49908e40b0b598e61db59b71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836f7b9ee8700c0b187b419d2ccce6775", "guid": "bfdfe7dc352907fc980b868725387e9866a81d67491e84a3ef0e7b3befdbdaba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c614f6928e244a116b9b2c60b64425d5", "guid": "bfdfe7dc352907fc980b868725387e98f9a04fb7607f394362f9bff3a632b600", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982db04166e93e6e985740e9fe3e876564", "guid": "bfdfe7dc352907fc980b868725387e98c1b9cbe39894801763b170085b67c5ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868a4fdeb891df828cf039c78a14b2b5a", "guid": "bfdfe7dc352907fc980b868725387e984e87e4b3e6b500cceaa46ede0ec13dfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a11ace98fef7ed8a154f3f4e4555a65", "guid": "bfdfe7dc352907fc980b868725387e9804e24ee381ecd94ecdaeb870ed0114ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa237dc761cdfcf0e5fe057e60e0ee7e", "guid": "bfdfe7dc352907fc980b868725387e987e8729dc670ba4e8dc2e7ab782a73fb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877321e3108c80e726537a3a343dc74a3", "guid": "bfdfe7dc352907fc980b868725387e980673b86a47f0fe84859c2176137bfbd7"}], "guid": "bfdfe7dc352907fc980b868725387e98dc3f6c017f50c3291e26862a62071ad6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984f879d127ea0d972ed5c16a75075e481", "guid": "bfdfe7dc352907fc980b868725387e981251f36c01e0bb324ca1cf7a8d9bddb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98955b6cffc23ab65f335bfd95c2d8c040", "guid": "bfdfe7dc352907fc980b868725387e98d8db050f1c7a5fd0ec0a4596c587f92c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989afb7077c46049bcc9636a8eb4bfad10", "guid": "bfdfe7dc352907fc980b868725387e98251d465f7239177f9eb05186be427f1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987970c9be8d02c849141756c00f040979", "guid": "bfdfe7dc352907fc980b868725387e98b9bf91278debb41b3833f220f1b435bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcaf295e1795ad69c8a1f1ab944ebf99", "guid": "bfdfe7dc352907fc980b868725387e98af769f021239e440439d2f37c2a8b47a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98623222397461dba7910f3ac4e969649e", "guid": "bfdfe7dc352907fc980b868725387e98f4410e2eefcc5e3073d1057d8a440acc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ec779fb45e79990eaa5e4e142f6c81b", "guid": "bfdfe7dc352907fc980b868725387e981c659a32af5c88dd60c9e7e92e503c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfba6dea48048be3cbf14722b236d961", "guid": "bfdfe7dc352907fc980b868725387e98385e9f712da19f3fdeab81bd33335d87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860970ccfb3f478f07500fd6baf371726", "guid": "bfdfe7dc352907fc980b868725387e98a44cf29d16204615eb6acc6a177ed90d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986252ef187b38196fb65937957d71e467", "guid": "bfdfe7dc352907fc980b868725387e981759ea69ef13d9431585b07246de10e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856a741dc158f7069378b227472400a9d", "guid": "bfdfe7dc352907fc980b868725387e98c7a014a0f2696eb65fdde5d95ff8a377"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b81afaeb0788ce99c695e12b9b80b52", "guid": "bfdfe7dc352907fc980b868725387e987d3c88b047f31c79453e480da1cee745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825111ca52006b9e8470065e4ee873cc6", "guid": "bfdfe7dc352907fc980b868725387e98754dd466afb74dc55b83de8dbd18dd4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98574cb28f703a0391d320557659ff57cc", "guid": "bfdfe7dc352907fc980b868725387e98d346057307b65a0c4cb63b7ce2cfbc65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84b645a175757673bd9a5c534b6d6ec", "guid": "bfdfe7dc352907fc980b868725387e9831a5a7cf4456b84e8b2fb59d15681b76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b09876de183e83e4c2e1ff79361941", "guid": "bfdfe7dc352907fc980b868725387e98630b01bae14ac405faf80a562b3c32b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986befebb8ecc9768a935f91d0f0781e1e", "guid": "bfdfe7dc352907fc980b868725387e98c9f34912719e77ba96475461238e0e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cdaec4d360591c4fefdbf8fcf2139bf", "guid": "bfdfe7dc352907fc980b868725387e984867af3440057818079d08ea0c4d6195"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db7af13c399b2c8d9015fb9cb7d6e54a", "guid": "bfdfe7dc352907fc980b868725387e9804dbc6bc4a995fc03805e33bbdc61583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98010c4d77af89912f5d71e404bf3a222d", "guid": "bfdfe7dc352907fc980b868725387e98d92cafd93ccc407f0b67993abcefdce5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e522d13babd3eee1ef66d68abf90d9a5", "guid": "bfdfe7dc352907fc980b868725387e9820143a2be1e220fac8de61a11087c01f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816fb834af58d5ccdb58af384b24e3c43", "guid": "bfdfe7dc352907fc980b868725387e98f5c29ea9255f8220e21e5404dc5a3f8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c9d86d2c21f29de8daa538911d535a3", "guid": "bfdfe7dc352907fc980b868725387e982693cc42ebb05a72e63217341d71bd9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f32ebb52946d4958ea6bedc52ea42aa", "guid": "bfdfe7dc352907fc980b868725387e985d0530ef6188ebe958e1f0d3f3298d82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98795e8a28f2568abe0362327093e9ea2f", "guid": "bfdfe7dc352907fc980b868725387e9829041b1e039eb006c470ff92591bf911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859744c9a140c902709a6f91ae70639c4", "guid": "bfdfe7dc352907fc980b868725387e98b92b9cf860855796967f283ce7415d09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848578ca5d5d3a41e2c54a424c55caa91", "guid": "bfdfe7dc352907fc980b868725387e98debe9957367259f62596ede328df84af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98813d410e933d7180b240bf9700b0ee4f", "guid": "bfdfe7dc352907fc980b868725387e98ec8f6bff25068400792a0da62301d62a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edce143115f367986cf98be932962823", "guid": "bfdfe7dc352907fc980b868725387e981808cef69632857ddb48af8a1b5faf76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f331d0b466ac57933cec2f76e34e5ed", "guid": "bfdfe7dc352907fc980b868725387e980f230ea024b72dbc683a1fabead8d6bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985365cb1535e2d7d50ad010766d2fccf7", "guid": "bfdfe7dc352907fc980b868725387e98deca3a41ff15842151e1be841cc3fff0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807332616400a6b04f814bcfe0a85e0c9", "guid": "bfdfe7dc352907fc980b868725387e98f21ff164321f2d57d89d63e9fa8d5d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c278ef1dc6a45ac5fd5e694fbb0a20d5", "guid": "bfdfe7dc352907fc980b868725387e981618ab1789cddc3783305bb624a1280e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827c75741901cc8872a4b90b4475e1c0f", "guid": "bfdfe7dc352907fc980b868725387e98c367e72e8bce33e83d8a76f05c126e9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e14c9f38fa3b686afdea7a486421cf6f", "guid": "bfdfe7dc352907fc980b868725387e98c36efdc476e07ebbd7263ace396ae758"}], "guid": "bfdfe7dc352907fc980b868725387e98053600d677a0ee3a6e8294a5f6c29926", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98a7f5c11a3ddc6c6109c703c9daffc90f"}], "guid": "bfdfe7dc352907fc980b868725387e986ef21295949b1adf438aaa172cba7c15", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987799c6ff721a2f90b939c7d64edb78cc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e985c8c1a45791dbc15ae7565c9ac08e62e", "name": "AppCheckCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}