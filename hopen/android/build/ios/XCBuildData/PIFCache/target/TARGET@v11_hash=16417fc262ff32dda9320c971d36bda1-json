{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c739bbadb616d455d86373fbec425cf3", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b749830fa94de6d7ebf1f4a6d802952b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9876759994a2e7899c5af571283868bd60", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983b2e96308caafa38626287fb044bff99", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9876759994a2e7899c5af571283868bd60", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fb514d949e51c312e0c18efac01a717", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d92f5223f79ea7ba69fe5cebace3555f", "guid": "bfdfe7dc352907fc980b868725387e98e0cc2f864a2973ef2ab9a1a45422ee95", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98268a5cf75c8a5b6a55ece567d4662839", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e4955bd5d21a3f8382eadd2e7d57505e", "guid": "bfdfe7dc352907fc980b868725387e98956b93610f4aee1611cda4893354c2a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871da4419c32a33647925c109a369f2bf", "guid": "bfdfe7dc352907fc980b868725387e98343617d09622c136bca76ef4cb58fe2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848b94650f59587ce1e7e470e6ac84a53", "guid": "bfdfe7dc352907fc980b868725387e98de0b19194691ced8bc3fc5c29478385c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b71c4859ed33d3c00953e635e3f34bd6", "guid": "bfdfe7dc352907fc980b868725387e98a66bb2bb44deaf8850617bccca2778ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892632ce970ef43666176304b07354ebc", "guid": "bfdfe7dc352907fc980b868725387e98522cb24a6085723ecb37afaa7816efdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a04f0237e31458f247e80e7e711a41ac", "guid": "bfdfe7dc352907fc980b868725387e98fa0e0588cea5150d3069903a75b2a83d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988aa6737e083c4f6a64bb2b7a6b40eb63", "guid": "bfdfe7dc352907fc980b868725387e989cdba4c08713a61354e38dc659643e80"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cd4a28d154305dda7a43dffe60732a1", "guid": "bfdfe7dc352907fc980b868725387e9823296abc04008f125ecc16848a006fa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3772673f4d84cb452f652ba225be229", "guid": "bfdfe7dc352907fc980b868725387e985a2b4fb11792e9ae138b63d11ce395ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98383781e6eddc26fd10d4cdee9ee4417a", "guid": "bfdfe7dc352907fc980b868725387e98bd198eaae072fac8c65521ab1d3acc3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8b81497d6c6308a8cf9b0b627a34d9d", "guid": "bfdfe7dc352907fc980b868725387e985dcc42c0d682fbfb29c6a861401c3070"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d4be63ae8a54b19689af8bedb81b71d", "guid": "bfdfe7dc352907fc980b868725387e9809374aea6393ffb5eb5d7a7919f9308a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6f5494d1411f3cd630c08fa285aafaa", "guid": "bfdfe7dc352907fc980b868725387e985c15877e330c1e271bbf9b058083b618"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989810e088e7b56f64091b307c1836901f", "guid": "bfdfe7dc352907fc980b868725387e98ff1557472407b65cde961472331b4099"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856f3688a67c26125d6c3c6c5e2ec9c45", "guid": "bfdfe7dc352907fc980b868725387e981a3f0f37d1eaf91c640d910f0b259117"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de0575ff85e7fb266ba1bd06ac88d224", "guid": "bfdfe7dc352907fc980b868725387e9829d1768491e875dabb00daad94698ece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c63061eb871bc1564665ace99c0ad1ae", "guid": "bfdfe7dc352907fc980b868725387e98463b55df84980e8e11040f50aa7241db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987173fbbab0fb8a7c27fe613d3f1066b4", "guid": "bfdfe7dc352907fc980b868725387e9817dff4532fe3a365a02266a135de8752"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987369fe7e2a42845ee763e63c25d3b626", "guid": "bfdfe7dc352907fc980b868725387e98b77659a67eb35ecb34c31bb8c7561aaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886b3cd128314b15bc617e88ab18761e2", "guid": "bfdfe7dc352907fc980b868725387e98b4721e8ce3db5ba0f936d8b9ac7e0ab9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a39b5103da9eb648c824504cea9566b", "guid": "bfdfe7dc352907fc980b868725387e986088e50d58e539879a7f86c2d73ab0fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa605c21c814fdf04244968b6d1a3ee5", "guid": "bfdfe7dc352907fc980b868725387e9817482b9048f2c016a39ee80700fa9dc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98829c836ac274875055bdac8c37800406", "guid": "bfdfe7dc352907fc980b868725387e98cdefd4ca0dc5d8771242df5f8797dfc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5c91e01b4eb9be7aaa669b0818a938b", "guid": "bfdfe7dc352907fc980b868725387e9855b624d563806176eacba31a83870aab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1403a1daea86c65574c43c22258e400", "guid": "bfdfe7dc352907fc980b868725387e980c0d9b8ced9f74b1833e7fd9db3ddc2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5ed16637f971e7a6e37a65654d89a29", "guid": "bfdfe7dc352907fc980b868725387e98e47c39a665049a0f9d8b5bfec0b99735"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a23b03071292ed958c4391cdcacf23cc", "guid": "bfdfe7dc352907fc980b868725387e98d3dc4af7737d7d637456593a5cc5673b"}], "guid": "bfdfe7dc352907fc980b868725387e98c6eef811180634e4fa2ab067867ad28e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98185b0a410d6773bbf97909ab82330835", "guid": "bfdfe7dc352907fc980b868725387e989fdc6f234b882c2d864f4abd38efe4df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ecf1a81ce5cd8b78914d0bb073baf23", "guid": "bfdfe7dc352907fc980b868725387e98df94492636de127d577e0d490e6ea5e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e981c2d6cd22747a90043aaafa834e369ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fc4d1bcef8ce62596157e2d0cd5cf4f", "guid": "bfdfe7dc352907fc980b868725387e984ecf68ae613fe817a92e45f12ae708f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981925823dade64e954318992e61d3ff24", "guid": "bfdfe7dc352907fc980b868725387e9891592efb284329b430898eb83266e012"}], "guid": "bfdfe7dc352907fc980b868725387e98cb852f9dff106e83a3eb1542332e22b0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98eb3a748caffe9483d19f044725003401", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98f047243e35f095b9f0c8079456567dbc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}