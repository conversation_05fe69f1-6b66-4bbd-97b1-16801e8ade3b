{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a1c84ed3c85fda71261aad9ee66543b4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398bac938fb47b84c16f19633c15c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c4995246defad023a32d1660cad75f67", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839d79a52f24f54321de12379511903ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c4995246defad023a32d1660cad75f67", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894fef8acb7de742f96aaab49fd64da62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9815c270de55ec356622919e9642fecb0e", "guid": "bfdfe7dc352907fc980b868725387e980223af070454170a0b7d3b1361ba6800", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986bed5be9ba8d2378862a0a12601dfeb5", "guid": "bfdfe7dc352907fc980b868725387e98af9baa1a214e18f0740673a53ee3a1e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d09eee2bda258d0eb889953c661e6fcb", "guid": "bfdfe7dc352907fc980b868725387e98d361524d0bbc0eae975162423ea39391", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858dc8246fbd6464ad8629625eeec5071", "guid": "bfdfe7dc352907fc980b868725387e98592724f0aead9aed6184ccba926c3358", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98779c65e6ff096ae1522395d8d46a7aa1", "guid": "bfdfe7dc352907fc980b868725387e985f4c9ea9715d308ccf7cfb4fa38d39f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888cf8c0ba8b894fe9406def5e9767d95", "guid": "bfdfe7dc352907fc980b868725387e985f4eb06ccf7b0e6b6f62b42e6e567952", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c979e7b52442ab34641979d2281e1882", "guid": "bfdfe7dc352907fc980b868725387e98200c518de51f12b07c10a1568b24e4a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848e7f461f76a0faec21c25a9364b59b8", "guid": "bfdfe7dc352907fc980b868725387e987423cabde9be5c9ce6ad4211d85a7a9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808f30702c29aa976ce93dc36b137ca76", "guid": "bfdfe7dc352907fc980b868725387e986622024fa4c5026bce3d6bedb93708bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac350a0eca9747d2eefc6ebe62539865", "guid": "bfdfe7dc352907fc980b868725387e988baaa201e8200465d5cad48cfc617506", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875079b87bb80346ccbd0b25f3a7ad08d", "guid": "bfdfe7dc352907fc980b868725387e9875fe1bb25210e274e094b1d5a28f48af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecdba04452868d9fc2e6b3cf2708763f", "guid": "bfdfe7dc352907fc980b868725387e98a3013bb633bcb8861c4af070676454c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2e86e2dad73ff45582899f40e944c7a", "guid": "bfdfe7dc352907fc980b868725387e986b370a394be613ba5799fa4d11c919ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b8836e144dbcb3b6dd4a402e350fd23", "guid": "bfdfe7dc352907fc980b868725387e98fb07394129e8e15c4a276f6c926ae120", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eb0862030691a2eb6770b3546c2e7a2", "guid": "bfdfe7dc352907fc980b868725387e989314e081c3f5bfab1279e3c2c1139a24", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983145ad27ab9a5d703e29269b9b2b583a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9850781659c5b921c3b8cd7b76fe265079", "guid": "bfdfe7dc352907fc980b868725387e98462d72be51e2d42e9f0113c685cf957a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f3f79321a327732534fbd5729e5ed2f", "guid": "bfdfe7dc352907fc980b868725387e989851f21dc1881e64d21e0223601a3ea9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f62d3d6e820191119954b083ee479be4", "guid": "bfdfe7dc352907fc980b868725387e988b2b0d3293fe5fa5163e848e73287fa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f28dbacf0af65314bc90228c8892ba9", "guid": "bfdfe7dc352907fc980b868725387e9878fe5122da43e13355af702578c47c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ced5b757e76b129e96bae7565cf411e3", "guid": "bfdfe7dc352907fc980b868725387e98152a413d79815b6b7e16f8222b059af3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811fb1a31e8958090a1da668be1fa835e", "guid": "bfdfe7dc352907fc980b868725387e98d8a60e9dc307978e06d0976c668337a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d995b40008c6a316f6526bf51b857a8", "guid": "bfdfe7dc352907fc980b868725387e985c4b75f26b14508b53e12df60d30b440"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847185d0c9f8faa023e51abb511cbe588", "guid": "bfdfe7dc352907fc980b868725387e9870213b2a7f693ef9848866859b406466"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df8e179e7b62afbec3e71a8126f09be7", "guid": "bfdfe7dc352907fc980b868725387e98f6ab12171d8d5fc29fc520257bd34abc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c57bc4f32ed60461dcd378af9c541b90", "guid": "bfdfe7dc352907fc980b868725387e9806ef2c8ff6903129ffb8c9def9edfc4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8732f34501888d0bbe21ed65394651e", "guid": "bfdfe7dc352907fc980b868725387e98a89106363ecf1c82c77a5e97d941ae89"}], "guid": "bfdfe7dc352907fc980b868725387e9884539da3bdf2ed4ad426ca202aaf6bc7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98c1dae7e29df157eddda815ae5462108c"}], "guid": "bfdfe7dc352907fc980b868725387e98119e723bd2257d68f10fcea70feb1342", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c10b7a3ff26395a834064a0172d6124", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98a3435bfc3b1be550d78f0653eca9c848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}