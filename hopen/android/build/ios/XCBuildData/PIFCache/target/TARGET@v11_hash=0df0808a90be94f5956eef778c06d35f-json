{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98037d17b9a3ebb862a87f2b79ebd6493f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/posthog_flutter", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "posthog_flutter", "INFOPLIST_FILE": "Target Support Files/posthog_flutter/ResourceBundle-PostHogFlutter-posthog_flutter-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "PostHogFlutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9887465c6ce5c1ba1bebfbb34ef1292262", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9810fc2e2507995c25828c5ff68be751bd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/posthog_flutter", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "posthog_flutter", "INFOPLIST_FILE": "Target Support Files/posthog_flutter/ResourceBundle-PostHogFlutter-posthog_flutter-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "PRODUCT_NAME": "PostHogFlutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e983560019665ed02462ecd230a64002770", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9810fc2e2507995c25828c5ff68be751bd", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/posthog_flutter", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "posthog_flutter", "INFOPLIST_FILE": "Target Support Files/posthog_flutter/ResourceBundle-PostHogFlutter-posthog_flutter-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "PRODUCT_NAME": "PostHogFlutter", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98dde24357620e7b74c3cab50a0fa7c9f7", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980417065c9d9ad8db8d07754ec61d2622", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98708d6d7ffbfa907417da9f2cfa21bdda", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98705927a2733506ec2be289e786285bd5", "guid": "bfdfe7dc352907fc980b868725387e98bb3d945cfc6b3c8a15b51ab96d71ed1b"}], "guid": "bfdfe7dc352907fc980b868725387e98cd9bae3fae743b757c271f094e76c5eb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98df92421e200a7bd296cb807b18bf05ab", "name": "posthog_flutter-PostHogFlutter", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98c73cf5693700cc2acc807e59daba8eea", "name": "PostHogFlutter.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}