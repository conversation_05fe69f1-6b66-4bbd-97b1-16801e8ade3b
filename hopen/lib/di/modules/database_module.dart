import 'package:get_it/get_it.dart';

import '../../provider/repositories/local_storage/local_storage_repository_impl.dart';
import '../../repositories/local_storage/abstract_chat_message_repository.dart';
import '../../repositories/local_storage/abstract_chat_metadata_repository.dart';
import '../../repositories/local_storage/abstract_draft_message_repository.dart';
import '../../repositories/local_storage/abstract_fcm_message_repository.dart';
import '../../repositories/local_storage/abstract_unread_count_repository.dart';
import '../../repositories/local_storage/abstract_user_settings_repository.dart';
import '../../repositories/local_storage/drift_chat_message_repository.dart';
import '../../repositories/local_storage/drift_chat_metadata_repository.dart';
import '../../repositories/local_storage/drift_draft_message_repository.dart';
import '../../repositories/local_storage/drift_fcm_message_repository.dart';
import '../../repositories/local_storage/drift_unread_count_repository.dart';
import '../../repositories/local_storage/drift_user_settings_repository.dart';
import '../../repositories/local_storage/local_storage_repository.dart';
import '../../statefulbusinesslogic/core/db/app_database.dart';
import '../../statefulbusinesslogic/core/services/profile_picture_service.dart';
import '../../statefulbusinesslogic/core/services/hopen_cache_manager.dart';
import '../../repositories/local_storage/abstract_profile_picture_repository.dart';
import '../../provider/repositories/local_storage/profile_picture_repository_impl.dart';
import '../../provider/notifiers/profile_picture_notifier.dart';
import '../../provider/repositories/user/cached_user_repository.dart';
import '../../provider/notifiers/user_profile_notifier.dart';
import '../../provider/services/user_profile_preloader_service.dart';
import '../../provider/services/webrtc/mqtt_webrtc_signaling_service.dart';
import '../../provider/services/mqtt/mqtt_service.dart';
import '../../repositories/user/user_repository.dart';

/// Database and local storage module
/// Registers Drift database, DAOs, and local storage repositories
void registerDatabaseModule(GetIt sl) {
  // Core Database
  sl.registerLazySingleton<AppDatabase>(AppDatabase.new);

  // DAOs
  sl.registerLazySingleton<ChatMessageDao>(
    () => sl<AppDatabase>().chatMessageDao,
  );
  sl.registerLazySingleton<UserSettingDao>(
    () => sl<AppDatabase>().userSettingDao,
  );
  sl.registerLazySingleton<ChatMetadataDao>(
    () => sl<AppDatabase>().chatMetadataDao,
  );
  sl.registerLazySingleton<UnreadCountDao>(
    () => sl<AppDatabase>().unreadCountDao,
  );
  sl.registerLazySingleton<DraftMessageDao>(
    () => sl<AppDatabase>().draftMessageDao,
  );
  sl.registerLazySingleton<FcmMessageDao>(
    () => sl<AppDatabase>().fcmMessageDao,
  );
  sl.registerLazySingleton<ProfilePictureDao>(
    () => sl<AppDatabase>().profilePictureDao,
  );
  sl.registerLazySingleton<UserProfileDao>(
    () => sl<AppDatabase>().userProfileDao,
  );

  // Abstract Repository Implementations
  sl.registerLazySingleton<AbstractChatMessageRepository>(
    () => DriftChatMessageRepository(sl()),
  );
  sl.registerLazySingleton<AbstractUnreadCountRepository>(
    () => DriftUnreadCountRepository(unreadCountDao: sl()),
  );
  sl.registerLazySingleton<AbstractDraftMessageRepository>(
    () => DriftDraftMessageRepository(draftMessageDao: sl()),
  );
  sl.registerLazySingleton<AbstractFcmMessageRepository>(
    () => DriftFcmMessageRepository(fcmMessageDao: sl()),
  );
  sl.registerLazySingleton<AbstractChatMetadataRepository>(
    () => DriftChatMetadataRepository(chatMetadataDao: sl()),
  );
  sl.registerLazySingleton<AbstractUserSettingsRepository>(
    () => DriftUserSettingsRepository(userSettingDao: sl()),
  );

  // Local Storage Repository
  sl.registerLazySingleton<LocalStorageRepository>(
    () => LocalStorageRepositoryImpl(localStorageService: sl()),
  );

  // Profile Picture Service (Single Source of Truth)
  sl.registerLazySingleton<ProfilePictureService>(
    () => ProfilePictureService(
      dao: sl<ProfilePictureDao>(),
      cacheManager: HopenCacheManager(),
    ),
  );

  // Profile Picture Repository
  sl.registerLazySingleton<AbstractProfilePictureRepository>(
    () => ProfilePictureRepositoryImpl(
      profilePictureService: sl<ProfilePictureService>(),
    ),
  );

  // Profile Picture Notifier (State Management)
  sl.registerLazySingleton<ProfilePictureNotifier>(
    () => ProfilePictureNotifier(sl<AbstractProfilePictureRepository>()),
  );

  // Cached User Repository (Stale-While-Revalidate)
  sl.registerLazySingleton<CachedUserRepository>(
    () => CachedUserRepository(
      networkRepository: sl<UserRepository>(),
      dao: sl<UserProfileDao>(),
    ),
  );

  // User Profile Notifier (State Management)
  sl.registerLazySingleton<UserProfileNotifier>(
    () => UserProfileNotifier(sl<CachedUserRepository>()),
  );

  // User Profile Preloader Service
  sl.registerLazySingleton<UserProfilePreloaderService>(
    () => UserProfilePreloaderService.instance,
  );

  // MQTT WebRTC Signaling Service (depends on MqttService from chat module)
  sl.registerLazySingleton<MqttWebRtcSignalingService>(
    () => MqttWebRtcSignalingService(sl<MqttService>()),
  );
}
