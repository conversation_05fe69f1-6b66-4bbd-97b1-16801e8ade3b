import 'package:get_it/get_it.dart';
import '../../provider/repositories/chat/chat_repository_impl.dart';
import '../../provider/repositories/messaging/message_repository_impl.dart';
import '../../provider/services/messaging/messaging_service.dart';
import '../../provider/services/mqtt/mqtt_service.dart';
import '../../repositories/chat/chat_repository.dart';
import '../../repositories/messaging/message_repository.dart';
import '../../statefulbusinesslogic/bloc/chat/chat_bloc.dart';
import '../../statefulbusinesslogic/core/usecases/get_messages_usecase.dart';
import '../../statefulbusinesslogic/core/usecases/send_message_usecase.dart';

/// Chat and messaging module
/// Registers MQTT, messaging services, chat repositories and BLoCs
void registerChatModule(GetIt sl) {
  // MQTT Service
  sl.registerLazySingleton<MqttService>(() => MqttServiceImpl());

  // Messaging Services
  sl.registerLazySingleton<MessagingService>(
    () => MessagingService(
      apiService: sl(),
      mqttService: sl(),
      storageService: sl(),
      fcmService: sl(),
    ),
  );

  // Repositories
  sl.registerLazySingleton<ChatRepository>(
    () => ChatRepositoryImpl(
      mqttService: sl(),
      httpDataSource: sl(),
      mediaUploadService: sl(),
      authRepository: sl(),
      audioRecordingRepository: sl(),
      localMessageRepository: sl(),
    ),
  );
  sl.registerLazySingleton<MessageRepository>(
    () => MessageRepositoryImpl(mqttService: sl(), httpDataSource: sl()),
  );

  // Use Cases
  sl.registerLazySingleton<SendMessageUseCase>(() => SendMessageUseCase(sl()));
  sl.registerLazySingleton<GetMessagesUseCase>(() => GetMessagesUseCase(sl()));

  // BLoCs
  sl.registerFactory(
    () => ChatBloc(chatRepository: sl(), authRepository: sl()),
  );
}
