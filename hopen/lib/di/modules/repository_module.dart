import '../../provider/repositories/audio_recording/audio_recording_repository_impl.dart';
import '../../provider/repositories/sync/sync_repository_impl.dart';
import '../../provider/services/api/http_api_service.dart';
import '../../provider/services/mqtt_only_real_time_service.dart';
import '../../repositories/audio_recording/audio_recording_repository.dart';
import '../../repositories/sync/sync_repository.dart';
import '../../statefulbusinesslogic/core/db/app_database.dart';
import '../injection_container_refactored.dart';

// ... existing code ...

void initializeRepositoryModule() {
  // ... existing registrations ...

  // Register AudioRecordingRepository
  if (!sl.isRegistered<AudioRecordingRepository>()) {
    sl.registerLazySingleton<AudioRecordingRepository>(
      AudioRecordingRepositoryImpl.new,
    );
  }

  // Register SyncRepository
  if (!sl.isRegistered<SyncRepository>()) {
    sl.registerLazySingleton<SyncRepository>(
      () => SyncRepositoryImpl(
        apiService: sl<HttpApiService>(),
        mqttService: sl<MqttOnlyRealTimeService>(),
        database: sl<AppDatabase>(),
      ),
    );
  }

  // ... existing code ...
}
