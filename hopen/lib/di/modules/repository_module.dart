import '../../repositories/audio_recording/audio_recording_repository.dart';
import '../../provider/repositories/audio_recording/audio_recording_repository_impl.dart';
import '../../repositories/sync/sync_repository.dart';
import '../../provider/repositories/sync/sync_repository_impl.dart';
import '../injection_container_refactored.dart';

// ... existing code ...

void initializeRepositoryModule() {
  // ... existing registrations ...

  // Register AudioRecordingRepository
  if (!sl.isRegistered<AudioRecordingRepository>()) {
    sl.registerLazySingleton<AudioRecordingRepository>(
      () => AudioRecordingRepositoryImpl(),
    );
  }

  // ... existing code ...
}
