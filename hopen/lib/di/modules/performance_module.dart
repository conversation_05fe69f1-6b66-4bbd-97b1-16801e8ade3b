import 'package:get_it/get_it.dart';
import '../../provider/services/ui_optimization_service.dart';
import '../../provider/services/network_optimization_service.dart';
import '../../provider/services/cache_invalidation_service.dart';
import '../../provider/services/cache_monitoring_service.dart';

/// Performance module for registering performance-related services
class PerformanceModule {
  static Future<void> register(GetIt sl) async {
    // Register UI optimization service
    sl.registerLazySingletonAsync<UIOptimizationService>(() async {
      final service = UIOptimizationService();
      await service.initialize();
      return service;
    });

    // Register network optimization service
    sl.registerLazySingletonAsync<NetworkOptimizationService>(() async {
      final service = NetworkOptimizationService();
      await service.initialize();
      return service;
    });

    // Register cache invalidation service
    sl.registerLazySingletonAsync<CacheInvalidationService>(() async {
      final service = CacheInvalidationService();
      await service.initialize();
      return service;
    });

    // Register cache monitoring service
    sl.registerLazySingletonAsync<CacheMonitoringService>(() async {
      final service = CacheMonitoringService();
      await service.initialize();
      return service;
    });
  }
}
