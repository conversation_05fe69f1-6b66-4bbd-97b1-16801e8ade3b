/// Sync BLoC for HTTP/3-First Hybrid Sync Architecture
/// 
/// Manages the state of initial data synchronization operations.
/// This BLoC follows the four-layer dependency rule and handles:
/// - HTTP/3-first bulk data synchronization
/// - MQTT real-time connection establishment
/// - Sync progress tracking and metrics
/// - Error handling and retry logic

import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../repositories/sync/sync_repository.dart';
import '../../core/models/sync_models.dart';
import '../../core/services/logging_service.dart';

part 'sync_event.dart';
part 'sync_state.dart';

/// BLoC for managing sync operations and state
class SyncBloc extends Bloc<SyncEvent, SyncState> {
  SyncBloc({
    required SyncRepository syncRepository,
  })  : _syncRepository = syncRepository,
        super(const SyncInitial()) {
    on<StartSyncEvent>(_onStartSync);
    on<CancelSyncEvent>(_onCancelSync);
    on<RetrySync>(_onRetrySync);
    on<_SyncStatusChanged>(_onSyncStatusChanged);

    // Listen to sync status changes
    _statusSubscription = _syncRepository.statusStream.listen(
      (status) => add(_SyncStatusChanged(status)),
    );
  }

  final SyncRepository _syncRepository;
  StreamSubscription<SyncStatus>? _statusSubscription;

  /// Start the initial sync process
  Future<void> _onStartSync(
    StartSyncEvent event,
    Emitter<SyncState> emit,
  ) async {
    if (_syncRepository.isSyncing) {
      LoggingService.warning('SyncBloc: Sync already in progress, ignoring start request');
      return;
    }

    LoggingService.info('SyncBloc: Starting initial sync');
    
    emit(const SyncInProgress(
      status: SyncStatus.initializing,
      message: 'Initializing sync...',
    ));

    try {
      final result = await _syncRepository.performInitialSync(event.config);
      
      if (result.isSuccess) {
        final syncResult = result.data!;
        
        LoggingService.info(
          'SyncBloc: Sync completed successfully in ${syncResult.syncDurationMs}ms using ${syncResult.protocolUsed}',
        );
        
        emit(SyncSuccess(
          result: syncResult,
          message: _buildSuccessMessage(syncResult),
        ));
      } else {
        LoggingService.error('SyncBloc: Sync failed: ${result.error}');
        
        emit(SyncFailure(
          error: result.error ?? 'Unknown sync error',
          canRetry: true,
          lastAttemptTime: DateTime.now(),
        ));
      }
    } catch (e, stackTrace) {
      LoggingService.error('SyncBloc: Unexpected sync error: $e');
      LoggingService.error('Stack trace: $stackTrace');
      
      emit(SyncFailure(
        error: 'Unexpected error during sync: $e',
        canRetry: true,
        lastAttemptTime: DateTime.now(),
      ));
    }
  }

  /// Cancel ongoing sync operation
  Future<void> _onCancelSync(
    CancelSyncEvent event,
    Emitter<SyncState> emit,
  ) async {
    LoggingService.info('SyncBloc: Cancelling sync operation');
    
    final cancelled = await _syncRepository.cancelSync();
    
    if (cancelled) {
      emit(const SyncCancelled(
        message: 'Sync operation was cancelled',
      ));
    } else {
      LoggingService.warning('SyncBloc: No sync operation to cancel');
    }
  }

  /// Retry failed sync operation
  Future<void> _onRetrySync(
    RetrySync event,
    Emitter<SyncState> emit,
  ) async {
    LoggingService.info('SyncBloc: Retrying sync operation');
    
    // Restart the sync process
    add(StartSyncEvent(config: event.config));
  }

  /// Handle sync status changes from repository
  void _onSyncStatusChanged(
    _SyncStatusChanged event,
    Emitter<SyncState> emit,
  ) {
    final status = event.status;
    
    if (status.isInProgress && state is! SyncInProgress) {
      emit(SyncInProgress(
        status: status,
        message: _getStatusMessage(status),
        metrics: _syncRepository.lastSyncMetrics,
      ));
    }
  }

  /// Build success message based on sync result
  String _buildSuccessMessage(SyncResult result) {
    final protocol = result.usedHttp3 ? 'HTTP/3' : result.protocolUsed;
    final performance = result.performance.emoji;
    
    return 'Sync completed $performance in ${result.syncDurationMs}ms using $protocol';
  }

  /// Get user-friendly message for sync status
  String _getStatusMessage(SyncStatus status) {
    switch (status) {
      case SyncStatus.initializing:
        return 'Preparing to sync...';
      case SyncStatus.fetchingData:
        return 'Fetching your data...';
      case SyncStatus.connectingMqtt:
        return 'Connecting to real-time updates...';
      case SyncStatus.processingData:
        return 'Processing and storing data...';
      case SyncStatus.completed:
        return 'Sync completed successfully!';
      case SyncStatus.failed:
        return 'Sync failed';
      case SyncStatus.notStarted:
        return 'Ready to sync';
    }
  }

  @override
  Future<void> close() {
    _statusSubscription?.cancel();
    return super.close();
  }
}
