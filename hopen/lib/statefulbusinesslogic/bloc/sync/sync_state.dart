part of 'sync_bloc.dart';

/// Base class for all sync states
abstract class SyncState extends Equatable {
  const SyncState();

  @override
  List<Object?> get props => [];
}

/// Initial state before any sync operation
class SyncInitial extends SyncState {
  const SyncInitial();

  @override
  String toString() => 'SyncInitial()';
}

/// State when sync operation is in progress
class SyncInProgress extends SyncState {
  const SyncInProgress({
    required this.status,
    required this.message,
    this.metrics,
    this.progress,
  });

  /// Current sync status
  final SyncStatus status;

  /// User-friendly message describing current operation
  final String message;

  /// Optional metrics from ongoing sync
  final SyncMetrics? metrics;

  /// Optional progress percentage (0.0 to 1.0)
  final double? progress;

  @override
  List<Object?> get props => [status, message, metrics, progress];

  @override
  String toString() => 'SyncInProgress(status: $status, message: $message)';

  /// Create a copy with updated values
  SyncInProgress copyWith({
    SyncStatus? status,
    String? message,
    SyncMetrics? metrics,
    double? progress,
  }) {
    return SyncInProgress(
      status: status ?? this.status,
      message: message ?? this.message,
      metrics: metrics ?? this.metrics,
      progress: progress ?? this.progress,
    );
  }
}

/// State when sync operation completed successfully
class SyncSuccess extends SyncState {
  const SyncSuccess({
    required this.result,
    required this.message,
  });

  /// Complete sync result with metrics
  final SyncResult result;

  /// Success message for user display
  final String message;

  @override
  List<Object?> get props => [result, message];

  @override
  String toString() => 'SyncSuccess(result: $result, message: $message)';
}

/// State when sync operation failed
class SyncFailure extends SyncState {
  const SyncFailure({
    required this.error,
    required this.canRetry,
    required this.lastAttemptTime,
    this.retryCount = 0,
    this.metrics,
  });

  /// Error message describing the failure
  final String error;

  /// Whether the operation can be retried
  final bool canRetry;

  /// Timestamp of the last attempt
  final DateTime lastAttemptTime;

  /// Number of retry attempts made
  final int retryCount;

  /// Optional metrics from failed sync attempt
  final SyncMetrics? metrics;

  @override
  List<Object?> get props => [error, canRetry, lastAttemptTime, retryCount, metrics];

  @override
  String toString() => 'SyncFailure(error: $error, canRetry: $canRetry, retryCount: $retryCount)';

  /// Create a copy with updated values
  SyncFailure copyWith({
    String? error,
    bool? canRetry,
    DateTime? lastAttemptTime,
    int? retryCount,
    SyncMetrics? metrics,
  }) {
    return SyncFailure(
      error: error ?? this.error,
      canRetry: canRetry ?? this.canRetry,
      lastAttemptTime: lastAttemptTime ?? this.lastAttemptTime,
      retryCount: retryCount ?? this.retryCount,
      metrics: metrics ?? this.metrics,
    );
  }
}

/// State when sync operation was cancelled
class SyncCancelled extends SyncState {
  const SyncCancelled({
    required this.message,
  });

  /// Message describing the cancellation
  final String message;

  @override
  List<Object?> get props => [message];

  @override
  String toString() => 'SyncCancelled(message: $message)';
}
