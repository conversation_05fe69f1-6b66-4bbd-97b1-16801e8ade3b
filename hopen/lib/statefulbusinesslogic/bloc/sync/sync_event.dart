part of 'sync_bloc.dart';

/// Base class for all sync events
abstract class SyncEvent extends Equatable {
  const SyncEvent();

  @override
  List<Object?> get props => [];
}

/// Event to start the initial sync process
class StartSyncEvent extends SyncEvent {
  const StartSyncEvent({this.config});

  /// Optional configuration for sync behavior
  final SyncConfig? config;

  @override
  List<Object?> get props => [config];

  @override
  String toString() => 'StartSyncEvent(config: $config)';
}

/// Event to cancel ongoing sync operation
class CancelSyncEvent extends SyncEvent {
  const CancelSyncEvent();

  @override
  String toString() => 'CancelSyncEvent()';
}

/// Event to retry a failed sync operation
class RetrySync extends SyncEvent {
  const RetrySync({this.config});

  /// Optional configuration for retry attempt
  final SyncConfig? config;

  @override
  List<Object?> get props => [config];

  @override
  String toString() => 'RetrySync(config: $config)';
}

/// Internal event for sync status changes
class _SyncStatusChanged extends SyncEvent {
  const _SyncStatusChanged(this.status);

  final SyncStatus status;

  @override
  List<Object?> get props => [status];

  @override
  String toString() => '_SyncStatusChanged(status: $status)';
}
