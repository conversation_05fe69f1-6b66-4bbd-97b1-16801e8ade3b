import 'package:flutter/foundation.dart';
import '../models/user_model.dart';

/// Abstract interface for user profile state management
/// 
/// This interface allows the presentation layer to depend on an abstraction
/// rather than a concrete implementation, following the dependency inversion principle
abstract class UserProfileNotifierInterface extends ChangeNotifier {
  /// Current user data (null if not loaded)
  UserModel? get currentUser;

  /// Whether the notifier is currently loading data
  bool get isLoading;

  /// Current error message (null if no error)
  String? get error;

  /// Initialize with user ID (typically called after login)
  Future<void> initializeUser(String userId);

  /// Refresh user profile data
  Future<void> refreshUser();

  /// Clear current user data
  void clearUser();

  /// Update user profile data
  Future<void> updateUser(UserModel user);

  /// Dispose resources
  @override
  void dispose();
}
