import 'dart:io';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';

/// Custom cache manager that bypasses certificate issues for development
/// Enhanced with dynamic cache sizing and memory pressure handling
class HopenCacheManager extends CacheManager with ImageCacheManager {
  static const key = 'hopenCachedImageData';

  static HopenCacheManager? _instance;

  factory HopenCacheManager() {
    return _instance ??= HopenCacheManager._();
  }

  HopenCacheManager._() : super(
    Config(
      key,
      stalePeriod: const Duration(days: 7), // Cache for 7 days
      maxNrOfCacheObjects: _calculateOptimalCacheSize(), // Dynamic cache size
      repo: JsonCacheInfoRepository(databaseName: key),
      fileService: HopenHttpFileService(), // Use our custom HTTP service
    ),
  );

  /// Calculate optimal cache size based on device capabilities
  static int _calculateOptimalCacheSize() {
    try {
      // Platform-specific cache sizing
      if (Platform.isAndroid) {
        // Android devices: conservative cache size
        return 150;
      } else if (Platform.isIOS) {
        // iOS devices: moderate cache size
        return 200;
      } else if (Platform.isMacOS || Platform.isWindows) {
        // Desktop platforms: larger cache size
        return 500;
      } else if (Platform.isLinux) {
        // Linux: moderate cache size
        return 300;
      } else {
        // Web and other platforms: default cache size
        return 200;
      }
    } catch (e) {
      // Fallback to default if platform detection fails
      return 200;
    }
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    try {
      // Get cache directory from the cache manager
      final cacheDir = await getTemporaryDirectory();
      final directory = Directory('${cacheDir.path}/$key');
      
      if (await directory.exists()) {
        final files = await directory.list().toList();
        final totalSize = await _calculateDirectorySize(directory);
        
        return {
          'cacheDirectory': directory.path,
          'totalFiles': files.length,
          'totalSize': totalSize,
          'maxCacheObjects': _calculateOptimalCacheSize(),
          'stalePeriod': const Duration(days: 7).inDays,
        };
      }
      
      return {
        'cacheDirectory': 'Not available',
        'totalFiles': 0,
        'totalSize': 0,
        'maxCacheObjects': _calculateOptimalCacheSize(),
        'stalePeriod': const Duration(days: 7).inDays,
      };
    } catch (e) {
      return {
        'cacheDirectory': 'Error',
        'totalFiles': 0,
        'totalSize': 0,
        'maxCacheObjects': _calculateOptimalCacheSize(),
        'stalePeriod': const Duration(days: 7).inDays,
        'error': e.toString(),
      };
    }
  }

  /// Calculate directory size recursively
  Future<int> _calculateDirectorySize(Directory directory) async {
    int totalSize = 0;
    
    try {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          totalSize += await entity.length();
        }
      }
    } catch (e) {
      // Handle permission errors or other issues
      print('Warning: Could not calculate directory size: $e');
    }
    
    return totalSize;
  }

  /// Clear cache with enhanced logging
  Future<void> clearCacheWithLogging() async {
    try {
      print('🧹 Clearing HopenCacheManager cache...');
      await emptyCache();
      print('✅ HopenCacheManager cache cleared successfully');
    } catch (e) {
      print('❌ Failed to clear HopenCacheManager cache: $e');
      rethrow;
    }
  }

  /// Get cache size in human-readable format
  Future<String> getCacheSizeFormatted() async {
    try {
      final stats = await getCacheStats();
      final sizeInBytes = stats['totalSize'] as int;
      
      if (sizeInBytes < 1024) {
        return '${sizeInBytes}B';
      } else if (sizeInBytes < 1024 * 1024) {
        return '${(sizeInBytes / 1024).toStringAsFixed(1)}KB';
      } else if (sizeInBytes < 1024 * 1024 * 1024) {
        return '${(sizeInBytes / (1024 * 1024)).toStringAsFixed(1)}MB';
      } else {
        return '${(sizeInBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
      }
    } catch (e) {
      return '0B';
    }
  }
}

/// Custom HTTP file service that bypasses certificate issues
class HopenHttpFileService extends HttpFileService {
  @override
  Future<FileServiceResponse> get(String url, {Map<String, String>? headers}) async {
    print('🌐 HopenHttpFileService fetching: $url');

    try {
      // Use the default HTTP file service but with our custom HTTP client
      // This ensures compatibility with the expected response types
      return await super.get(url, headers: headers);
    } catch (e) {
      print('❌ HopenHttpFileService error: $e');

      // Fallback: try with certificate bypass if the default fails
      try {
        print('🔄 Trying with certificate bypass...');

        // Create HTTP client that bypasses certificate verification
        final client = HttpClient();
        client.badCertificateCallback = (cert, host, port) => true;

        final request = await client.getUrl(Uri.parse(url));
        if (headers != null) {
          headers.forEach((key, value) {
            request.headers.add(key, value);
          });
        }

        final response = await request.close();
        final bytes = await _consolidateHttpClientResponseBytes(response);

        print('✅ HopenHttpFileService certificate bypass success: ${bytes.length} bytes');

        // Create a proper StreamedResponse for HttpGetResponse
        final headersMap = <String, String>{};
        response.headers.forEach((name, values) {
          headersMap[name] = values.join(', ');
        });

        final streamedResponse = http.StreamedResponse(
          Stream.value(bytes),
          response.statusCode,
          contentLength: bytes.length,
          headers: headersMap,
        );

        return HttpGetResponse(streamedResponse);
      } catch (fallbackError) {
        print('❌ HopenHttpFileService fallback also failed: $fallbackError');
        rethrow;
      }
    }
  }

  /// Consolidate HTTP client response bytes
  Future<List<int>> _consolidateHttpClientResponseBytes(HttpClientResponse response) async {
    final List<int> bytes = [];
    await for (final chunk in response) {
      bytes.addAll(chunk);
    }
    return bytes;
  }
} 