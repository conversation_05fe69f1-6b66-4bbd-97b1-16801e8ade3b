import 'dart:io';
import 'dart:async';
import 'package:path/path.dart' as path;
import '../db/app_database.dart';
import '../services/logging_service.dart';
import 'hopen_cache_manager.dart';

/// Enhanced profile picture service with persistent caching
/// Provides profile picture caching with Drift database and Flutter Cache Manager
class ProfilePictureService {
  final ProfilePictureDao _dao;
  final HopenCacheManager _cacheManager;

  ProfilePictureService({
    required ProfilePictureDao dao,
    required HopenCacheManager cacheManager,
  })  : _dao = dao,
        _cacheManager = cacheManager;

  /// Get cached profile picture path for a user
  /// Returns local file path if cached, null otherwise
  Future<String?> getProfilePicturePath(String userId, String imageUrl) async {
    try {
      // 1. Check Drift database first for existing cache
      final cached = await _dao.getProfilePicture(userId);
      if (cached != null && await File(cached.localPath).exists()) {
        LoggingService.debug('Found cached profile picture for user $userId: ${cached.localPath}');
        await _dao.updateLastAccessed(userId);
        return cached.localPath;
      }

      // 2. Download and cache if not found
      LoggingService.info('Downloading and caching profile picture for user $userId from $imageUrl');
      final file = await _cacheManager.getSingleFile(imageUrl);
      final metadata = await file.stat();
      
      // 3. Save to Drift database
      final picture = DriftProfilePicture(
        userId: userId,
        imageUrl: imageUrl,
        localPath: file.path,
        fileSize: metadata.size,
        format: path.extension(file.path).replaceAll('.', ''),
        cachedAt: DateTime.now(),
        lastAccessed: DateTime.now(),
        isActive: true,
      );
      
      await _dao.saveProfilePicture(picture);
      LoggingService.success('Successfully cached profile picture for user $userId: ${file.path}');
      return file.path;
    } catch (e) {
      LoggingService.error('Failed to cache profile picture for user $userId: $e');
      return null;
    }
  }

  /// Clear old cache entries based on age
  Future<void> clearOldCache(Duration age) async {
    try {
      LoggingService.info('Clearing profile picture cache entries older than ${age.inDays} days');
      
      final oldEntries = await _dao.getOldCacheEntries(age);
      int deletedFiles = 0;
      int deletedRecords = 0;
      
      for (final entry in oldEntries) {
        try {
          final file = File(entry.localPath);
          if (await file.exists()) {
            await file.delete();
            deletedFiles++;
          }
        } catch (e) {
          LoggingService.warning('Failed to delete file for user ${entry.userId}: $e');
        }
        
        try {
          await _dao.deleteProfilePicture(entry.userId);
          deletedRecords++;
        } catch (e) {
          LoggingService.warning('Failed to delete database record for user ${entry.userId}: $e');
        }
      }
      
      LoggingService.success('Cleared $deletedFiles files and $deletedRecords database records');
    } catch (e) {
      LoggingService.error('Failed to clear old cache: $e');
    }
  }

  /// Generate user initials for fallback avatar
  static String generateInitials(String? firstName, String? lastName) {
    final trimmedFirst = firstName?.trim() ?? '';
    final trimmedLast = lastName?.trim() ?? '';
    
    final first = trimmedFirst.isNotEmpty ? trimmedFirst[0].toUpperCase() : '';
    final last = trimmedLast.isNotEmpty ? trimmedLast[0].toUpperCase() : '';
    
    if (first.isEmpty && last.isEmpty) {
      return 'U'; // Default for User
    }
    
    return '$first$last';
  }

  /// Clear cache entries exceeding size limit
  Future<void> clearCacheBySize(int maxSizeBytes) async {
    try {
      LoggingService.info('Clearing profile picture cache to stay under ${maxSizeBytes ~/ (1024 * 1024)}MB');
      
      final currentSize = await _dao.getTotalCacheSize();
      if (currentSize <= maxSizeBytes) {
        LoggingService.info('Cache size ($currentSize bytes) is within limit ($maxSizeBytes bytes)');
        return;
      }
      
      // Get all entries sorted by last accessed (LRU)
      final allEntries = await _dao.getAllProfilePictures();
      allEntries.sort((a, b) => a.lastAccessed.compareTo(b.lastAccessed));
      
      int currentTotalSize = currentSize;
      int deletedFiles = 0;
      int deletedRecords = 0;
      
      for (final entry in allEntries) {
        if (currentTotalSize <= maxSizeBytes) break;
        
        try {
          final file = File(entry.localPath);
          if (await file.exists()) {
            await file.delete();
            deletedFiles++;
            currentTotalSize -= entry.fileSize;
          }
        } catch (e) {
          LoggingService.warning('Failed to delete file for user ${entry.userId}: $e');
        }
        
        try {
          await _dao.deleteProfilePicture(entry.userId);
          deletedRecords++;
        } catch (e) {
          LoggingService.warning('Failed to delete database record for user ${entry.userId}: $e');
        }
      }
      
      LoggingService.success('Cleared $deletedFiles files and $deletedRecords database records. New size: $currentTotalSize bytes');
    } catch (e) {
      LoggingService.error('Failed to clear cache by size: $e');
    }
  }

  /// Clear all cache entries
  Future<void> clearAllCache() async {
    try {
      LoggingService.info('Clearing all profile picture cache entries');
      
      // Delete all files first
      final pictures = await _dao.getAllProfilePictures();
      for (final entry in pictures) {
        try {
          final file = File(entry.localPath);
          if (await file.exists()) {
            await file.delete();
          }
        } catch (e) {
          LoggingService.warning('Failed to delete file for user ${entry.userId}: $e');
        }
      }
      
      // Clear database entries
      await _dao.clearAllCache();
      LoggingService.success('Successfully cleared all profile picture cache entries');
    } catch (e) {
      LoggingService.error('Failed to clear all cache: $e');
    }
  }

  /// Update the last accessed time for a profile picture
  Future<void> updateLastAccessed(String userId) async {
    try {
      await _dao.updateLastAccessed(userId);
    } catch (e) {
      LoggingService.warning('Failed to update last accessed time for user $userId: $e');
    }
  }

  /// Check if a profile picture is cached for a user
  Future<bool> isProfilePictureCached(String userId) async {
    try {
      final cached = await _dao.getProfilePicture(userId);
      if (cached != null) {
        return await File(cached.localPath).exists();
      }
      return false;
    } catch (e) {
      LoggingService.warning('Failed to check if profile picture is cached for user $userId: $e');
      return false;
    }
  }

  /// Get the cache entry for a user
  Future<DriftProfilePicture?> getCacheEntry(String userId) async {
    try {
      return await _dao.getProfilePicture(userId);
    } catch (e) {
      LoggingService.warning('Failed to get cache entry for user $userId: $e');
      return null;
    }
  }

  /// Get enhanced cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    try {
      final allEntries = await _dao.getAllProfilePictures();
      final totalSize = await _dao.getTotalCacheSize();
      
      // Calculate statistics
      final now = DateTime.now();
      final activeEntries = allEntries.where((e) => e.isActive).length;
      final inactiveEntries = allEntries.length - activeEntries;
      
      // Calculate age distribution
      final recentEntries = allEntries.where((e) => 
        now.difference(e.cachedAt).inDays < 1).length;
      final weekOldEntries = allEntries.where((e) => 
        now.difference(e.cachedAt).inDays >= 1 && 
        now.difference(e.cachedAt).inDays < 7).length;
      final oldEntries = allEntries.where((e) => 
        now.difference(e.cachedAt).inDays >= 7).length;
      
      // Calculate format distribution
      final formatCounts = <String, int>{};
      for (final entry in allEntries) {
        formatCounts[entry.format] = (formatCounts[entry.format] ?? 0) + 1;
      }
      
      // Calculate average file size
      final avgFileSize = allEntries.isNotEmpty 
          ? totalSize / allEntries.length 
          : 0;
      
      return {
        'totalEntries': allEntries.length,
        'activeEntries': activeEntries,
        'inactiveEntries': inactiveEntries,
        'totalSize': totalSize,
        'averageFileSize': avgFileSize,
        'recentEntries': recentEntries,
        'weekOldEntries': weekOldEntries,
        'oldEntries': oldEntries,
        'formatDistribution': formatCounts,
        'cacheEfficiency': _calculateCacheEfficiency(allEntries),
      };
    } catch (e) {
      LoggingService.error('Failed to get cache statistics: $e');
      return {};
    }
  }

  /// Calculate cache efficiency based on access patterns
  double _calculateCacheEfficiency(List<DriftProfilePicture> entries) {
    if (entries.isEmpty) return 0.0;
    
    final now = DateTime.now();
    int frequentlyAccessed = 0;
    
    for (final entry in entries) {
      final daysSinceLastAccess = now.difference(entry.lastAccessed).inDays;
      if (daysSinceLastAccess <= 1) {
        frequentlyAccessed++;
      }
    }
    
    final total = entries.length;
    return frequentlyAccessed / total;
  }

  /// Get cache size in human-readable format
  Future<String> getCacheSizeFormatted() async {
    try {
      final totalSize = await _dao.getTotalCacheSize();
      
      if (totalSize < 1024) {
        return '${totalSize}B';
      } else if (totalSize < 1024 * 1024) {
        return '${(totalSize / 1024).toStringAsFixed(1)}KB';
      } else if (totalSize < 1024 * 1024 * 1024) {
        return '${(totalSize / (1024 * 1024)).toStringAsFixed(1)}MB';
      } else {
        return '${(totalSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
      }
    } catch (e) {
      LoggingService.error('Failed to get formatted cache size: $e');
      return '0B';
    }
  }

  /// Optimize cache by removing rarely accessed entries
  Future<void> optimizeCache({int maxEntries = 100}) async {
    try {
      LoggingService.info('Optimizing profile picture cache (max entries: $maxEntries)');
      
      final allEntries = await _dao.getAllProfilePictures();
      if (allEntries.length <= maxEntries) {
        LoggingService.info('Cache size (${allEntries.length}) is within limit ($maxEntries)');
        return;
      }
      
      // Sort by last accessed (LRU)
      allEntries.sort((a, b) => a.lastAccessed.compareTo(b.lastAccessed));
      
      // Remove oldest entries
      final entriesToRemove = allEntries.take(allEntries.length - maxEntries);
      int deletedFiles = 0;
      int deletedRecords = 0;
      
      for (final entry in entriesToRemove) {
        try {
          final file = File(entry.localPath);
          if (await file.exists()) {
            await file.delete();
            deletedFiles++;
          }
        } catch (e) {
          LoggingService.warning('Failed to delete file for user ${entry.userId}: $e');
        }
        
        try {
          await _dao.deleteProfilePicture(entry.userId);
          deletedRecords++;
        } catch (e) {
          LoggingService.warning('Failed to delete database record for user ${entry.userId}: $e');
        }
      }
      
      LoggingService.success('Cache optimization completed: removed $deletedFiles files and $deletedRecords database records');
    } catch (e) {
      LoggingService.error('Failed to optimize cache: $e');
    }
  }

  /// Handle memory pressure by clearing old cache entries
  Future<void> handleMemoryPressure() async {
    try {
      LoggingService.info('Handling memory pressure - clearing old profile picture cache entries');
      
      // Clear entries older than 3 days
      await clearOldCache(const Duration(days: 3));
      
      // Optimize cache to keep only 50 most recent entries
      await optimizeCache(maxEntries: 50);
      
      LoggingService.success('Memory pressure handled for profile picture cache');
    } catch (e) {
      LoggingService.error('Failed to handle memory pressure: $e');
    }
  }

  /// Preload profile pictures for given user IDs
  Future<void> preloadProfilePictures(List<String> userIds, List<String> imageUrls) async {
    try {
      LoggingService.info('Preloading profile pictures for ${userIds.length} users');
      
      final futures = <Future>[];
      for (int i = 0; i < userIds.length && i < imageUrls.length; i++) {
        futures.add(getProfilePicturePath(userIds[i], imageUrls[i]));
      }
      
      await Future.wait(futures);
      LoggingService.success('Successfully preloaded ${futures.length} profile pictures');
    } catch (e) {
      LoggingService.error('Failed to preload profile pictures: $e');
    }
  }
}
