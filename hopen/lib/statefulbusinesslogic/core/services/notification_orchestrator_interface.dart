import 'dart:async';
import '../models/notification_model.dart';

/// Interface for orchestrating notification operations across the application
/// 
/// This service provides a clean abstraction for the provider layer to trigger
/// notification-related operations without directly depending on BLoCs or
/// business logic implementations.
abstract class NotificationOrchestratorInterface {
  /// Process incoming notification data and route appropriately
  /// 
  /// This method should be called when MQTT events or other real-time
  /// notifications are received to process and store them.
  Future<void> processNotification(Map<String, dynamic> data);
  
  /// Get all notifications for user
  Future<List<Notification>> getNotifications();
  
  /// Get unread notifications count
  Future<int> getUnreadCount();
  
  /// Mark notification as read
  Future<void> markAsRead(String notificationId);
  
  /// Mark all notifications as read
  Future<void> markAllAsRead();
  
  /// Delete notification
  Future<void> deleteNotification(String notificationId);
  
  /// Clear all notifications
  Future<void> clearAllNotifications();
  
  /// Get notifications stream for real-time updates
  Stream<List<Notification>> notificationsStream();
  
  /// Get unread count stream for real-time updates
  Stream<int> unreadCountStream();
}
