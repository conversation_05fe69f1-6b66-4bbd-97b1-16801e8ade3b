import 'dart:async';
import 'dart:io';

import '../../../provider/exceptions/network_exception.dart';
import '../../../provider/exceptions/storage_exception.dart';
import '../services/logging_service.dart';
import 'result.dart';

/// Standardized error handling utility for consistent error management
/// across the application. Eliminates repetitive error handling patterns.
class ErrorHandler {
  const ErrorHandler._();

  /// Execute an operation with standardized error handling
  static Future<Result<T>> execute<T>(
    Future<T> Function() operation, {
    String? context,
    Map<String, dynamic>? metadata,
    bool logErrors = true,
  }) async {
    try {
      final result = await operation();
      return Result.success(result);
    } catch (error, stackTrace) {
      return _handleError<T>(
        error,
        stackTrace,
        context: context,
        metadata: metadata,
        logErrors: logErrors,
      );
    }
  }

  /// Execute a synchronous operation with standardized error handling
  static Result<T> executeSync<T>(
    T Function() operation, {
    String? context,
    Map<String, dynamic>? metadata,
    bool logErrors = true,
  }) {
    try {
      final result = operation();
      return Result.success(result);
    } catch (error, stackTrace) {
      return _handleError<T>(
        error,
        stackTrace,
        context: context,
        metadata: metadata,
        logErrors: logErrors,
      );
    }
  }

  /// Execute an operation with retry logic and error handling
  static Future<Result<T>> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
    String? context,
    Map<String, dynamic>? metadata,
    bool logErrors = true,
  }) async {
    int attempts = 0;
    
    while (attempts <= maxRetries) {
      try {
        final result = await operation();
        return Result.success(result);
      } catch (error, stackTrace) {
        attempts++;
        
        if (attempts > maxRetries || !_isRetryableError(error)) {
          return _handleError<T>(
            error,
            stackTrace,
            context: context,
            metadata: metadata,
            logErrors: logErrors,
          );
        }
        
        if (logErrors) {
          LoggingService.warning(
            'Operation failed (attempt $attempts/$maxRetries), retrying in ${delay.inMilliseconds}ms',
            context: context,
            metadata: {
              ...?metadata,
              'attempt': attempts,
              'maxRetries': maxRetries,
              'error': error.toString(),
            },
          );
        }
        
        await Future.delayed(delay);
      }
    }
    
    return Result.failure(
      UnknownError(message: 'Maximum retry attempts exceeded'),
    );
  }

  /// Execute multiple operations concurrently with error handling
  static Future<Result<List<T>>> executeConcurrently<T>(
    List<Future<T> Function()> operations, {
    String? context,
    Map<String, dynamic>? metadata,
    bool logErrors = true,
    bool failFast = false,
  }) async {
    try {
      if (failFast) {
        final results = await Future.wait(
          operations.map((op) => op()),
        );
        return Result.success(results);
      } else {
        final results = await Future.wait(
          operations.map((op) => op().catchError((e) => null)),
        );
        
        final validResults = results.where((r) => r != null).cast<T>().toList();
        
        if (validResults.length != operations.length && logErrors) {
          LoggingService.warning(
            'Some concurrent operations failed',
            context: context,
            metadata: {
              ...?metadata,
              'totalOperations': operations.length,
              'successfulOperations': validResults.length,
              'failedOperations': operations.length - validResults.length,
            },
          );
        }
        
        return Result.success(validResults);
      }
    } catch (error, stackTrace) {
      return _handleError<List<T>>(
        error,
        stackTrace,
        context: context,
        metadata: metadata,
        logErrors: logErrors,
      );
    }
  }

  /// Execute an operation with timeout and error handling
  static Future<Result<T>> executeWithTimeout<T>(
    Future<T> Function() operation, {
    Duration timeout = const Duration(seconds: 30),
    String? context,
    Map<String, dynamic>? metadata,
    bool logErrors = true,
  }) async {
    try {
      final result = await operation().timeout(timeout);
      return Result.success(result);
    } catch (error, stackTrace) {
      return _handleError<T>(
        error,
        stackTrace,
        context: context,
        metadata: metadata,
        logErrors: logErrors,
      );
    }
  }

  /// Handle errors with standardized categorization and logging
  static Result<T> _handleError<T>(
    dynamic error,
    StackTrace stackTrace, {
    String? context,
    Map<String, dynamic>? metadata,
    bool logErrors = true,
  }) {
    final categorizedError = _categorizeError(error);
    
    if (logErrors) {
      _logError(
        categorizedError,
        stackTrace,
        context: context,
        metadata: metadata,
      );
    }
    
    return Result.failure(categorizedError);
  }

  /// Categorize errors into standardized types
  static AppError _categorizeError(dynamic error) {
    if (error is AppError) {
      return error;
    }
    
    if (error is SocketException) {
      return NetworkError(
        message: 'Network connection failed',
        originalError: error,
      );
    }
    
    if (error is TimeoutException) {
      return NetworkError(
        message: 'Request timed out',
        originalError: error,
      );
    }
    
    if (error is HttpException) {
      return NetworkError(
        message: 'HTTP error: ${error.message}',
        originalError: error,
      );
    }
    
    if (error is FileSystemException) {
      return StorageException.processingFailed(
        reason: 'File system error: ${error.message}',
      );
    }
    
    if (error is FormatException) {
      return ValidationError(
        message: 'Invalid data format: ${error.message}',
        originalError: error,
      );
    }
    
    if (error is ArgumentError) {
      return ValidationError(
        message: 'Invalid argument: ${error.message}',
        originalError: error,
      );
    }
    
    return UnknownError(
      message: error.toString(),
      originalError: error,
    );
  }

  /// Log errors with appropriate severity levels
  static void _logError(
    AppError error,
    StackTrace stackTrace, {
    String? context,
    Map<String, dynamic>? metadata,
  }) {
    final errorMetadata = {
      'errorType': error.runtimeType.toString(),
      'errorCode': error.code,
      'timestamp': DateTime.now().toIso8601String(),
      ...?metadata,
    };
    
    switch (error.severity) {
      case ErrorSeverity.low:
        LoggingService.debug(
          error.message,
          context: context,
          metadata: errorMetadata,
        );
        break;
      case ErrorSeverity.medium:
        LoggingService.warning(
          error.message,
          context: context,
          metadata: errorMetadata,
        );
        break;
      case ErrorSeverity.high:
        LoggingService.error(
          error.message,
          error: error.originalError,
          stackTrace: stackTrace,
          context: context,
          metadata: errorMetadata,
        );
        break;
      case ErrorSeverity.critical:
        LoggingService.critical(
          error.message,
          error: error.originalError,
          stackTrace: stackTrace,
          context: context,
          metadata: errorMetadata,
        );
        break;
    }
  }

  /// Check if an error is retryable
  static bool _isRetryableError(dynamic error) {
    if (error is NetworkError) {
      return true;
    }
    
    if (error is SocketException) {
      return true;
    }
    
    if (error is TimeoutException) {
      return true;
    }
    
    if (error is HttpException) {
      // Retry on server errors (5xx) but not client errors (4xx)
      return error.message.contains('5');
    }
    
    return false;
  }
}

/// Base class for application errors
abstract class AppError {
  final String message;
  final String code;
  final ErrorSeverity severity;
  final dynamic originalError;

  const AppError({
    required this.message,
    required this.code,
    required this.severity,
    this.originalError,
  });

  @override
  String toString() => '$code: $message';
}

/// Error severity levels
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Network-related errors
class NetworkError extends AppError {
  const NetworkError({
    required String message,
    dynamic originalError,
  }) : super(
          message: message,
          code: 'NETWORK_ERROR',
          severity: ErrorSeverity.medium,
          originalError: originalError,
        );
}

/// Validation-related errors
class ValidationError extends AppError {
  const ValidationError({
    required String message,
    dynamic originalError,
  }) : super(
          message: message,
          code: 'VALIDATION_ERROR',
          severity: ErrorSeverity.low,
          originalError: originalError,
        );
}

/// Unknown or unexpected errors
class UnknownError extends AppError {
  const UnknownError({
    required String message,
    dynamic originalError,
  }) : super(
          message: message,
          code: 'UNKNOWN_ERROR',
          severity: ErrorSeverity.high,
          originalError: originalError,
        );
}
