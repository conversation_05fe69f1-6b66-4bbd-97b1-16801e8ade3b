/// Sync Repository Interface
/// 
/// Defines the contract for initial data synchronization using HTTP/3-first approach.
/// This repository handles the bulk data fetch that occurs after successful login.

import '../error/result.dart';
import '../models/sync_models.dart';

/// Repository interface for initial data synchronization
abstract class SyncRepository {
  /// Perform initial bulk data synchronization
  /// 
  /// This method:
  /// 1. Fetches all user data in a single HTTP/3 request
  /// 2. Establishes MQTT connection for real-time updates
  /// 3. Stores data in local Drift database
  /// 4. Returns sync result with performance metrics
  /// 
  /// Returns [Result<SyncResult>] containing sync status and metrics
  Future<Result<SyncResult>> performInitialSync();
}
