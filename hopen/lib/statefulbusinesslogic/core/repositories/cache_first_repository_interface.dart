/// Cache-First Repository Interface
/// 
/// This interface defines the contract for repositories that prioritize local cache
/// and only fetch from remote when triggered by MQTT events or initial load.
/// 
/// Following the four-layer dependency rule:
/// - Business Logic Layer defines this interface
/// - Repository Layer extends this interface for specific domains
/// - Provider Layer implements the concrete repositories
/// - Presentation Layer uses repositories through dependency injection

import '../events/mqtt_event_interface.dart';

/// Base interface for cache-first repositories
abstract class CacheFirstRepository {
  /// Initialize the repository (load initial data from cache)
  Future<void> initialize();
  
  /// Refresh data from remote source (triggered by MQTT events)
  Future<void> refreshFromRemote();
  
  /// Handle MQTT events that affect this repository's data
  Future<void> handleMqttEvent(MqttEvent event);
  
  /// Get the last time data was refreshed from remote
  DateTime? get lastRemoteRefresh;
  
  /// Check if repository has cached data
  bool get hasCachedData;
  
  /// Clear all cached data
  Future<void> clearCache();
}

/// Interface for repositories that handle real-time events
abstract class RealTimeEventRepository extends CacheFirstRepository {
  /// Stream of real-time events for this repository
  Stream<MqttEvent> get eventStream;
  
  /// Process a real-time event and update cache accordingly
  Future<void> processRealTimeEvent(MqttEvent event);
}

/// Cache strategy for different types of data
enum CacheStrategy {
  /// Always use cache, only refresh on MQTT events
  cacheFirst,
  
  /// Use cache but periodically refresh (for non-real-time data)
  cacheWithPeriodicRefresh,
  
  /// Always fetch fresh data (for critical real-time data)
  alwaysFresh,
}

/// Cache configuration for repositories
class CacheConfig {
  const CacheConfig({
    this.strategy = CacheStrategy.cacheFirst,
    this.maxAge = const Duration(hours: 1),
    this.refreshInterval = const Duration(minutes: 30),
    this.enableBackgroundRefresh = false,
  });

  final CacheStrategy strategy;
  final Duration maxAge;
  final Duration refreshInterval;
  final bool enableBackgroundRefresh;
}

/// Repository state for monitoring and debugging
enum RepositoryState {
  uninitialized,
  initializing,
  ready,
  refreshing,
  error,
}

/// Repository metrics for monitoring
class RepositoryMetrics {
  const RepositoryMetrics({
    required this.state,
    required this.lastRefresh,
    required this.cacheHitCount,
    required this.cacheMissCount,
    required this.remoteCallCount,
    required this.errorCount,
    this.lastError,
  });

  final RepositoryState state;
  final DateTime? lastRefresh;
  final int cacheHitCount;
  final int cacheMissCount;
  final int remoteCallCount;
  final int errorCount;
  final String? lastError;

  double get cacheHitRatio {
    final total = cacheHitCount + cacheMissCount;
    return total > 0 ? cacheHitCount / total : 0.0;
  }
}

/// Enhanced cache-first repository with monitoring
abstract class MonitoredCacheFirstRepository extends CacheFirstRepository {
  /// Get current repository metrics
  RepositoryMetrics get metrics;
  
  /// Stream of repository state changes
  Stream<RepositoryState> get stateStream;
  
  /// Enable/disable debug logging
  void setDebugMode(bool enabled);
}
