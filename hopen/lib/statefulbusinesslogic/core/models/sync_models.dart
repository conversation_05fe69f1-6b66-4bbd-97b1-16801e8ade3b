/// Sync Models for HTTP/3-First Hybrid Sync Architecture
/// 
/// These models represent the sync operation results and metrics
/// following the four-layer dependency rule (Business Logic Layer).

import 'package:equatable/equatable.dart';

/// Result of an initial data synchronization operation
class Sync<PERSON><PERSON><PERSON> extends Equatable {
  const SyncResult({
    required this.success,
    required this.syncTimestamp,
    required this.syncDurationMs,
    required this.protocolUsed,
    required this.mqttConnected,
    required this.dataTypes,
    this.errorMessage,
    this.totalRecords = 0,
    this.cacheHitRatio = 0.0,
  });

  /// Whether the sync operation was successful
  final bool success;

  /// Timestamp when sync was completed
  final DateTime syncTimestamp;

  /// Duration of sync operation in milliseconds
  final int syncDurationMs;

  /// HTTP protocol used (HTTP/3, HTTP/2, etc.)
  final String protocolUsed;

  /// Whether MQTT connection was established
  final bool mqttConnected;

  /// List of data types that were synchronized
  final List<String> dataTypes;

  /// Error message if sync failed
  final String? errorMessage;

  /// Total number of records synchronized
  final int totalRecords;

  /// Cache hit ratio for performance metrics
  final double cacheHitRatio;

  /// Performance category based on sync duration
  SyncPerformance get performance {
    if (syncDurationMs < 1000) return SyncPerformance.excellent;
    if (syncDurationMs < 3000) return SyncPerformance.good;
    if (syncDurationMs < 5000) return SyncPerformance.acceptable;
    return SyncPerformance.poor;
  }

  /// Whether HTTP/3 was successfully used
  bool get usedHttp3 => protocolUsed.contains('HTTP/3') || protocolUsed.contains('h3');

  /// Create a copy with updated values
  SyncResult copyWith({
    bool? success,
    DateTime? syncTimestamp,
    int? syncDurationMs,
    String? protocolUsed,
    bool? mqttConnected,
    List<String>? dataTypes,
    String? errorMessage,
    int? totalRecords,
    double? cacheHitRatio,
  }) {
    return SyncResult(
      success: success ?? this.success,
      syncTimestamp: syncTimestamp ?? this.syncTimestamp,
      syncDurationMs: syncDurationMs ?? this.syncDurationMs,
      protocolUsed: protocolUsed ?? this.protocolUsed,
      mqttConnected: mqttConnected ?? this.mqttConnected,
      dataTypes: dataTypes ?? this.dataTypes,
      errorMessage: errorMessage ?? this.errorMessage,
      totalRecords: totalRecords ?? this.totalRecords,
      cacheHitRatio: cacheHitRatio ?? this.cacheHitRatio,
    );
  }

  @override
  List<Object?> get props => [
        success,
        syncTimestamp,
        syncDurationMs,
        protocolUsed,
        mqttConnected,
        dataTypes,
        errorMessage,
        totalRecords,
        cacheHitRatio,
      ];

  @override
  String toString() {
    return 'SyncResult('
        'success: $success, '
        'duration: ${syncDurationMs}ms, '
        'protocol: $protocolUsed, '
        'mqtt: $mqttConnected, '
        'dataTypes: ${dataTypes.length}, '
        'records: $totalRecords'
        ')';
  }
}

/// Performance categories for sync operations
enum SyncPerformance {
  excellent, // < 1s
  good,      // 1-3s
  acceptable, // 3-5s
  poor,      // > 5s
}

/// Extension for SyncPerformance display
extension SyncPerformanceExtension on SyncPerformance {
  String get displayName {
    switch (this) {
      case SyncPerformance.excellent:
        return 'Excellent';
      case SyncPerformance.good:
        return 'Good';
      case SyncPerformance.acceptable:
        return 'Acceptable';
      case SyncPerformance.poor:
        return 'Poor';
    }
  }

  String get emoji {
    switch (this) {
      case SyncPerformance.excellent:
        return '🚀';
      case SyncPerformance.good:
        return '✅';
      case SyncPerformance.acceptable:
        return '⚠️';
      case SyncPerformance.poor:
        return '🐌';
    }
  }
}

/// Sync configuration options
class SyncConfig extends Equatable {
  const SyncConfig({
    this.timeoutMs = 30000,
    this.retryAttempts = 3,
    this.retryDelayMs = 1000,
    this.enableMetrics = true,
    this.forceHttp3 = false,
    this.clearCacheFirst = true,
    this.parallelMqttConnection = true,
  });

  /// Timeout for sync operation in milliseconds
  final int timeoutMs;

  /// Number of retry attempts on failure
  final int retryAttempts;

  /// Delay between retry attempts in milliseconds
  final int retryDelayMs;

  /// Whether to collect performance metrics
  final bool enableMetrics;

  /// Force HTTP/3 usage (fail if not available)
  final bool forceHttp3;

  /// Clear local cache before sync
  final bool clearCacheFirst;

  /// Connect to MQTT in parallel with HTTP sync
  final bool parallelMqttConnection;

  @override
  List<Object?> get props => [
        timeoutMs,
        retryAttempts,
        retryDelayMs,
        enableMetrics,
        forceHttp3,
        clearCacheFirst,
        parallelMqttConnection,
      ];
}

/// Sync metrics for monitoring and analytics
class SyncMetrics extends Equatable {
  const SyncMetrics({
    required this.startTime,
    required this.endTime,
    required this.httpRequestTime,
    required this.mqttConnectionTime,
    required this.databaseTransactionTime,
    required this.totalDataSize,
    required this.protocolNegotiationTime,
    this.networkLatency,
    this.serverProcessingTime,
  });

  final DateTime startTime;
  final DateTime endTime;
  final Duration httpRequestTime;
  final Duration mqttConnectionTime;
  final Duration databaseTransactionTime;
  final int totalDataSize; // in bytes
  final Duration protocolNegotiationTime;
  final Duration? networkLatency;
  final Duration? serverProcessingTime;

  /// Total sync duration
  Duration get totalDuration => endTime.difference(startTime);

  /// Data transfer rate in bytes per second
  double get transferRate {
    final durationSeconds = httpRequestTime.inMilliseconds / 1000.0;
    return durationSeconds > 0 ? totalDataSize / durationSeconds : 0.0;
  }

  /// Breakdown of time spent in each phase
  Map<String, Duration> get timeBreakdown => {
        'HTTP Request': httpRequestTime,
        'MQTT Connection': mqttConnectionTime,
        'Database Transaction': databaseTransactionTime,
        'Protocol Negotiation': protocolNegotiationTime,
      };

  @override
  List<Object?> get props => [
        startTime,
        endTime,
        httpRequestTime,
        mqttConnectionTime,
        databaseTransactionTime,
        totalDataSize,
        protocolNegotiationTime,
        networkLatency,
        serverProcessingTime,
      ];
}

/// Sync status for real-time updates
enum SyncStatus {
  notStarted,
  initializing,
  fetchingData,
  connectingMqtt,
  processingData,
  completed,
  failed,
}

/// Extension for SyncStatus display
extension SyncStatusExtension on SyncStatus {
  String get displayName {
    switch (this) {
      case SyncStatus.notStarted:
        return 'Not Started';
      case SyncStatus.initializing:
        return 'Initializing';
      case SyncStatus.fetchingData:
        return 'Fetching Data';
      case SyncStatus.connectingMqtt:
        return 'Connecting to Real-time';
      case SyncStatus.processingData:
        return 'Processing Data';
      case SyncStatus.completed:
        return 'Completed';
      case SyncStatus.failed:
        return 'Failed';
    }
  }

  bool get isInProgress {
    return this == SyncStatus.initializing ||
        this == SyncStatus.fetchingData ||
        this == SyncStatus.connectingMqtt ||
        this == SyncStatus.processingData;
  }

  bool get isCompleted => this == SyncStatus.completed;
  bool get isFailed => this == SyncStatus.failed;
}
