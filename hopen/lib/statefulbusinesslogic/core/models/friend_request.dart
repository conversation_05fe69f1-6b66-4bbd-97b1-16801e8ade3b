import 'package:equatable/equatable.dart';

/// Represents an auto-generated friend request from bubble expiry
class FriendRequest extends Equatable {
  const FriendRequest({
    required this.id,
    required this.requesterId,
    required this.recipientId,
    required this.status,
    required this.createdAt,
    required this.sourceBubbleId,
    required this.autoGenerated,
    this.requesterName,
    this.requesterUsername,
    this.requesterProfilePicUrl,
    this.bubbleName,
  });

  final String id;
  final String requesterId;
  final String recipientId;
  final String status; // pending, accepted, declined
  final DateTime createdAt;
  final String sourceBubbleId;
  final bool autoGenerated;
  
  // Optional requester details (populated by backend)
  final String? requesterName;
  final String? requesterUsername;
  final String? requesterProfilePicUrl;
  final String? bubbleName;

  factory FriendRequest.fromJson(Map<String, dynamic> json) {
    return FriendRequest(
      id: (json['_key'] ?? json['id'] ?? '').toString(),
      requesterId: (json['requester_id'] ?? '').toString(),
      recipientId: (json['recipient_id'] ?? '').toString(),
      status: (json['status'] ?? 'pending').toString(),
      createdAt: DateTime.parse((json['created_at'] ?? DateTime.now().toIso8601String()).toString()),
      sourceBubbleId: (json['source_bubble_id'] ?? '').toString(),
      autoGenerated: (json['auto_generated'] ?? true) as bool,
      requesterName: json['requester_name']?.toString(),
      requesterUsername: json['requester_username']?.toString(),
      requesterProfilePicUrl: json['requester_profile_pic_url']?.toString(),
      bubbleName: json['bubble_name']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'requester_id': requesterId,
      'recipient_id': recipientId,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'source_bubble_id': sourceBubbleId,
      'auto_generated': autoGenerated,
      'requester_name': requesterName,
      'requester_username': requesterUsername,
      'requester_profile_pic_url': requesterProfilePicUrl,
      'bubble_name': bubbleName,
    };
  }

  FriendRequest copyWith({
    String? id,
    String? requesterId,
    String? recipientId,
    String? status,
    DateTime? createdAt,
    String? sourceBubbleId,
    bool? autoGenerated,
    String? requesterName,
    String? requesterUsername,
    String? requesterProfilePicUrl,
    String? bubbleName,
  }) {
    return FriendRequest(
      id: id ?? this.id,
      requesterId: requesterId ?? this.requesterId,
      recipientId: recipientId ?? this.recipientId,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      sourceBubbleId: sourceBubbleId ?? this.sourceBubbleId,
      autoGenerated: autoGenerated ?? this.autoGenerated,
      requesterName: requesterName ?? this.requesterName,
      requesterUsername: requesterUsername ?? this.requesterUsername,
      requesterProfilePicUrl: requesterProfilePicUrl ?? this.requesterProfilePicUrl,
      bubbleName: bubbleName ?? this.bubbleName,
    );
  }

  @override
  List<Object?> get props => [
    id,
    requesterId,
    recipientId,
    status,
    createdAt,
    sourceBubbleId,
    autoGenerated,
    requesterName,
    requesterUsername,
    requesterProfilePicUrl,
    bubbleName,
  ];
}
