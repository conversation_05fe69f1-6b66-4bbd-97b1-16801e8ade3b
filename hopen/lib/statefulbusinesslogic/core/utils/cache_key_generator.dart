import 'dart:convert';
import 'package:crypto/crypto.dart';

/// Utility class for generating consistent cache keys across the application
/// Eliminates duplication and ensures consistent cache key generation patterns
class CacheKeyGenerator {
  const CacheKeyGenerator._();

  /// Generate a cache key for HTTP requests
  static String generateHttpCacheKey(
    String method,
    String url, {
    Map<String, String>? headers,
    Object? body,
  }) {
    final components = <String>[method.toUpperCase(), _normalizeUrl(url)];

    // Add headers if present (sorted for consistency)
    if (headers != null && headers.isNotEmpty) {
      final sortedHeaders = Map.fromEntries(
        headers.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
      );
      components.add('headers:${jsonEncode(sortedHeaders)}');
    }

    // Add body if present
    if (body != null) {
      components.add('body:${_hashObject(body)}');
    }

    return _generateHash(components.join('|'));
  }

  /// Generate a cache key for image resources
  static String generateImageCacheKey(
    String imageUrl, {
    double? scale,
    Map<String, String>? headers,
    int? width,
    int? height,
  }) {
    final components = <String>['image', _normalizeUrl(imageUrl)];

    if (scale != null) {
      components.add('scale:$scale');
    }

    if (width != null) {
      components.add('width:$width');
    }

    if (height != null) {
      components.add('height:$height');
    }

    // Add headers if present (sorted for consistency)
    if (headers != null && headers.isNotEmpty) {
      final sortedHeaders = Map.fromEntries(
        headers.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
      );
      components.add('headers:${jsonEncode(sortedHeaders)}');
    }

    return _generateHash(components.join('|'));
  }

  /// Generate a cache key for user-specific data
  static String generateUserCacheKey(
    String userId,
    String dataType, {
    Map<String, dynamic>? parameters,
  }) {
    final components = <String>['user', userId, dataType];

    if (parameters != null && parameters.isNotEmpty) {
      final sortedParams = Map.fromEntries(
        parameters.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
      );
      components.add('params:${jsonEncode(sortedParams)}');
    }

    return _generateHash(components.join('|'));
  }

  /// Generate a cache key for chat-related data
  static String generateChatCacheKey(
    String chatId,
    String dataType, {
    String? userId,
    Map<String, dynamic>? parameters,
  }) {
    final components = <String>['chat', chatId, dataType];

    if (userId != null) {
      components.add('user:$userId');
    }

    if (parameters != null && parameters.isNotEmpty) {
      final sortedParams = Map.fromEntries(
        parameters.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
      );
      components.add('params:${jsonEncode(sortedParams)}');
    }

    return _generateHash(components.join('|'));
  }

  /// Generate a cache key for bubble-related data
  static String generateBubbleCacheKey(
    String bubbleId,
    String dataType, {
    String? userId,
    Map<String, dynamic>? parameters,
  }) {
    final components = <String>['bubble', bubbleId, dataType];

    if (userId != null) {
      components.add('user:$userId');
    }

    if (parameters != null && parameters.isNotEmpty) {
      final sortedParams = Map.fromEntries(
        parameters.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
      );
      components.add('params:${jsonEncode(sortedParams)}');
    }

    return _generateHash(components.join('|'));
  }

  /// Generate a cache key for media files
  static String generateMediaCacheKey(
    String mediaId,
    String mediaType, {
    String? quality,
    String? format,
    Map<String, dynamic>? parameters,
  }) {
    final components = <String>['media', mediaId, mediaType];

    if (quality != null) {
      components.add('quality:$quality');
    }

    if (format != null) {
      components.add('format:$format');
    }

    if (parameters != null && parameters.isNotEmpty) {
      final sortedParams = Map.fromEntries(
        parameters.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
      );
      components.add('params:${jsonEncode(sortedParams)}');
    }

    return _generateHash(components.join('|'));
  }

  /// Generate a generic cache key with custom components
  static String generateGenericCacheKey(
    String namespace,
    List<String> components, {
    Map<String, dynamic>? parameters,
  }) {
    final keyComponents = <String>[namespace, ...components];

    if (parameters != null && parameters.isNotEmpty) {
      final sortedParams = Map.fromEntries(
        parameters.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
      );
      keyComponents.add('params:${jsonEncode(sortedParams)}');
    }

    return _generateHash(keyComponents.join('|'));
  }

  /// Generate a cache key for API responses with pagination
  static String generatePaginatedCacheKey(
    String endpoint, {
    int? page,
    int? limit,
    String? sortBy,
    String? sortOrder,
    Map<String, dynamic>? filters,
  }) {
    final components = <String>['paginated', _normalizeUrl(endpoint)];

    if (page != null) {
      components.add('page:$page');
    }

    if (limit != null) {
      components.add('limit:$limit');
    }

    if (sortBy != null) {
      components.add('sort:$sortBy');
    }

    if (sortOrder != null) {
      components.add('order:$sortOrder');
    }

    if (filters != null && filters.isNotEmpty) {
      final sortedFilters = Map.fromEntries(
        filters.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
      );
      components.add('filters:${jsonEncode(sortedFilters)}');
    }

    return _generateHash(components.join('|'));
  }

  /// Normalize URL for consistent cache keys
  static String _normalizeUrl(String url) {
    try {
      final uri = Uri.parse(url);
      // Remove fragment and normalize query parameters
      final normalizedUri = uri.replace(
        fragment: null,
        queryParameters:
            uri.queryParameters.isEmpty
                ? null
                : Map.fromEntries(
                  uri.queryParameters.entries.toList()
                    ..sort((a, b) => a.key.compareTo(b.key)),
                ),
      );
      return normalizedUri.toString();
    } catch (e) {
      // If URL parsing fails, return the original URL
      return url;
    }
  }

  /// Generate a hash from the input string
  static String _generateHash(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(
      0,
      16,
    ); // Use first 16 characters for shorter keys
  }

  /// Hash an object for consistent representation
  static String _hashObject(Object obj) {
    try {
      final jsonString = jsonEncode(obj);
      return _generateHash(jsonString);
    } catch (e) {
      // If JSON encoding fails, use string representation
      return _generateHash(obj.toString());
    }
  }
}
