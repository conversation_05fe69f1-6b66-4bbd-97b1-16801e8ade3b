import 'dart:convert';
import 'dart:io';
// Removed: import 'package:flutter/foundation.dart' show kIsWeb;

import 'package:drift/drift.dart';
// Removed: import 'package:drift/native.dart';
// Removed: import 'package:drift/web.dart';

import '../models/chat_message.dart' as domain_models;

import 'connection/connection.dart';

part 'app_database.g.dart'; // Generated by drift_dev

// Test comment to trigger build_runner watch
// --- Enum Definitions for Drift ---
// We'll use Drift's built-in enum support directly in table definitions if simple,
// or define them here if shared across tables / complex.
// For now, let's assume we'll map them in the table definitions.

// --- Table Definitions ---

// 1. Chat Messages Table
@DataClassName('DriftChatMessage')
class ChatMessages extends Table {
  TextColumn get id => text()(); // Primary key for the message
  TextColumn get chatId =>
      text()(); // Associates message with a specific chat conversation
  TextColumn get textContent =>
      text().named('text')(); // Renamed to avoid conflict with Table.text
  DateTimeColumn get timestamp => dateTime()();
  TextColumn get senderId => text()();

  // Using IntEnumColumn for enums from domain_models
  IntColumn get mediaType => intEnum<domain_models.MediaType>()();
  TextColumn get mediaUrl => text().nullable()();
  TextColumn get mediaThumbnail => text().nullable()();
  TextColumn get mediaTitle => text().nullable()();
  TextColumn get mediaDescription => text().nullable()();
  IntColumn get deliveryStatus =>
      intEnum<domain_models.MessageDeliveryStatus>().named('status')();

  TextColumn get reactions =>
      text()
          .map(const JsonMapConverter())
          .nullable()(); // Store Map as JSON string
  TextColumn get replyToMessageId =>
      text().nullable()(); // ID of the message being replied to
  BoolColumn get isForwarded => boolean().withDefault(const Constant(false))();

  @override
  Set<Column> get primaryKey => {id}; // Message ID is unique

  // Index for faster querying by chatId and timestamp
  @override
  List<Index> get indexes => [
    Index(
      'idx_chat_messages_chat_id_timestamp',
      'CREATE INDEX idx_chat_messages_chat_id_timestamp ON chat_messages (chat_id, timestamp DESC)',
    ),
  ];
}

// Helper for JsonMapConverter
class JsonMapConverter extends TypeConverter<Map<String, dynamic>, String> {
  const JsonMapConverter();

  @override
  Map<String, dynamic> fromSql(String fromDb) {
    if (fromDb.isEmpty) return {};
    return json.decode(fromDb) as Map<String, dynamic>;
  }

  @override
  String toSql(Map<String, dynamic> value) => json.encode(value);
}

// 2. User Settings Table (Generic Key-Value)
@DataClassName('DriftUserSetting')
class UserSettings extends Table {
  TextColumn get settingKey => text().named('key')(); // Setting key
  TextColumn get settingValue =>
      text().named('value')(); // Setting value stored as JSON string

  @override
  Set<Column> get primaryKey => {settingKey};
}

// 3. Chat Metadata Table
@DataClassName('DriftChatMetadata')
class ChatMetadatas extends Table {
  // Drift convention: plural table names
  TextColumn get chatId => text()();
  TextColumn get metadata =>
      text().map(const JsonMapConverter())(); // Store Map as JSON string

  @override
  Set<Column> get primaryKey => {chatId};
}

// 4. Unread Counts Table
@DataClassName('DriftUnreadCount')
class UnreadCounts extends Table {
  TextColumn get chatId => text()();
  IntColumn get count => integer()();

  @override
  Set<Column> get primaryKey => {chatId};
}

// 5. Draft Messages Table
@DataClassName('DriftDraftMessage')
class DraftMessages extends Table {
  TextColumn get chatId => text()();
  TextColumn get draftText => text()();

  @override
  Set<Column> get primaryKey => {chatId};
}

// 6. FCM Messages Table (from MessagingService)
@DataClassName('DriftFcmMessage')
class FcmMessages extends Table {
  IntColumn get id =>
      integer().autoIncrement()(); // Auto-incrementing primary key
  TextColumn get messageId => text().nullable()(); // Original FCM message ID
  TextColumn get data =>
      text().map(const JsonMapConverter())(); // FCM data payload as JSON
  DateTimeColumn get receivedTimestamp => dateTime().named('timestamp')();
  TextColumn get notificationTitle => text().nullable()();
  TextColumn get notificationBody => text().nullable()();

  @override
  List<Index> get indexes => [
    Index(
      'idx_fcm_messages_timestamp',
      'CREATE INDEX idx_fcm_messages_timestamp ON fcm_messages (timestamp DESC)',
    ),
  ];
}

// 7. Profile Pictures Table for persistent image caching
@DataClassName('DriftProfilePicture')
class ProfilePictures extends Table {
  TextColumn get userId => text()(); // User ID
  TextColumn get imageUrl => text()(); // Original URL
  TextColumn get localPath => text()(); // Local file path
  TextColumn get thumbnailPath => text().nullable()(); // Thumbnail path
  IntColumn get fileSize => integer()(); // File size in bytes
  TextColumn get format => text()(); // Image format (webp, jpg, etc.)
  DateTimeColumn get cachedAt => dateTime()(); // Cache timestamp
  DateTimeColumn get lastAccessed => dateTime()(); // Last access time
  BoolColumn get isActive =>
      boolean().withDefault(const Constant(true))(); // Active status

  @override
  Set<Column> get primaryKey => {userId};
}

// 8. Contact Requests Table
@DataClassName('DriftContactRequest')
class ContactRequests extends Table {
  TextColumn get id => text()(); // Request ID (primary key)
  TextColumn get requesterId => text()(); // User who sent the request
  TextColumn get recipientId => text()(); // User who received the request
  TextColumn get status => text()(); // pending, accepted, declined, cancelled
  TextColumn get message => text().nullable()(); // Optional message
  DateTimeColumn get createdAt => dateTime()(); // When request was created
  DateTimeColumn get updatedAt => dateTime()(); // When request was last updated
  DateTimeColumn get cachedAt => dateTime()(); // When this data was cached
  BoolColumn get isStale =>
      boolean().withDefault(const Constant(false))(); // Cache staleness

  @override
  Set<Column> get primaryKey => {id};

  @override
  List<Index> get indexes => [
    Index(
      'idx_contact_requests_requester',
      'CREATE INDEX idx_contact_requests_requester ON contact_requests (requester_id)',
    ),
    Index(
      'idx_contact_requests_recipient',
      'CREATE INDEX idx_contact_requests_recipient ON contact_requests (recipient_id)',
    ),
    Index(
      'idx_contact_requests_status',
      'CREATE INDEX idx_contact_requests_status ON contact_requests (status)',
    ),
  ];
}

// 9. Friend Requests Table
@DataClassName('DriftFriendRequest')
class FriendRequests extends Table {
  TextColumn get id => text()(); // Request ID (primary key)
  TextColumn get requesterId => text()(); // User who sent the request
  TextColumn get recipientId => text()(); // User who received the request
  TextColumn get status => text()(); // pending, accepted, declined
  TextColumn get sourceBubbleId =>
      text()(); // Bubble that triggered this request
  BoolColumn get autoGenerated =>
      boolean().withDefault(
        const Constant(true),
      )(); // Always true for friend requests
  DateTimeColumn get createdAt => dateTime()(); // When request was created
  DateTimeColumn get updatedAt => dateTime()(); // When request was last updated
  DateTimeColumn get cachedAt => dateTime()(); // When this data was cached
  BoolColumn get isStale =>
      boolean().withDefault(const Constant(false))(); // Cache staleness

  @override
  Set<Column> get primaryKey => {id};

  @override
  List<Index> get indexes => [
    Index(
      'idx_friend_requests_requester',
      'CREATE INDEX idx_friend_requests_requester ON friend_requests (requester_id)',
    ),
    Index(
      'idx_friend_requests_recipient',
      'CREATE INDEX idx_friend_requests_recipient ON friend_requests (recipient_id)',
    ),
    Index(
      'idx_friend_requests_status',
      'CREATE INDEX idx_friend_requests_status ON friend_requests (status)',
    ),
  ];
}

// 10. User Profiles Table for comprehensive user data caching
@DataClassName('DriftUserProfile')
class UserProfiles extends Table {
  TextColumn get id => text()(); // User ID (primary key)
  TextColumn get email => text().nullable()(); // Email
  TextColumn get username => text().nullable()(); // Username
  TextColumn get firstName => text().nullable()(); // First name
  TextColumn get lastName => text().nullable()(); // Last name
  TextColumn get profilePictureUrl => text().nullable()(); // Avatar URL
  DateTimeColumn get birthday => dateTime().nullable()(); // Date of birth
  TextColumn get friendIds => text().nullable()(); // JSON array of friend IDs
  TextColumn get bubbleId => text().nullable()(); // Current bubble ID
  TextColumn get contactIds => text().nullable()(); // JSON array of contact IDs
  TextColumn get onlineStatus =>
      text().withDefault(const Constant('offline'))(); // Online status
  TextColumn get bubbleStatus =>
      text().withDefault(
        const Constant('noBubble'),
      )(); // Bubble membership status
  TextColumn get blockedUserIds =>
      text().nullable()(); // JSON array of blocked user IDs
  TextColumn get pendingSentContactRequestIds =>
      text().nullable()(); // JSON array
  TextColumn get pendingReceivedContactRequestIds =>
      text().nullable()(); // JSON array
  BoolColumn get hasCompletedOnboarding =>
      boolean().withDefault(const Constant(false))(); // Onboarding status
  DateTimeColumn get cachedAt => dateTime()(); // When this data was cached
  DateTimeColumn get lastRefreshed =>
      dateTime()(); // Last network refresh attempt
  DateTimeColumn get lastAccessed =>
      dateTime()(); // Last time this profile was accessed
  BoolColumn get isStale =>
      boolean().withDefault(
        const Constant(false),
      )(); // Whether data needs refresh

  @override
  Set<Column> get primaryKey => {id};

  @override
  List<Index> get indexes => [
    Index(
      'idx_profile_pictures_user_id',
      'CREATE INDEX idx_profile_pictures_user_id ON profile_pictures (user_id)',
    ),
    Index(
      'idx_profile_pictures_cached_at',
      'CREATE INDEX idx_profile_pictures_cached_at ON profile_pictures (cached_at DESC)',
    ),
    Index(
      'idx_profile_pictures_last_accessed',
      'CREATE INDEX idx_profile_pictures_last_accessed ON profile_pictures (last_accessed DESC)',
    ),
  ];
}

// Removed old connectToDatabase function
/*
QueryExecutor connectToDatabase() {
  if (kIsWeb) {
    // For web platform
    return WebDatabase('hopen_db');
  }

  return LazyDatabase(() async {
    // For mobile/desktop platforms
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'hopen_db.sqlite'));

    // Make sure the parent directory exists
    if (!await file.parent.exists()) {
      await file.parent.create(recursive: true);
    }

    // Apply workaround for Flutter 3.3.0 and later on mobile platforms
    if (Platform.isAndroid || Platform.isIOS) {
      try {
        await applyWorkaroundToOpenSqlite3OnOldAndroidVersions();
      } catch (e) {
        print('SQLite workaround error: $e');
        // Continue anyway, as this is just a workaround
      }
    }

    return NativeDatabase.createInBackground(file);
  });
}
*/

// --- Database Class Definition ---
@DriftDatabase(
  tables: [
    ChatMessages,
    UserSettings,
    ChatMetadatas,
    UnreadCounts,
    DraftMessages,
    FcmMessages,
    ProfilePictures,
    ContactRequests,
    FriendRequests,
    UserProfiles,
  ],
  daos: [
    ChatMessageDao,
    UserSettingDao,
    ChatMetadataDao,
    UnreadCountDao,
    DraftMessageDao,
    FcmMessageDao,
    ProfilePictureDao,
    ContactRequestDao,
    FriendRequestDao,
    UserProfileDao,
  ],
)
class AppDatabase extends _$AppDatabase {
  // Use the new connect() function from the conditional import
  AppDatabase() : super(connect());

  // Schema version - increment this when you change the schema.
  @override
  int get schemaVersion => 4; // Incremented for ContactRequests and FriendRequests tables

  // DAO Getters
  // These are initialized by the generated _$AppDatabase class
  @override
  late final ChatMessageDao chatMessageDao = ChatMessageDao(this);
  @override
  late final UserSettingDao userSettingDao = UserSettingDao(this);
  @override
  late final ChatMetadataDao chatMetadataDao = ChatMetadataDao(this);
  @override
  late final UnreadCountDao unreadCountDao = UnreadCountDao(this);
  @override
  late final DraftMessageDao draftMessageDao = DriftMessageDao(this);
  @override
  late final FcmMessageDao fcmMessageDao = FcmMessageDao(this);
  @override
  late final ProfilePictureDao profilePictureDao = ProfilePictureDao(this);
  @override
  late final ContactRequestDao contactRequestDao = ContactRequestDao(this);
  @override
  late final FriendRequestDao friendRequestDao = FriendRequestDao(this);
  @override
  late final UserProfileDao userProfileDao = UserProfileDao(this);

  // Database migration strategy for handling schema changes
  @override
  MigrationStrategy get migration => MigrationStrategy(
    onCreate: (Migrator m) {
      return m.createAll();
    },
    onUpgrade: (Migrator m, int from, int to) async {
      // Handle database schema migrations between versions
      if (from < 2) {
        // Migration for version 2: Add ProfilePictures table
        await m.createTable(profilePictures);
      }

      if (from < 3) {
        // Example migration for version 3
        // await m.addColumn(userSettings, userSettings.darkMode);
        // await m.alterTable(TableMigration(userSettings));
      }

      // Add more migration steps as needed for future versions
      // Always use incremental checks (from < version) to ensure
      // migrations are applied in the correct order
    },
    beforeOpen: (details) async {
      // Enable foreign key constraints
      await customStatement('PRAGMA foreign_keys = ON');

      // Optimize SQLite performance
      await customStatement('PRAGMA journal_mode = WAL');
      await customStatement('PRAGMA synchronous = NORMAL');
      await customStatement('PRAGMA cache_size = 10000');
      await customStatement('PRAGMA temp_store = MEMORY');

      // Log database opening
      print(
        'Database opened: ${details.wasCreated ? "Created new" : "Opened existing"} database (version ${details.versionNow})',
      );
    },
  );
}

// --- DAO Definitions ---

// 1. ChatMessage DAO
@DriftAccessor(tables: [ChatMessages])
class ChatMessageDao extends DatabaseAccessor<AppDatabase>
    with _$ChatMessageDaoMixin {
  ChatMessageDao(super.db);

  // Basic CRUD operations (examples)
  Future<List<DriftChatMessage>> getMessagesForChat(String chatId) =>
      (select(chatMessages)
            ..where((tbl) => tbl.chatId.equals(chatId))
            ..orderBy([
              (t) => OrderingTerm(
                expression: t.timestamp,
                mode: OrderingMode.desc,
              ),
            ]))
          .get();

  Stream<List<DriftChatMessage>> watchMessagesForChat(String chatId) =>
      (select(chatMessages)
            ..where((tbl) => tbl.chatId.equals(chatId))
            ..orderBy([
              (t) => OrderingTerm(
                expression: t.timestamp,
                mode: OrderingMode.desc,
              ),
            ]))
          .watch();

  Future<void> insertMessage(DriftChatMessage message) =>
      into(chatMessages).insert(message);
  Future<void> insertOrReplaceMessage(DriftChatMessage message) =>
      into(chatMessages).insertOnConflictUpdate(message);
  Future<bool> updateMessage(DriftChatMessage message) =>
      update(chatMessages).replace(message);
  Future<int> deleteMessage(String id) =>
      (delete(chatMessages)..where((tbl) => tbl.id.equals(id))).go();
  Future<int> deleteAllMessagesForChat(String chatId) =>
      (delete(chatMessages)..where((tbl) => tbl.chatId.equals(chatId))).go();
}

// 2. UserSetting DAO
@DriftAccessor(tables: [UserSettings])
class UserSettingDao extends DatabaseAccessor<AppDatabase>
    with _$UserSettingDaoMixin {
  UserSettingDao(super.db);

  Future<DriftUserSetting?> getSetting(String key) =>
      (select(userSettings)
        ..where((tbl) => tbl.settingKey.equals(key))).getSingleOrNull();
  Future<void> insertOrUpdateSetting(DriftUserSetting setting) =>
      into(userSettings).insertOnConflictUpdate(setting);
  Future<int> deleteSetting(String key) =>
      (delete(userSettings)..where((tbl) => tbl.settingKey.equals(key))).go();
}

// 3. ChatMetadata DAO
@DriftAccessor(tables: [ChatMetadatas])
class ChatMetadataDao extends DatabaseAccessor<AppDatabase>
    with _$ChatMetadataDaoMixin {
  ChatMetadataDao(super.db);

  Future<DriftChatMetadata?> getChatMetadata(String chatId) =>
      (select(chatMetadatas)
        ..where((tbl) => tbl.chatId.equals(chatId))).getSingleOrNull();
  Future<void> insertOrUpdateChatMetadata(DriftChatMetadata metadata) =>
      into(chatMetadatas).insertOnConflictUpdate(metadata);
  Future<int> deleteChatMetadata(String chatId) =>
      (delete(chatMetadatas)..where((tbl) => tbl.chatId.equals(chatId))).go();
}

// 4. UnreadCount DAO
@DriftAccessor(tables: [UnreadCounts])
class UnreadCountDao extends DatabaseAccessor<AppDatabase>
    with _$UnreadCountDaoMixin {
  UnreadCountDao(super.db);

  Future<DriftUnreadCount?> getUnreadCount(String chatId) =>
      (select(unreadCounts)
        ..where((tbl) => tbl.chatId.equals(chatId))).getSingleOrNull();
  Stream<DriftUnreadCount?> watchUnreadCountStream(String chatId) =>
      (select(unreadCounts)
        ..where((tbl) => tbl.chatId.equals(chatId))).watchSingleOrNull();
  Future<void> insertOrUpdateUnreadCount(DriftUnreadCount unread) =>
      into(unreadCounts).insertOnConflictUpdate(unread);
  Future<void> setUnreadCount(String chatId, int count) => into(
    unreadCounts,
  ).insertOnConflictUpdate(DriftUnreadCount(chatId: chatId, count: count));
}

// 5. DraftMessage DAO
@DriftAccessor(tables: [DraftMessages])
class DraftMessageDao extends DatabaseAccessor<AppDatabase>
    with _$DraftMessageDaoMixin {
  DraftMessageDao(super.db);

  Future<DriftDraftMessage?> getDraftMessage(String chatId) =>
      (select(draftMessages)
        ..where((tbl) => tbl.chatId.equals(chatId))).getSingleOrNull();
  Future<void> insertOrUpdateDraftMessage(DriftDraftMessage draft) =>
      into(draftMessages).insertOnConflictUpdate(draft);
  Future<int> deleteDraftMessage(String chatId) =>
      (delete(draftMessages)..where((tbl) => tbl.chatId.equals(chatId))).go();
}

// 6. FcmMessage DAO
@DriftAccessor(tables: [FcmMessages])
class FcmMessageDao extends DatabaseAccessor<AppDatabase>
    with _$FcmMessageDaoMixin {
  FcmMessageDao(super.db);

  Future<List<DriftFcmMessage>> getAllFcmMessages() =>
      (select(fcmMessages)..orderBy([
        (t) => OrderingTerm(
          expression: t.receivedTimestamp,
          mode: OrderingMode.desc,
        ),
      ])).get();
  Future<void> insertFcmMessage(DriftFcmMessage message) =>
      into(fcmMessages).insert(message);
  Future<int> deleteAllFcmMessages() => delete(fcmMessages).go();
}

// 7. ProfilePicture DAO
@DriftAccessor(tables: [ProfilePictures])
class ProfilePictureDao extends DatabaseAccessor<AppDatabase>
    with _$ProfilePictureDaoMixin {
  ProfilePictureDao(super.db);

  Future<DriftProfilePicture?> getProfilePicture(String userId) =>
      (select(profilePictures)
        ..where((t) => t.userId.equals(userId))).getSingleOrNull();

  Future<void> saveProfilePicture(DriftProfilePicture picture) =>
      into(profilePictures).insertOnConflictUpdate(picture);

  Future<void> deleteProfilePicture(String userId) =>
      (delete(profilePictures)..where((t) => t.userId.equals(userId))).go();

  Future<void> updateLastAccessed(String userId) => (update(profilePictures)
    ..where(
      (t) => t.userId.equals(userId),
    )).write(ProfilePicturesCompanion(lastAccessed: Value(DateTime.now())));

  Future<List<DriftProfilePicture>> getOldCacheEntries(Duration age) {
    final cutoff = DateTime.now().subtract(age);
    return (select(profilePictures)
      ..where((t) => t.lastAccessed.isSmallerThanValue(cutoff))).get();
  }

  Future<List<DriftProfilePicture>> getAllProfilePictures() =>
      select(profilePictures).get();

  Future<int> getTotalCacheSize() async {
    final pictures = await getAllProfilePictures();
    return pictures.fold<int>(0, (sum, picture) => sum + picture.fileSize);
  }

  Future<void> clearAllCache() => delete(profilePictures).go();
}

// 8. ContactRequest DAO - Contact request caching with real-time updates
@DriftAccessor(tables: [ContactRequests])
class ContactRequestDao extends DatabaseAccessor<AppDatabase>
    with _$ContactRequestDaoMixin {
  ContactRequestDao(super.db);

  /// Get all contact requests for a user (sent and received)
  Future<List<DriftContactRequest>> getContactRequestsForUser(String userId) =>
      (select(contactRequests)..where(
        (tbl) =>
            tbl.requesterId.equals(userId) | tbl.recipientId.equals(userId),
      )).get();

  /// Watch contact requests for a user (reactive stream)
  Stream<List<DriftContactRequest>> watchContactRequestsForUser(
    String userId,
  ) =>
      (select(contactRequests)..where(
        (tbl) =>
            tbl.requesterId.equals(userId) | tbl.recipientId.equals(userId),
      )).watch();

  /// Get pending sent requests for a user
  Future<List<DriftContactRequest>> getPendingSentRequests(String userId) =>
      (select(contactRequests)..where(
        (tbl) => tbl.requesterId.equals(userId) & tbl.status.equals('pending'),
      )).get();

  /// Watch pending sent requests for a user
  Stream<List<DriftContactRequest>> watchPendingSentRequests(String userId) =>
      (select(contactRequests)..where(
        (tbl) => tbl.requesterId.equals(userId) & tbl.status.equals('pending'),
      )).watch();

  /// Get pending received requests for a user
  Future<List<DriftContactRequest>> getPendingReceivedRequests(String userId) =>
      (select(contactRequests)..where(
        (tbl) => tbl.recipientId.equals(userId) & tbl.status.equals('pending'),
      )).get();

  /// Watch pending received requests for a user
  Stream<List<DriftContactRequest>> watchPendingReceivedRequests(
    String userId,
  ) =>
      (select(contactRequests)..where(
        (tbl) => tbl.recipientId.equals(userId) & tbl.status.equals('pending'),
      )).watch();

  /// Insert or update contact request (upsert)
  Future<void> insertOrUpdateContactRequest(DriftContactRequest request) =>
      into(contactRequests).insertOnConflictUpdate(request);

  /// Update contact request status
  Future<void> updateContactRequestStatus(String requestId, String status) =>
      (update(contactRequests)..where((tbl) => tbl.id.equals(requestId))).write(
        ContactRequestsCompanion(
          status: Value(status),
          updatedAt: Value(DateTime.now()),
        ),
      );

  /// Delete contact request
  Future<void> deleteContactRequest(String requestId) =>
      (delete(contactRequests)..where((tbl) => tbl.id.equals(requestId))).go();

  /// Get contact request by ID
  Future<DriftContactRequest?> getContactRequestById(String requestId) =>
      (select(contactRequests)
        ..where((tbl) => tbl.id.equals(requestId))).getSingleOrNull();

  /// Clear all contact requests for a user
  Future<void> clearContactRequestsForUser(String userId) =>
      (delete(contactRequests)..where(
        (tbl) =>
            tbl.requesterId.equals(userId) | tbl.recipientId.equals(userId),
      )).go();
}

// 9. FriendRequest DAO - Friend request caching with real-time updates
@DriftAccessor(tables: [FriendRequests])
class FriendRequestDao extends DatabaseAccessor<AppDatabase>
    with _$FriendRequestDaoMixin {
  FriendRequestDao(super.db);

  /// Get all friend requests for a user (sent and received)
  Future<List<DriftFriendRequest>> getFriendRequestsForUser(String userId) =>
      (select(friendRequests)..where(
        (tbl) =>
            tbl.requesterId.equals(userId) | tbl.recipientId.equals(userId),
      )).get();

  /// Watch friend requests for a user (reactive stream)
  Stream<List<DriftFriendRequest>> watchFriendRequestsForUser(String userId) =>
      (select(friendRequests)..where(
        (tbl) =>
            tbl.requesterId.equals(userId) | tbl.recipientId.equals(userId),
      )).watch();

  /// Get pending received friend requests for a user
  Future<List<DriftFriendRequest>> getPendingReceivedFriendRequests(
    String userId,
  ) =>
      (select(friendRequests)..where(
        (tbl) => tbl.recipientId.equals(userId) & tbl.status.equals('pending'),
      )).get();

  /// Watch pending received friend requests for a user
  Stream<List<DriftFriendRequest>> watchPendingReceivedFriendRequests(
    String userId,
  ) =>
      (select(friendRequests)..where(
        (tbl) => tbl.recipientId.equals(userId) & tbl.status.equals('pending'),
      )).watch();

  /// Insert or update friend request (upsert)
  Future<void> insertOrUpdateFriendRequest(DriftFriendRequest request) =>
      into(friendRequests).insertOnConflictUpdate(request);

  /// Update friend request status
  Future<void> updateFriendRequestStatus(String requestId, String status) =>
      (update(friendRequests)..where((tbl) => tbl.id.equals(requestId))).write(
        FriendRequestsCompanion(
          status: Value(status),
          updatedAt: Value(DateTime.now()),
        ),
      );

  /// Delete friend request
  Future<void> deleteFriendRequest(String requestId) =>
      (delete(friendRequests)..where((tbl) => tbl.id.equals(requestId))).go();

  /// Get friend request by ID
  Future<DriftFriendRequest?> getFriendRequestById(String requestId) =>
      (select(friendRequests)
        ..where((tbl) => tbl.id.equals(requestId))).getSingleOrNull();

  /// Clear all friend requests for a user
  Future<void> clearFriendRequestsForUser(String userId) =>
      (delete(friendRequests)..where(
        (tbl) =>
            tbl.requesterId.equals(userId) | tbl.recipientId.equals(userId),
      )).go();
}

// 10. UserProfile DAO - Comprehensive user data caching with stale-while-revalidate
@DriftAccessor(tables: [UserProfiles])
class UserProfileDao extends DatabaseAccessor<AppDatabase>
    with _$UserProfileDaoMixin {
  UserProfileDao(super.db);

  /// Get user profile from cache
  Future<DriftUserProfile?> getUserProfile(String userId) =>
      (select(userProfiles)
        ..where((tbl) => tbl.id.equals(userId))).getSingleOrNull();

  /// Watch user profile changes (reactive stream)
  Stream<DriftUserProfile?> watchUserProfile(String userId) =>
      (select(userProfiles)
        ..where((tbl) => tbl.id.equals(userId))).watchSingleOrNull();

  /// Insert or update user profile (upsert)
  Future<void> insertOrUpdateUserProfile(DriftUserProfile profile) =>
      into(userProfiles).insertOnConflictUpdate(profile);

  /// Update last accessed time
  Future<void> updateLastAccessed(String userId) => (update(userProfiles)
    ..where(
      (tbl) => tbl.id.equals(userId),
    )).write(UserProfilesCompanion(lastAccessed: Value(DateTime.now())));

  /// Mark profile as stale (needs refresh)
  Future<void> markAsStale(String userId) => (update(userProfiles)..where(
    (tbl) => tbl.id.equals(userId),
  )).write(UserProfilesCompanion(isStale: const Value(true)));

  /// Mark profile as fresh (just refreshed)
  Future<void> markAsFresh(String userId) =>
      (update(userProfiles)..where((tbl) => tbl.id.equals(userId))).write(
        UserProfilesCompanion(
          isStale: const Value(false),
          lastRefreshed: Value(DateTime.now()),
        ),
      );

  /// Get all stale profiles (for background refresh)
  Future<List<DriftUserProfile>> getStaleProfiles() =>
      (select(userProfiles)..where((tbl) => tbl.isStale.equals(true))).get();

  /// Get profiles that need refresh (older than stalePeriod)
  Future<List<DriftUserProfile>> getProfilesNeedingRefresh(
    Duration stalePeriod,
  ) {
    final cutoffTime = DateTime.now().subtract(stalePeriod);
    return (select(userProfiles)
      ..where((tbl) => tbl.lastRefreshed.isSmallerThanValue(cutoffTime))).get();
  }

  /// Delete user profile
  Future<int> deleteUserProfile(String userId) =>
      (delete(userProfiles)..where((tbl) => tbl.id.equals(userId))).go();

  /// Clear all cached profiles
  Future<void> clearAllProfiles() => delete(userProfiles).go();

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    final totalProfiles =
        await (selectOnly(userProfiles)
          ..addColumns([userProfiles.id.count()])).getSingle();

    final staleProfiles =
        await (selectOnly(userProfiles)
              ..addColumns([userProfiles.id.count()])
              ..where(userProfiles.isStale.equals(true)))
            .getSingle();

    final recentProfiles =
        await (selectOnly(userProfiles)
              ..addColumns([userProfiles.id.count()])
              ..where(
                userProfiles.lastAccessed.isBiggerThanValue(
                  DateTime.now().subtract(const Duration(days: 1)),
                ),
              ))
            .getSingle();

    return {
      'totalProfiles': totalProfiles.read(userProfiles.id.count()) ?? 0,
      'staleProfiles': staleProfiles.read(userProfiles.id.count()) ?? 0,
      'recentProfiles': recentProfiles.read(userProfiles.id.count()) ?? 0,
    };
  }
}
