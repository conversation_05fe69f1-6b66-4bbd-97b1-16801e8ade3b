// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_database.dart';

// ignore_for_file: type=lint
class $ChatMessagesTable extends ChatMessages
    with TableInfo<$ChatMessagesTable, DriftChatMessage> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ChatMessagesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _chatIdMeta = const VerificationMeta('chatId');
  @override
  late final GeneratedColumn<String> chatId = GeneratedColumn<String>(
      'chat_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _textContentMeta =
      const VerificationMeta('textContent');
  @override
  late final GeneratedColumn<String> textContent = GeneratedColumn<String>(
      'text', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _timestampMeta =
      const VerificationMeta('timestamp');
  @override
  late final GeneratedColumn<DateTime> timestamp = GeneratedColumn<DateTime>(
      'timestamp', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _senderIdMeta =
      const VerificationMeta('senderId');
  @override
  late final GeneratedColumn<String> senderId = GeneratedColumn<String>(
      'sender_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  late final GeneratedColumnWithTypeConverter<domain_models.MediaType, int>
      mediaType = GeneratedColumn<int>('media_type', aliasedName, false,
              type: DriftSqlType.int, requiredDuringInsert: true)
          .withConverter<domain_models.MediaType>(
              $ChatMessagesTable.$convertermediaType);
  static const VerificationMeta _mediaUrlMeta =
      const VerificationMeta('mediaUrl');
  @override
  late final GeneratedColumn<String> mediaUrl = GeneratedColumn<String>(
      'media_url', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _mediaThumbnailMeta =
      const VerificationMeta('mediaThumbnail');
  @override
  late final GeneratedColumn<String> mediaThumbnail = GeneratedColumn<String>(
      'media_thumbnail', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _mediaTitleMeta =
      const VerificationMeta('mediaTitle');
  @override
  late final GeneratedColumn<String> mediaTitle = GeneratedColumn<String>(
      'media_title', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _mediaDescriptionMeta =
      const VerificationMeta('mediaDescription');
  @override
  late final GeneratedColumn<String> mediaDescription = GeneratedColumn<String>(
      'media_description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  late final GeneratedColumnWithTypeConverter<
      domain_models.MessageDeliveryStatus,
      int> deliveryStatus = GeneratedColumn<int>('status', aliasedName, false,
          type: DriftSqlType.int, requiredDuringInsert: true)
      .withConverter<domain_models.MessageDeliveryStatus>(
          $ChatMessagesTable.$converterdeliveryStatus);
  @override
  late final GeneratedColumnWithTypeConverter<Map<String, dynamic>?, String>
      reactions = GeneratedColumn<String>('reactions', aliasedName, true,
              type: DriftSqlType.string, requiredDuringInsert: false)
          .withConverter<Map<String, dynamic>?>(
              $ChatMessagesTable.$converterreactionsn);
  static const VerificationMeta _replyToMessageIdMeta =
      const VerificationMeta('replyToMessageId');
  @override
  late final GeneratedColumn<String> replyToMessageId = GeneratedColumn<String>(
      'reply_to_message_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _isForwardedMeta =
      const VerificationMeta('isForwarded');
  @override
  late final GeneratedColumn<bool> isForwarded = GeneratedColumn<bool>(
      'is_forwarded', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("is_forwarded" IN (0, 1))'),
      defaultValue: const Constant(false));
  @override
  List<GeneratedColumn> get $columns => [
        id,
        chatId,
        textContent,
        timestamp,
        senderId,
        mediaType,
        mediaUrl,
        mediaThumbnail,
        mediaTitle,
        mediaDescription,
        deliveryStatus,
        reactions,
        replyToMessageId,
        isForwarded
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'chat_messages';
  @override
  VerificationContext validateIntegrity(Insertable<DriftChatMessage> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('chat_id')) {
      context.handle(_chatIdMeta,
          chatId.isAcceptableOrUnknown(data['chat_id']!, _chatIdMeta));
    } else if (isInserting) {
      context.missing(_chatIdMeta);
    }
    if (data.containsKey('text')) {
      context.handle(_textContentMeta,
          textContent.isAcceptableOrUnknown(data['text']!, _textContentMeta));
    } else if (isInserting) {
      context.missing(_textContentMeta);
    }
    if (data.containsKey('timestamp')) {
      context.handle(_timestampMeta,
          timestamp.isAcceptableOrUnknown(data['timestamp']!, _timestampMeta));
    } else if (isInserting) {
      context.missing(_timestampMeta);
    }
    if (data.containsKey('sender_id')) {
      context.handle(_senderIdMeta,
          senderId.isAcceptableOrUnknown(data['sender_id']!, _senderIdMeta));
    } else if (isInserting) {
      context.missing(_senderIdMeta);
    }
    if (data.containsKey('media_url')) {
      context.handle(_mediaUrlMeta,
          mediaUrl.isAcceptableOrUnknown(data['media_url']!, _mediaUrlMeta));
    }
    if (data.containsKey('media_thumbnail')) {
      context.handle(
          _mediaThumbnailMeta,
          mediaThumbnail.isAcceptableOrUnknown(
              data['media_thumbnail']!, _mediaThumbnailMeta));
    }
    if (data.containsKey('media_title')) {
      context.handle(
          _mediaTitleMeta,
          mediaTitle.isAcceptableOrUnknown(
              data['media_title']!, _mediaTitleMeta));
    }
    if (data.containsKey('media_description')) {
      context.handle(
          _mediaDescriptionMeta,
          mediaDescription.isAcceptableOrUnknown(
              data['media_description']!, _mediaDescriptionMeta));
    }
    if (data.containsKey('reply_to_message_id')) {
      context.handle(
          _replyToMessageIdMeta,
          replyToMessageId.isAcceptableOrUnknown(
              data['reply_to_message_id']!, _replyToMessageIdMeta));
    }
    if (data.containsKey('is_forwarded')) {
      context.handle(
          _isForwardedMeta,
          isForwarded.isAcceptableOrUnknown(
              data['is_forwarded']!, _isForwardedMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  DriftChatMessage map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return DriftChatMessage(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      chatId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chat_id'])!,
      textContent: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}text'])!,
      timestamp: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}timestamp'])!,
      senderId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}sender_id'])!,
      mediaType: $ChatMessagesTable.$convertermediaType.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}media_type'])!),
      mediaUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}media_url']),
      mediaThumbnail: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}media_thumbnail']),
      mediaTitle: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}media_title']),
      mediaDescription: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}media_description']),
      deliveryStatus: $ChatMessagesTable.$converterdeliveryStatus.fromSql(
          attachedDatabase.typeMapping
              .read(DriftSqlType.int, data['${effectivePrefix}status'])!),
      reactions: $ChatMessagesTable.$converterreactionsn.fromSql(
          attachedDatabase.typeMapping
              .read(DriftSqlType.string, data['${effectivePrefix}reactions'])),
      replyToMessageId: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}reply_to_message_id']),
      isForwarded: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_forwarded'])!,
    );
  }

  @override
  $ChatMessagesTable createAlias(String alias) {
    return $ChatMessagesTable(attachedDatabase, alias);
  }

  static JsonTypeConverter2<domain_models.MediaType, int, int>
      $convertermediaType = const EnumIndexConverter<domain_models.MediaType>(
          domain_models.MediaType.values);
  static JsonTypeConverter2<domain_models.MessageDeliveryStatus, int, int>
      $converterdeliveryStatus =
      const EnumIndexConverter<domain_models.MessageDeliveryStatus>(
          domain_models.MessageDeliveryStatus.values);
  static TypeConverter<Map<String, dynamic>, String> $converterreactions =
      const JsonMapConverter();
  static TypeConverter<Map<String, dynamic>?, String?> $converterreactionsn =
      NullAwareTypeConverter.wrap($converterreactions);
}

class DriftChatMessage extends DataClass
    implements Insertable<DriftChatMessage> {
  final String id;
  final String chatId;
  final String textContent;
  final DateTime timestamp;
  final String senderId;
  final domain_models.MediaType mediaType;
  final String? mediaUrl;
  final String? mediaThumbnail;
  final String? mediaTitle;
  final String? mediaDescription;
  final domain_models.MessageDeliveryStatus deliveryStatus;
  final Map<String, dynamic>? reactions;
  final String? replyToMessageId;
  final bool isForwarded;
  const DriftChatMessage(
      {required this.id,
      required this.chatId,
      required this.textContent,
      required this.timestamp,
      required this.senderId,
      required this.mediaType,
      this.mediaUrl,
      this.mediaThumbnail,
      this.mediaTitle,
      this.mediaDescription,
      required this.deliveryStatus,
      this.reactions,
      this.replyToMessageId,
      required this.isForwarded});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['chat_id'] = Variable<String>(chatId);
    map['text'] = Variable<String>(textContent);
    map['timestamp'] = Variable<DateTime>(timestamp);
    map['sender_id'] = Variable<String>(senderId);
    {
      map['media_type'] = Variable<int>(
          $ChatMessagesTable.$convertermediaType.toSql(mediaType));
    }
    if (!nullToAbsent || mediaUrl != null) {
      map['media_url'] = Variable<String>(mediaUrl);
    }
    if (!nullToAbsent || mediaThumbnail != null) {
      map['media_thumbnail'] = Variable<String>(mediaThumbnail);
    }
    if (!nullToAbsent || mediaTitle != null) {
      map['media_title'] = Variable<String>(mediaTitle);
    }
    if (!nullToAbsent || mediaDescription != null) {
      map['media_description'] = Variable<String>(mediaDescription);
    }
    {
      map['status'] = Variable<int>(
          $ChatMessagesTable.$converterdeliveryStatus.toSql(deliveryStatus));
    }
    if (!nullToAbsent || reactions != null) {
      map['reactions'] = Variable<String>(
          $ChatMessagesTable.$converterreactionsn.toSql(reactions));
    }
    if (!nullToAbsent || replyToMessageId != null) {
      map['reply_to_message_id'] = Variable<String>(replyToMessageId);
    }
    map['is_forwarded'] = Variable<bool>(isForwarded);
    return map;
  }

  ChatMessagesCompanion toCompanion(bool nullToAbsent) {
    return ChatMessagesCompanion(
      id: Value(id),
      chatId: Value(chatId),
      textContent: Value(textContent),
      timestamp: Value(timestamp),
      senderId: Value(senderId),
      mediaType: Value(mediaType),
      mediaUrl: mediaUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(mediaUrl),
      mediaThumbnail: mediaThumbnail == null && nullToAbsent
          ? const Value.absent()
          : Value(mediaThumbnail),
      mediaTitle: mediaTitle == null && nullToAbsent
          ? const Value.absent()
          : Value(mediaTitle),
      mediaDescription: mediaDescription == null && nullToAbsent
          ? const Value.absent()
          : Value(mediaDescription),
      deliveryStatus: Value(deliveryStatus),
      reactions: reactions == null && nullToAbsent
          ? const Value.absent()
          : Value(reactions),
      replyToMessageId: replyToMessageId == null && nullToAbsent
          ? const Value.absent()
          : Value(replyToMessageId),
      isForwarded: Value(isForwarded),
    );
  }

  factory DriftChatMessage.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return DriftChatMessage(
      id: serializer.fromJson<String>(json['id']),
      chatId: serializer.fromJson<String>(json['chatId']),
      textContent: serializer.fromJson<String>(json['textContent']),
      timestamp: serializer.fromJson<DateTime>(json['timestamp']),
      senderId: serializer.fromJson<String>(json['senderId']),
      mediaType: $ChatMessagesTable.$convertermediaType
          .fromJson(serializer.fromJson<int>(json['mediaType'])),
      mediaUrl: serializer.fromJson<String?>(json['mediaUrl']),
      mediaThumbnail: serializer.fromJson<String?>(json['mediaThumbnail']),
      mediaTitle: serializer.fromJson<String?>(json['mediaTitle']),
      mediaDescription: serializer.fromJson<String?>(json['mediaDescription']),
      deliveryStatus: $ChatMessagesTable.$converterdeliveryStatus
          .fromJson(serializer.fromJson<int>(json['deliveryStatus'])),
      reactions: serializer.fromJson<Map<String, dynamic>?>(json['reactions']),
      replyToMessageId: serializer.fromJson<String?>(json['replyToMessageId']),
      isForwarded: serializer.fromJson<bool>(json['isForwarded']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'chatId': serializer.toJson<String>(chatId),
      'textContent': serializer.toJson<String>(textContent),
      'timestamp': serializer.toJson<DateTime>(timestamp),
      'senderId': serializer.toJson<String>(senderId),
      'mediaType': serializer.toJson<int>(
          $ChatMessagesTable.$convertermediaType.toJson(mediaType)),
      'mediaUrl': serializer.toJson<String?>(mediaUrl),
      'mediaThumbnail': serializer.toJson<String?>(mediaThumbnail),
      'mediaTitle': serializer.toJson<String?>(mediaTitle),
      'mediaDescription': serializer.toJson<String?>(mediaDescription),
      'deliveryStatus': serializer.toJson<int>(
          $ChatMessagesTable.$converterdeliveryStatus.toJson(deliveryStatus)),
      'reactions': serializer.toJson<Map<String, dynamic>?>(reactions),
      'replyToMessageId': serializer.toJson<String?>(replyToMessageId),
      'isForwarded': serializer.toJson<bool>(isForwarded),
    };
  }

  DriftChatMessage copyWith(
          {String? id,
          String? chatId,
          String? textContent,
          DateTime? timestamp,
          String? senderId,
          domain_models.MediaType? mediaType,
          Value<String?> mediaUrl = const Value.absent(),
          Value<String?> mediaThumbnail = const Value.absent(),
          Value<String?> mediaTitle = const Value.absent(),
          Value<String?> mediaDescription = const Value.absent(),
          domain_models.MessageDeliveryStatus? deliveryStatus,
          Value<Map<String, dynamic>?> reactions = const Value.absent(),
          Value<String?> replyToMessageId = const Value.absent(),
          bool? isForwarded}) =>
      DriftChatMessage(
        id: id ?? this.id,
        chatId: chatId ?? this.chatId,
        textContent: textContent ?? this.textContent,
        timestamp: timestamp ?? this.timestamp,
        senderId: senderId ?? this.senderId,
        mediaType: mediaType ?? this.mediaType,
        mediaUrl: mediaUrl.present ? mediaUrl.value : this.mediaUrl,
        mediaThumbnail:
            mediaThumbnail.present ? mediaThumbnail.value : this.mediaThumbnail,
        mediaTitle: mediaTitle.present ? mediaTitle.value : this.mediaTitle,
        mediaDescription: mediaDescription.present
            ? mediaDescription.value
            : this.mediaDescription,
        deliveryStatus: deliveryStatus ?? this.deliveryStatus,
        reactions: reactions.present ? reactions.value : this.reactions,
        replyToMessageId: replyToMessageId.present
            ? replyToMessageId.value
            : this.replyToMessageId,
        isForwarded: isForwarded ?? this.isForwarded,
      );
  DriftChatMessage copyWithCompanion(ChatMessagesCompanion data) {
    return DriftChatMessage(
      id: data.id.present ? data.id.value : this.id,
      chatId: data.chatId.present ? data.chatId.value : this.chatId,
      textContent:
          data.textContent.present ? data.textContent.value : this.textContent,
      timestamp: data.timestamp.present ? data.timestamp.value : this.timestamp,
      senderId: data.senderId.present ? data.senderId.value : this.senderId,
      mediaType: data.mediaType.present ? data.mediaType.value : this.mediaType,
      mediaUrl: data.mediaUrl.present ? data.mediaUrl.value : this.mediaUrl,
      mediaThumbnail: data.mediaThumbnail.present
          ? data.mediaThumbnail.value
          : this.mediaThumbnail,
      mediaTitle:
          data.mediaTitle.present ? data.mediaTitle.value : this.mediaTitle,
      mediaDescription: data.mediaDescription.present
          ? data.mediaDescription.value
          : this.mediaDescription,
      deliveryStatus: data.deliveryStatus.present
          ? data.deliveryStatus.value
          : this.deliveryStatus,
      reactions: data.reactions.present ? data.reactions.value : this.reactions,
      replyToMessageId: data.replyToMessageId.present
          ? data.replyToMessageId.value
          : this.replyToMessageId,
      isForwarded:
          data.isForwarded.present ? data.isForwarded.value : this.isForwarded,
    );
  }

  @override
  String toString() {
    return (StringBuffer('DriftChatMessage(')
          ..write('id: $id, ')
          ..write('chatId: $chatId, ')
          ..write('textContent: $textContent, ')
          ..write('timestamp: $timestamp, ')
          ..write('senderId: $senderId, ')
          ..write('mediaType: $mediaType, ')
          ..write('mediaUrl: $mediaUrl, ')
          ..write('mediaThumbnail: $mediaThumbnail, ')
          ..write('mediaTitle: $mediaTitle, ')
          ..write('mediaDescription: $mediaDescription, ')
          ..write('deliveryStatus: $deliveryStatus, ')
          ..write('reactions: $reactions, ')
          ..write('replyToMessageId: $replyToMessageId, ')
          ..write('isForwarded: $isForwarded')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      chatId,
      textContent,
      timestamp,
      senderId,
      mediaType,
      mediaUrl,
      mediaThumbnail,
      mediaTitle,
      mediaDescription,
      deliveryStatus,
      reactions,
      replyToMessageId,
      isForwarded);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is DriftChatMessage &&
          other.id == this.id &&
          other.chatId == this.chatId &&
          other.textContent == this.textContent &&
          other.timestamp == this.timestamp &&
          other.senderId == this.senderId &&
          other.mediaType == this.mediaType &&
          other.mediaUrl == this.mediaUrl &&
          other.mediaThumbnail == this.mediaThumbnail &&
          other.mediaTitle == this.mediaTitle &&
          other.mediaDescription == this.mediaDescription &&
          other.deliveryStatus == this.deliveryStatus &&
          other.reactions == this.reactions &&
          other.replyToMessageId == this.replyToMessageId &&
          other.isForwarded == this.isForwarded);
}

class ChatMessagesCompanion extends UpdateCompanion<DriftChatMessage> {
  final Value<String> id;
  final Value<String> chatId;
  final Value<String> textContent;
  final Value<DateTime> timestamp;
  final Value<String> senderId;
  final Value<domain_models.MediaType> mediaType;
  final Value<String?> mediaUrl;
  final Value<String?> mediaThumbnail;
  final Value<String?> mediaTitle;
  final Value<String?> mediaDescription;
  final Value<domain_models.MessageDeliveryStatus> deliveryStatus;
  final Value<Map<String, dynamic>?> reactions;
  final Value<String?> replyToMessageId;
  final Value<bool> isForwarded;
  final Value<int> rowid;
  const ChatMessagesCompanion({
    this.id = const Value.absent(),
    this.chatId = const Value.absent(),
    this.textContent = const Value.absent(),
    this.timestamp = const Value.absent(),
    this.senderId = const Value.absent(),
    this.mediaType = const Value.absent(),
    this.mediaUrl = const Value.absent(),
    this.mediaThumbnail = const Value.absent(),
    this.mediaTitle = const Value.absent(),
    this.mediaDescription = const Value.absent(),
    this.deliveryStatus = const Value.absent(),
    this.reactions = const Value.absent(),
    this.replyToMessageId = const Value.absent(),
    this.isForwarded = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ChatMessagesCompanion.insert({
    required String id,
    required String chatId,
    required String textContent,
    required DateTime timestamp,
    required String senderId,
    required domain_models.MediaType mediaType,
    this.mediaUrl = const Value.absent(),
    this.mediaThumbnail = const Value.absent(),
    this.mediaTitle = const Value.absent(),
    this.mediaDescription = const Value.absent(),
    required domain_models.MessageDeliveryStatus deliveryStatus,
    this.reactions = const Value.absent(),
    this.replyToMessageId = const Value.absent(),
    this.isForwarded = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        chatId = Value(chatId),
        textContent = Value(textContent),
        timestamp = Value(timestamp),
        senderId = Value(senderId),
        mediaType = Value(mediaType),
        deliveryStatus = Value(deliveryStatus);
  static Insertable<DriftChatMessage> custom({
    Expression<String>? id,
    Expression<String>? chatId,
    Expression<String>? textContent,
    Expression<DateTime>? timestamp,
    Expression<String>? senderId,
    Expression<int>? mediaType,
    Expression<String>? mediaUrl,
    Expression<String>? mediaThumbnail,
    Expression<String>? mediaTitle,
    Expression<String>? mediaDescription,
    Expression<int>? deliveryStatus,
    Expression<String>? reactions,
    Expression<String>? replyToMessageId,
    Expression<bool>? isForwarded,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (chatId != null) 'chat_id': chatId,
      if (textContent != null) 'text': textContent,
      if (timestamp != null) 'timestamp': timestamp,
      if (senderId != null) 'sender_id': senderId,
      if (mediaType != null) 'media_type': mediaType,
      if (mediaUrl != null) 'media_url': mediaUrl,
      if (mediaThumbnail != null) 'media_thumbnail': mediaThumbnail,
      if (mediaTitle != null) 'media_title': mediaTitle,
      if (mediaDescription != null) 'media_description': mediaDescription,
      if (deliveryStatus != null) 'status': deliveryStatus,
      if (reactions != null) 'reactions': reactions,
      if (replyToMessageId != null) 'reply_to_message_id': replyToMessageId,
      if (isForwarded != null) 'is_forwarded': isForwarded,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ChatMessagesCompanion copyWith(
      {Value<String>? id,
      Value<String>? chatId,
      Value<String>? textContent,
      Value<DateTime>? timestamp,
      Value<String>? senderId,
      Value<domain_models.MediaType>? mediaType,
      Value<String?>? mediaUrl,
      Value<String?>? mediaThumbnail,
      Value<String?>? mediaTitle,
      Value<String?>? mediaDescription,
      Value<domain_models.MessageDeliveryStatus>? deliveryStatus,
      Value<Map<String, dynamic>?>? reactions,
      Value<String?>? replyToMessageId,
      Value<bool>? isForwarded,
      Value<int>? rowid}) {
    return ChatMessagesCompanion(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      textContent: textContent ?? this.textContent,
      timestamp: timestamp ?? this.timestamp,
      senderId: senderId ?? this.senderId,
      mediaType: mediaType ?? this.mediaType,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      mediaThumbnail: mediaThumbnail ?? this.mediaThumbnail,
      mediaTitle: mediaTitle ?? this.mediaTitle,
      mediaDescription: mediaDescription ?? this.mediaDescription,
      deliveryStatus: deliveryStatus ?? this.deliveryStatus,
      reactions: reactions ?? this.reactions,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      isForwarded: isForwarded ?? this.isForwarded,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (chatId.present) {
      map['chat_id'] = Variable<String>(chatId.value);
    }
    if (textContent.present) {
      map['text'] = Variable<String>(textContent.value);
    }
    if (timestamp.present) {
      map['timestamp'] = Variable<DateTime>(timestamp.value);
    }
    if (senderId.present) {
      map['sender_id'] = Variable<String>(senderId.value);
    }
    if (mediaType.present) {
      map['media_type'] = Variable<int>(
          $ChatMessagesTable.$convertermediaType.toSql(mediaType.value));
    }
    if (mediaUrl.present) {
      map['media_url'] = Variable<String>(mediaUrl.value);
    }
    if (mediaThumbnail.present) {
      map['media_thumbnail'] = Variable<String>(mediaThumbnail.value);
    }
    if (mediaTitle.present) {
      map['media_title'] = Variable<String>(mediaTitle.value);
    }
    if (mediaDescription.present) {
      map['media_description'] = Variable<String>(mediaDescription.value);
    }
    if (deliveryStatus.present) {
      map['status'] = Variable<int>($ChatMessagesTable.$converterdeliveryStatus
          .toSql(deliveryStatus.value));
    }
    if (reactions.present) {
      map['reactions'] = Variable<String>(
          $ChatMessagesTable.$converterreactionsn.toSql(reactions.value));
    }
    if (replyToMessageId.present) {
      map['reply_to_message_id'] = Variable<String>(replyToMessageId.value);
    }
    if (isForwarded.present) {
      map['is_forwarded'] = Variable<bool>(isForwarded.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ChatMessagesCompanion(')
          ..write('id: $id, ')
          ..write('chatId: $chatId, ')
          ..write('textContent: $textContent, ')
          ..write('timestamp: $timestamp, ')
          ..write('senderId: $senderId, ')
          ..write('mediaType: $mediaType, ')
          ..write('mediaUrl: $mediaUrl, ')
          ..write('mediaThumbnail: $mediaThumbnail, ')
          ..write('mediaTitle: $mediaTitle, ')
          ..write('mediaDescription: $mediaDescription, ')
          ..write('deliveryStatus: $deliveryStatus, ')
          ..write('reactions: $reactions, ')
          ..write('replyToMessageId: $replyToMessageId, ')
          ..write('isForwarded: $isForwarded, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $UserSettingsTable extends UserSettings
    with TableInfo<$UserSettingsTable, DriftUserSetting> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $UserSettingsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _settingKeyMeta =
      const VerificationMeta('settingKey');
  @override
  late final GeneratedColumn<String> settingKey = GeneratedColumn<String>(
      'key', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _settingValueMeta =
      const VerificationMeta('settingValue');
  @override
  late final GeneratedColumn<String> settingValue = GeneratedColumn<String>(
      'value', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [settingKey, settingValue];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'user_settings';
  @override
  VerificationContext validateIntegrity(Insertable<DriftUserSetting> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('key')) {
      context.handle(_settingKeyMeta,
          settingKey.isAcceptableOrUnknown(data['key']!, _settingKeyMeta));
    } else if (isInserting) {
      context.missing(_settingKeyMeta);
    }
    if (data.containsKey('value')) {
      context.handle(
          _settingValueMeta,
          settingValue.isAcceptableOrUnknown(
              data['value']!, _settingValueMeta));
    } else if (isInserting) {
      context.missing(_settingValueMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {settingKey};
  @override
  DriftUserSetting map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return DriftUserSetting(
      settingKey: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}key'])!,
      settingValue: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}value'])!,
    );
  }

  @override
  $UserSettingsTable createAlias(String alias) {
    return $UserSettingsTable(attachedDatabase, alias);
  }
}

class DriftUserSetting extends DataClass
    implements Insertable<DriftUserSetting> {
  final String settingKey;
  final String settingValue;
  const DriftUserSetting(
      {required this.settingKey, required this.settingValue});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['key'] = Variable<String>(settingKey);
    map['value'] = Variable<String>(settingValue);
    return map;
  }

  UserSettingsCompanion toCompanion(bool nullToAbsent) {
    return UserSettingsCompanion(
      settingKey: Value(settingKey),
      settingValue: Value(settingValue),
    );
  }

  factory DriftUserSetting.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return DriftUserSetting(
      settingKey: serializer.fromJson<String>(json['settingKey']),
      settingValue: serializer.fromJson<String>(json['settingValue']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'settingKey': serializer.toJson<String>(settingKey),
      'settingValue': serializer.toJson<String>(settingValue),
    };
  }

  DriftUserSetting copyWith({String? settingKey, String? settingValue}) =>
      DriftUserSetting(
        settingKey: settingKey ?? this.settingKey,
        settingValue: settingValue ?? this.settingValue,
      );
  DriftUserSetting copyWithCompanion(UserSettingsCompanion data) {
    return DriftUserSetting(
      settingKey:
          data.settingKey.present ? data.settingKey.value : this.settingKey,
      settingValue: data.settingValue.present
          ? data.settingValue.value
          : this.settingValue,
    );
  }

  @override
  String toString() {
    return (StringBuffer('DriftUserSetting(')
          ..write('settingKey: $settingKey, ')
          ..write('settingValue: $settingValue')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(settingKey, settingValue);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is DriftUserSetting &&
          other.settingKey == this.settingKey &&
          other.settingValue == this.settingValue);
}

class UserSettingsCompanion extends UpdateCompanion<DriftUserSetting> {
  final Value<String> settingKey;
  final Value<String> settingValue;
  final Value<int> rowid;
  const UserSettingsCompanion({
    this.settingKey = const Value.absent(),
    this.settingValue = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  UserSettingsCompanion.insert({
    required String settingKey,
    required String settingValue,
    this.rowid = const Value.absent(),
  })  : settingKey = Value(settingKey),
        settingValue = Value(settingValue);
  static Insertable<DriftUserSetting> custom({
    Expression<String>? settingKey,
    Expression<String>? settingValue,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (settingKey != null) 'key': settingKey,
      if (settingValue != null) 'value': settingValue,
      if (rowid != null) 'rowid': rowid,
    });
  }

  UserSettingsCompanion copyWith(
      {Value<String>? settingKey,
      Value<String>? settingValue,
      Value<int>? rowid}) {
    return UserSettingsCompanion(
      settingKey: settingKey ?? this.settingKey,
      settingValue: settingValue ?? this.settingValue,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (settingKey.present) {
      map['key'] = Variable<String>(settingKey.value);
    }
    if (settingValue.present) {
      map['value'] = Variable<String>(settingValue.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('UserSettingsCompanion(')
          ..write('settingKey: $settingKey, ')
          ..write('settingValue: $settingValue, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ChatMetadatasTable extends ChatMetadatas
    with TableInfo<$ChatMetadatasTable, DriftChatMetadata> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ChatMetadatasTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _chatIdMeta = const VerificationMeta('chatId');
  @override
  late final GeneratedColumn<String> chatId = GeneratedColumn<String>(
      'chat_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  late final GeneratedColumnWithTypeConverter<Map<String, dynamic>, String>
      metadata = GeneratedColumn<String>('metadata', aliasedName, false,
              type: DriftSqlType.string, requiredDuringInsert: true)
          .withConverter<Map<String, dynamic>>(
              $ChatMetadatasTable.$convertermetadata);
  @override
  List<GeneratedColumn> get $columns => [chatId, metadata];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'chat_metadatas';
  @override
  VerificationContext validateIntegrity(Insertable<DriftChatMetadata> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('chat_id')) {
      context.handle(_chatIdMeta,
          chatId.isAcceptableOrUnknown(data['chat_id']!, _chatIdMeta));
    } else if (isInserting) {
      context.missing(_chatIdMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {chatId};
  @override
  DriftChatMetadata map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return DriftChatMetadata(
      chatId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chat_id'])!,
      metadata: $ChatMetadatasTable.$convertermetadata.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}metadata'])!),
    );
  }

  @override
  $ChatMetadatasTable createAlias(String alias) {
    return $ChatMetadatasTable(attachedDatabase, alias);
  }

  static TypeConverter<Map<String, dynamic>, String> $convertermetadata =
      const JsonMapConverter();
}

class DriftChatMetadata extends DataClass
    implements Insertable<DriftChatMetadata> {
  final String chatId;
  final Map<String, dynamic> metadata;
  const DriftChatMetadata({required this.chatId, required this.metadata});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['chat_id'] = Variable<String>(chatId);
    {
      map['metadata'] = Variable<String>(
          $ChatMetadatasTable.$convertermetadata.toSql(metadata));
    }
    return map;
  }

  ChatMetadatasCompanion toCompanion(bool nullToAbsent) {
    return ChatMetadatasCompanion(
      chatId: Value(chatId),
      metadata: Value(metadata),
    );
  }

  factory DriftChatMetadata.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return DriftChatMetadata(
      chatId: serializer.fromJson<String>(json['chatId']),
      metadata: serializer.fromJson<Map<String, dynamic>>(json['metadata']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'chatId': serializer.toJson<String>(chatId),
      'metadata': serializer.toJson<Map<String, dynamic>>(metadata),
    };
  }

  DriftChatMetadata copyWith(
          {String? chatId, Map<String, dynamic>? metadata}) =>
      DriftChatMetadata(
        chatId: chatId ?? this.chatId,
        metadata: metadata ?? this.metadata,
      );
  DriftChatMetadata copyWithCompanion(ChatMetadatasCompanion data) {
    return DriftChatMetadata(
      chatId: data.chatId.present ? data.chatId.value : this.chatId,
      metadata: data.metadata.present ? data.metadata.value : this.metadata,
    );
  }

  @override
  String toString() {
    return (StringBuffer('DriftChatMetadata(')
          ..write('chatId: $chatId, ')
          ..write('metadata: $metadata')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(chatId, metadata);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is DriftChatMetadata &&
          other.chatId == this.chatId &&
          other.metadata == this.metadata);
}

class ChatMetadatasCompanion extends UpdateCompanion<DriftChatMetadata> {
  final Value<String> chatId;
  final Value<Map<String, dynamic>> metadata;
  final Value<int> rowid;
  const ChatMetadatasCompanion({
    this.chatId = const Value.absent(),
    this.metadata = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ChatMetadatasCompanion.insert({
    required String chatId,
    required Map<String, dynamic> metadata,
    this.rowid = const Value.absent(),
  })  : chatId = Value(chatId),
        metadata = Value(metadata);
  static Insertable<DriftChatMetadata> custom({
    Expression<String>? chatId,
    Expression<String>? metadata,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (chatId != null) 'chat_id': chatId,
      if (metadata != null) 'metadata': metadata,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ChatMetadatasCompanion copyWith(
      {Value<String>? chatId,
      Value<Map<String, dynamic>>? metadata,
      Value<int>? rowid}) {
    return ChatMetadatasCompanion(
      chatId: chatId ?? this.chatId,
      metadata: metadata ?? this.metadata,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (chatId.present) {
      map['chat_id'] = Variable<String>(chatId.value);
    }
    if (metadata.present) {
      map['metadata'] = Variable<String>(
          $ChatMetadatasTable.$convertermetadata.toSql(metadata.value));
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ChatMetadatasCompanion(')
          ..write('chatId: $chatId, ')
          ..write('metadata: $metadata, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $UnreadCountsTable extends UnreadCounts
    with TableInfo<$UnreadCountsTable, DriftUnreadCount> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $UnreadCountsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _chatIdMeta = const VerificationMeta('chatId');
  @override
  late final GeneratedColumn<String> chatId = GeneratedColumn<String>(
      'chat_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _countMeta = const VerificationMeta('count');
  @override
  late final GeneratedColumn<int> count = GeneratedColumn<int>(
      'count', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [chatId, count];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'unread_counts';
  @override
  VerificationContext validateIntegrity(Insertable<DriftUnreadCount> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('chat_id')) {
      context.handle(_chatIdMeta,
          chatId.isAcceptableOrUnknown(data['chat_id']!, _chatIdMeta));
    } else if (isInserting) {
      context.missing(_chatIdMeta);
    }
    if (data.containsKey('count')) {
      context.handle(
          _countMeta, count.isAcceptableOrUnknown(data['count']!, _countMeta));
    } else if (isInserting) {
      context.missing(_countMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {chatId};
  @override
  DriftUnreadCount map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return DriftUnreadCount(
      chatId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chat_id'])!,
      count: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}count'])!,
    );
  }

  @override
  $UnreadCountsTable createAlias(String alias) {
    return $UnreadCountsTable(attachedDatabase, alias);
  }
}

class DriftUnreadCount extends DataClass
    implements Insertable<DriftUnreadCount> {
  final String chatId;
  final int count;
  const DriftUnreadCount({required this.chatId, required this.count});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['chat_id'] = Variable<String>(chatId);
    map['count'] = Variable<int>(count);
    return map;
  }

  UnreadCountsCompanion toCompanion(bool nullToAbsent) {
    return UnreadCountsCompanion(
      chatId: Value(chatId),
      count: Value(count),
    );
  }

  factory DriftUnreadCount.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return DriftUnreadCount(
      chatId: serializer.fromJson<String>(json['chatId']),
      count: serializer.fromJson<int>(json['count']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'chatId': serializer.toJson<String>(chatId),
      'count': serializer.toJson<int>(count),
    };
  }

  DriftUnreadCount copyWith({String? chatId, int? count}) => DriftUnreadCount(
        chatId: chatId ?? this.chatId,
        count: count ?? this.count,
      );
  DriftUnreadCount copyWithCompanion(UnreadCountsCompanion data) {
    return DriftUnreadCount(
      chatId: data.chatId.present ? data.chatId.value : this.chatId,
      count: data.count.present ? data.count.value : this.count,
    );
  }

  @override
  String toString() {
    return (StringBuffer('DriftUnreadCount(')
          ..write('chatId: $chatId, ')
          ..write('count: $count')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(chatId, count);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is DriftUnreadCount &&
          other.chatId == this.chatId &&
          other.count == this.count);
}

class UnreadCountsCompanion extends UpdateCompanion<DriftUnreadCount> {
  final Value<String> chatId;
  final Value<int> count;
  final Value<int> rowid;
  const UnreadCountsCompanion({
    this.chatId = const Value.absent(),
    this.count = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  UnreadCountsCompanion.insert({
    required String chatId,
    required int count,
    this.rowid = const Value.absent(),
  })  : chatId = Value(chatId),
        count = Value(count);
  static Insertable<DriftUnreadCount> custom({
    Expression<String>? chatId,
    Expression<int>? count,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (chatId != null) 'chat_id': chatId,
      if (count != null) 'count': count,
      if (rowid != null) 'rowid': rowid,
    });
  }

  UnreadCountsCompanion copyWith(
      {Value<String>? chatId, Value<int>? count, Value<int>? rowid}) {
    return UnreadCountsCompanion(
      chatId: chatId ?? this.chatId,
      count: count ?? this.count,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (chatId.present) {
      map['chat_id'] = Variable<String>(chatId.value);
    }
    if (count.present) {
      map['count'] = Variable<int>(count.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('UnreadCountsCompanion(')
          ..write('chatId: $chatId, ')
          ..write('count: $count, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $DraftMessagesTable extends DraftMessages
    with TableInfo<$DraftMessagesTable, DriftDraftMessage> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $DraftMessagesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _chatIdMeta = const VerificationMeta('chatId');
  @override
  late final GeneratedColumn<String> chatId = GeneratedColumn<String>(
      'chat_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _draftTextMeta =
      const VerificationMeta('draftText');
  @override
  late final GeneratedColumn<String> draftText = GeneratedColumn<String>(
      'draft_text', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  @override
  List<GeneratedColumn> get $columns => [chatId, draftText];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'draft_messages';
  @override
  VerificationContext validateIntegrity(Insertable<DriftDraftMessage> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('chat_id')) {
      context.handle(_chatIdMeta,
          chatId.isAcceptableOrUnknown(data['chat_id']!, _chatIdMeta));
    } else if (isInserting) {
      context.missing(_chatIdMeta);
    }
    if (data.containsKey('draft_text')) {
      context.handle(_draftTextMeta,
          draftText.isAcceptableOrUnknown(data['draft_text']!, _draftTextMeta));
    } else if (isInserting) {
      context.missing(_draftTextMeta);
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {chatId};
  @override
  DriftDraftMessage map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return DriftDraftMessage(
      chatId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}chat_id'])!,
      draftText: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}draft_text'])!,
    );
  }

  @override
  $DraftMessagesTable createAlias(String alias) {
    return $DraftMessagesTable(attachedDatabase, alias);
  }
}

class DriftDraftMessage extends DataClass
    implements Insertable<DriftDraftMessage> {
  final String chatId;
  final String draftText;
  const DriftDraftMessage({required this.chatId, required this.draftText});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['chat_id'] = Variable<String>(chatId);
    map['draft_text'] = Variable<String>(draftText);
    return map;
  }

  DraftMessagesCompanion toCompanion(bool nullToAbsent) {
    return DraftMessagesCompanion(
      chatId: Value(chatId),
      draftText: Value(draftText),
    );
  }

  factory DriftDraftMessage.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return DriftDraftMessage(
      chatId: serializer.fromJson<String>(json['chatId']),
      draftText: serializer.fromJson<String>(json['draftText']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'chatId': serializer.toJson<String>(chatId),
      'draftText': serializer.toJson<String>(draftText),
    };
  }

  DriftDraftMessage copyWith({String? chatId, String? draftText}) =>
      DriftDraftMessage(
        chatId: chatId ?? this.chatId,
        draftText: draftText ?? this.draftText,
      );
  DriftDraftMessage copyWithCompanion(DraftMessagesCompanion data) {
    return DriftDraftMessage(
      chatId: data.chatId.present ? data.chatId.value : this.chatId,
      draftText: data.draftText.present ? data.draftText.value : this.draftText,
    );
  }

  @override
  String toString() {
    return (StringBuffer('DriftDraftMessage(')
          ..write('chatId: $chatId, ')
          ..write('draftText: $draftText')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(chatId, draftText);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is DriftDraftMessage &&
          other.chatId == this.chatId &&
          other.draftText == this.draftText);
}

class DraftMessagesCompanion extends UpdateCompanion<DriftDraftMessage> {
  final Value<String> chatId;
  final Value<String> draftText;
  final Value<int> rowid;
  const DraftMessagesCompanion({
    this.chatId = const Value.absent(),
    this.draftText = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  DraftMessagesCompanion.insert({
    required String chatId,
    required String draftText,
    this.rowid = const Value.absent(),
  })  : chatId = Value(chatId),
        draftText = Value(draftText);
  static Insertable<DriftDraftMessage> custom({
    Expression<String>? chatId,
    Expression<String>? draftText,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (chatId != null) 'chat_id': chatId,
      if (draftText != null) 'draft_text': draftText,
      if (rowid != null) 'rowid': rowid,
    });
  }

  DraftMessagesCompanion copyWith(
      {Value<String>? chatId, Value<String>? draftText, Value<int>? rowid}) {
    return DraftMessagesCompanion(
      chatId: chatId ?? this.chatId,
      draftText: draftText ?? this.draftText,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (chatId.present) {
      map['chat_id'] = Variable<String>(chatId.value);
    }
    if (draftText.present) {
      map['draft_text'] = Variable<String>(draftText.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('DraftMessagesCompanion(')
          ..write('chatId: $chatId, ')
          ..write('draftText: $draftText, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $FcmMessagesTable extends FcmMessages
    with TableInfo<$FcmMessagesTable, DriftFcmMessage> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $FcmMessagesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<int> id = GeneratedColumn<int>(
      'id', aliasedName, false,
      hasAutoIncrement: true,
      type: DriftSqlType.int,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('PRIMARY KEY AUTOINCREMENT'));
  static const VerificationMeta _messageIdMeta =
      const VerificationMeta('messageId');
  @override
  late final GeneratedColumn<String> messageId = GeneratedColumn<String>(
      'message_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  late final GeneratedColumnWithTypeConverter<Map<String, dynamic>, String>
      data = GeneratedColumn<String>('data', aliasedName, false,
              type: DriftSqlType.string, requiredDuringInsert: true)
          .withConverter<Map<String, dynamic>>(
              $FcmMessagesTable.$converterdata);
  static const VerificationMeta _receivedTimestampMeta =
      const VerificationMeta('receivedTimestamp');
  @override
  late final GeneratedColumn<DateTime> receivedTimestamp =
      GeneratedColumn<DateTime>('timestamp', aliasedName, false,
          type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _notificationTitleMeta =
      const VerificationMeta('notificationTitle');
  @override
  late final GeneratedColumn<String> notificationTitle =
      GeneratedColumn<String>('notification_title', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _notificationBodyMeta =
      const VerificationMeta('notificationBody');
  @override
  late final GeneratedColumn<String> notificationBody = GeneratedColumn<String>(
      'notification_body', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        messageId,
        data,
        receivedTimestamp,
        notificationTitle,
        notificationBody
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'fcm_messages';
  @override
  VerificationContext validateIntegrity(Insertable<DriftFcmMessage> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('message_id')) {
      context.handle(_messageIdMeta,
          messageId.isAcceptableOrUnknown(data['message_id']!, _messageIdMeta));
    }
    if (data.containsKey('timestamp')) {
      context.handle(
          _receivedTimestampMeta,
          receivedTimestamp.isAcceptableOrUnknown(
              data['timestamp']!, _receivedTimestampMeta));
    } else if (isInserting) {
      context.missing(_receivedTimestampMeta);
    }
    if (data.containsKey('notification_title')) {
      context.handle(
          _notificationTitleMeta,
          notificationTitle.isAcceptableOrUnknown(
              data['notification_title']!, _notificationTitleMeta));
    }
    if (data.containsKey('notification_body')) {
      context.handle(
          _notificationBodyMeta,
          notificationBody.isAcceptableOrUnknown(
              data['notification_body']!, _notificationBodyMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  DriftFcmMessage map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return DriftFcmMessage(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}id'])!,
      messageId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}message_id']),
      data: $FcmMessagesTable.$converterdata.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}data'])!),
      receivedTimestamp: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}timestamp'])!,
      notificationTitle: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}notification_title']),
      notificationBody: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}notification_body']),
    );
  }

  @override
  $FcmMessagesTable createAlias(String alias) {
    return $FcmMessagesTable(attachedDatabase, alias);
  }

  static TypeConverter<Map<String, dynamic>, String> $converterdata =
      const JsonMapConverter();
}

class DriftFcmMessage extends DataClass implements Insertable<DriftFcmMessage> {
  final int id;
  final String? messageId;
  final Map<String, dynamic> data;
  final DateTime receivedTimestamp;
  final String? notificationTitle;
  final String? notificationBody;
  const DriftFcmMessage(
      {required this.id,
      this.messageId,
      required this.data,
      required this.receivedTimestamp,
      this.notificationTitle,
      this.notificationBody});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<int>(id);
    if (!nullToAbsent || messageId != null) {
      map['message_id'] = Variable<String>(messageId);
    }
    {
      map['data'] =
          Variable<String>($FcmMessagesTable.$converterdata.toSql(data));
    }
    map['timestamp'] = Variable<DateTime>(receivedTimestamp);
    if (!nullToAbsent || notificationTitle != null) {
      map['notification_title'] = Variable<String>(notificationTitle);
    }
    if (!nullToAbsent || notificationBody != null) {
      map['notification_body'] = Variable<String>(notificationBody);
    }
    return map;
  }

  FcmMessagesCompanion toCompanion(bool nullToAbsent) {
    return FcmMessagesCompanion(
      id: Value(id),
      messageId: messageId == null && nullToAbsent
          ? const Value.absent()
          : Value(messageId),
      data: Value(data),
      receivedTimestamp: Value(receivedTimestamp),
      notificationTitle: notificationTitle == null && nullToAbsent
          ? const Value.absent()
          : Value(notificationTitle),
      notificationBody: notificationBody == null && nullToAbsent
          ? const Value.absent()
          : Value(notificationBody),
    );
  }

  factory DriftFcmMessage.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return DriftFcmMessage(
      id: serializer.fromJson<int>(json['id']),
      messageId: serializer.fromJson<String?>(json['messageId']),
      data: serializer.fromJson<Map<String, dynamic>>(json['data']),
      receivedTimestamp:
          serializer.fromJson<DateTime>(json['receivedTimestamp']),
      notificationTitle:
          serializer.fromJson<String?>(json['notificationTitle']),
      notificationBody: serializer.fromJson<String?>(json['notificationBody']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<int>(id),
      'messageId': serializer.toJson<String?>(messageId),
      'data': serializer.toJson<Map<String, dynamic>>(data),
      'receivedTimestamp': serializer.toJson<DateTime>(receivedTimestamp),
      'notificationTitle': serializer.toJson<String?>(notificationTitle),
      'notificationBody': serializer.toJson<String?>(notificationBody),
    };
  }

  DriftFcmMessage copyWith(
          {int? id,
          Value<String?> messageId = const Value.absent(),
          Map<String, dynamic>? data,
          DateTime? receivedTimestamp,
          Value<String?> notificationTitle = const Value.absent(),
          Value<String?> notificationBody = const Value.absent()}) =>
      DriftFcmMessage(
        id: id ?? this.id,
        messageId: messageId.present ? messageId.value : this.messageId,
        data: data ?? this.data,
        receivedTimestamp: receivedTimestamp ?? this.receivedTimestamp,
        notificationTitle: notificationTitle.present
            ? notificationTitle.value
            : this.notificationTitle,
        notificationBody: notificationBody.present
            ? notificationBody.value
            : this.notificationBody,
      );
  DriftFcmMessage copyWithCompanion(FcmMessagesCompanion data) {
    return DriftFcmMessage(
      id: data.id.present ? data.id.value : this.id,
      messageId: data.messageId.present ? data.messageId.value : this.messageId,
      data: data.data.present ? data.data.value : this.data,
      receivedTimestamp: data.receivedTimestamp.present
          ? data.receivedTimestamp.value
          : this.receivedTimestamp,
      notificationTitle: data.notificationTitle.present
          ? data.notificationTitle.value
          : this.notificationTitle,
      notificationBody: data.notificationBody.present
          ? data.notificationBody.value
          : this.notificationBody,
    );
  }

  @override
  String toString() {
    return (StringBuffer('DriftFcmMessage(')
          ..write('id: $id, ')
          ..write('messageId: $messageId, ')
          ..write('data: $data, ')
          ..write('receivedTimestamp: $receivedTimestamp, ')
          ..write('notificationTitle: $notificationTitle, ')
          ..write('notificationBody: $notificationBody')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, messageId, data, receivedTimestamp,
      notificationTitle, notificationBody);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is DriftFcmMessage &&
          other.id == this.id &&
          other.messageId == this.messageId &&
          other.data == this.data &&
          other.receivedTimestamp == this.receivedTimestamp &&
          other.notificationTitle == this.notificationTitle &&
          other.notificationBody == this.notificationBody);
}

class FcmMessagesCompanion extends UpdateCompanion<DriftFcmMessage> {
  final Value<int> id;
  final Value<String?> messageId;
  final Value<Map<String, dynamic>> data;
  final Value<DateTime> receivedTimestamp;
  final Value<String?> notificationTitle;
  final Value<String?> notificationBody;
  const FcmMessagesCompanion({
    this.id = const Value.absent(),
    this.messageId = const Value.absent(),
    this.data = const Value.absent(),
    this.receivedTimestamp = const Value.absent(),
    this.notificationTitle = const Value.absent(),
    this.notificationBody = const Value.absent(),
  });
  FcmMessagesCompanion.insert({
    this.id = const Value.absent(),
    this.messageId = const Value.absent(),
    required Map<String, dynamic> data,
    required DateTime receivedTimestamp,
    this.notificationTitle = const Value.absent(),
    this.notificationBody = const Value.absent(),
  })  : data = Value(data),
        receivedTimestamp = Value(receivedTimestamp);
  static Insertable<DriftFcmMessage> custom({
    Expression<int>? id,
    Expression<String>? messageId,
    Expression<String>? data,
    Expression<DateTime>? receivedTimestamp,
    Expression<String>? notificationTitle,
    Expression<String>? notificationBody,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (messageId != null) 'message_id': messageId,
      if (data != null) 'data': data,
      if (receivedTimestamp != null) 'timestamp': receivedTimestamp,
      if (notificationTitle != null) 'notification_title': notificationTitle,
      if (notificationBody != null) 'notification_body': notificationBody,
    });
  }

  FcmMessagesCompanion copyWith(
      {Value<int>? id,
      Value<String?>? messageId,
      Value<Map<String, dynamic>>? data,
      Value<DateTime>? receivedTimestamp,
      Value<String?>? notificationTitle,
      Value<String?>? notificationBody}) {
    return FcmMessagesCompanion(
      id: id ?? this.id,
      messageId: messageId ?? this.messageId,
      data: data ?? this.data,
      receivedTimestamp: receivedTimestamp ?? this.receivedTimestamp,
      notificationTitle: notificationTitle ?? this.notificationTitle,
      notificationBody: notificationBody ?? this.notificationBody,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<int>(id.value);
    }
    if (messageId.present) {
      map['message_id'] = Variable<String>(messageId.value);
    }
    if (data.present) {
      map['data'] =
          Variable<String>($FcmMessagesTable.$converterdata.toSql(data.value));
    }
    if (receivedTimestamp.present) {
      map['timestamp'] = Variable<DateTime>(receivedTimestamp.value);
    }
    if (notificationTitle.present) {
      map['notification_title'] = Variable<String>(notificationTitle.value);
    }
    if (notificationBody.present) {
      map['notification_body'] = Variable<String>(notificationBody.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('FcmMessagesCompanion(')
          ..write('id: $id, ')
          ..write('messageId: $messageId, ')
          ..write('data: $data, ')
          ..write('receivedTimestamp: $receivedTimestamp, ')
          ..write('notificationTitle: $notificationTitle, ')
          ..write('notificationBody: $notificationBody')
          ..write(')'))
        .toString();
  }
}

class $ProfilePicturesTable extends ProfilePictures
    with TableInfo<$ProfilePicturesTable, DriftProfilePicture> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ProfilePicturesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _userIdMeta = const VerificationMeta('userId');
  @override
  late final GeneratedColumn<String> userId = GeneratedColumn<String>(
      'user_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _imageUrlMeta =
      const VerificationMeta('imageUrl');
  @override
  late final GeneratedColumn<String> imageUrl = GeneratedColumn<String>(
      'image_url', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _localPathMeta =
      const VerificationMeta('localPath');
  @override
  late final GeneratedColumn<String> localPath = GeneratedColumn<String>(
      'local_path', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _thumbnailPathMeta =
      const VerificationMeta('thumbnailPath');
  @override
  late final GeneratedColumn<String> thumbnailPath = GeneratedColumn<String>(
      'thumbnail_path', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _fileSizeMeta =
      const VerificationMeta('fileSize');
  @override
  late final GeneratedColumn<int> fileSize = GeneratedColumn<int>(
      'file_size', aliasedName, false,
      type: DriftSqlType.int, requiredDuringInsert: true);
  static const VerificationMeta _formatMeta = const VerificationMeta('format');
  @override
  late final GeneratedColumn<String> format = GeneratedColumn<String>(
      'format', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _cachedAtMeta =
      const VerificationMeta('cachedAt');
  @override
  late final GeneratedColumn<DateTime> cachedAt = GeneratedColumn<DateTime>(
      'cached_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _lastAccessedMeta =
      const VerificationMeta('lastAccessed');
  @override
  late final GeneratedColumn<DateTime> lastAccessed = GeneratedColumn<DateTime>(
      'last_accessed', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _isActiveMeta =
      const VerificationMeta('isActive');
  @override
  late final GeneratedColumn<bool> isActive = GeneratedColumn<bool>(
      'is_active', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_active" IN (0, 1))'),
      defaultValue: const Constant(true));
  @override
  List<GeneratedColumn> get $columns => [
        userId,
        imageUrl,
        localPath,
        thumbnailPath,
        fileSize,
        format,
        cachedAt,
        lastAccessed,
        isActive
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'profile_pictures';
  @override
  VerificationContext validateIntegrity(
      Insertable<DriftProfilePicture> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('user_id')) {
      context.handle(_userIdMeta,
          userId.isAcceptableOrUnknown(data['user_id']!, _userIdMeta));
    } else if (isInserting) {
      context.missing(_userIdMeta);
    }
    if (data.containsKey('image_url')) {
      context.handle(_imageUrlMeta,
          imageUrl.isAcceptableOrUnknown(data['image_url']!, _imageUrlMeta));
    } else if (isInserting) {
      context.missing(_imageUrlMeta);
    }
    if (data.containsKey('local_path')) {
      context.handle(_localPathMeta,
          localPath.isAcceptableOrUnknown(data['local_path']!, _localPathMeta));
    } else if (isInserting) {
      context.missing(_localPathMeta);
    }
    if (data.containsKey('thumbnail_path')) {
      context.handle(
          _thumbnailPathMeta,
          thumbnailPath.isAcceptableOrUnknown(
              data['thumbnail_path']!, _thumbnailPathMeta));
    }
    if (data.containsKey('file_size')) {
      context.handle(_fileSizeMeta,
          fileSize.isAcceptableOrUnknown(data['file_size']!, _fileSizeMeta));
    } else if (isInserting) {
      context.missing(_fileSizeMeta);
    }
    if (data.containsKey('format')) {
      context.handle(_formatMeta,
          format.isAcceptableOrUnknown(data['format']!, _formatMeta));
    } else if (isInserting) {
      context.missing(_formatMeta);
    }
    if (data.containsKey('cached_at')) {
      context.handle(_cachedAtMeta,
          cachedAt.isAcceptableOrUnknown(data['cached_at']!, _cachedAtMeta));
    } else if (isInserting) {
      context.missing(_cachedAtMeta);
    }
    if (data.containsKey('last_accessed')) {
      context.handle(
          _lastAccessedMeta,
          lastAccessed.isAcceptableOrUnknown(
              data['last_accessed']!, _lastAccessedMeta));
    } else if (isInserting) {
      context.missing(_lastAccessedMeta);
    }
    if (data.containsKey('is_active')) {
      context.handle(_isActiveMeta,
          isActive.isAcceptableOrUnknown(data['is_active']!, _isActiveMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {userId};
  @override
  DriftProfilePicture map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return DriftProfilePicture(
      userId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}user_id'])!,
      imageUrl: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}image_url'])!,
      localPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}local_path'])!,
      thumbnailPath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}thumbnail_path']),
      fileSize: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}file_size'])!,
      format: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}format'])!,
      cachedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}cached_at'])!,
      lastAccessed: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}last_accessed'])!,
      isActive: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_active'])!,
    );
  }

  @override
  $ProfilePicturesTable createAlias(String alias) {
    return $ProfilePicturesTable(attachedDatabase, alias);
  }
}

class DriftProfilePicture extends DataClass
    implements Insertable<DriftProfilePicture> {
  final String userId;
  final String imageUrl;
  final String localPath;
  final String? thumbnailPath;
  final int fileSize;
  final String format;
  final DateTime cachedAt;
  final DateTime lastAccessed;
  final bool isActive;
  const DriftProfilePicture(
      {required this.userId,
      required this.imageUrl,
      required this.localPath,
      this.thumbnailPath,
      required this.fileSize,
      required this.format,
      required this.cachedAt,
      required this.lastAccessed,
      required this.isActive});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['user_id'] = Variable<String>(userId);
    map['image_url'] = Variable<String>(imageUrl);
    map['local_path'] = Variable<String>(localPath);
    if (!nullToAbsent || thumbnailPath != null) {
      map['thumbnail_path'] = Variable<String>(thumbnailPath);
    }
    map['file_size'] = Variable<int>(fileSize);
    map['format'] = Variable<String>(format);
    map['cached_at'] = Variable<DateTime>(cachedAt);
    map['last_accessed'] = Variable<DateTime>(lastAccessed);
    map['is_active'] = Variable<bool>(isActive);
    return map;
  }

  ProfilePicturesCompanion toCompanion(bool nullToAbsent) {
    return ProfilePicturesCompanion(
      userId: Value(userId),
      imageUrl: Value(imageUrl),
      localPath: Value(localPath),
      thumbnailPath: thumbnailPath == null && nullToAbsent
          ? const Value.absent()
          : Value(thumbnailPath),
      fileSize: Value(fileSize),
      format: Value(format),
      cachedAt: Value(cachedAt),
      lastAccessed: Value(lastAccessed),
      isActive: Value(isActive),
    );
  }

  factory DriftProfilePicture.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return DriftProfilePicture(
      userId: serializer.fromJson<String>(json['userId']),
      imageUrl: serializer.fromJson<String>(json['imageUrl']),
      localPath: serializer.fromJson<String>(json['localPath']),
      thumbnailPath: serializer.fromJson<String?>(json['thumbnailPath']),
      fileSize: serializer.fromJson<int>(json['fileSize']),
      format: serializer.fromJson<String>(json['format']),
      cachedAt: serializer.fromJson<DateTime>(json['cachedAt']),
      lastAccessed: serializer.fromJson<DateTime>(json['lastAccessed']),
      isActive: serializer.fromJson<bool>(json['isActive']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'userId': serializer.toJson<String>(userId),
      'imageUrl': serializer.toJson<String>(imageUrl),
      'localPath': serializer.toJson<String>(localPath),
      'thumbnailPath': serializer.toJson<String?>(thumbnailPath),
      'fileSize': serializer.toJson<int>(fileSize),
      'format': serializer.toJson<String>(format),
      'cachedAt': serializer.toJson<DateTime>(cachedAt),
      'lastAccessed': serializer.toJson<DateTime>(lastAccessed),
      'isActive': serializer.toJson<bool>(isActive),
    };
  }

  DriftProfilePicture copyWith(
          {String? userId,
          String? imageUrl,
          String? localPath,
          Value<String?> thumbnailPath = const Value.absent(),
          int? fileSize,
          String? format,
          DateTime? cachedAt,
          DateTime? lastAccessed,
          bool? isActive}) =>
      DriftProfilePicture(
        userId: userId ?? this.userId,
        imageUrl: imageUrl ?? this.imageUrl,
        localPath: localPath ?? this.localPath,
        thumbnailPath:
            thumbnailPath.present ? thumbnailPath.value : this.thumbnailPath,
        fileSize: fileSize ?? this.fileSize,
        format: format ?? this.format,
        cachedAt: cachedAt ?? this.cachedAt,
        lastAccessed: lastAccessed ?? this.lastAccessed,
        isActive: isActive ?? this.isActive,
      );
  DriftProfilePicture copyWithCompanion(ProfilePicturesCompanion data) {
    return DriftProfilePicture(
      userId: data.userId.present ? data.userId.value : this.userId,
      imageUrl: data.imageUrl.present ? data.imageUrl.value : this.imageUrl,
      localPath: data.localPath.present ? data.localPath.value : this.localPath,
      thumbnailPath: data.thumbnailPath.present
          ? data.thumbnailPath.value
          : this.thumbnailPath,
      fileSize: data.fileSize.present ? data.fileSize.value : this.fileSize,
      format: data.format.present ? data.format.value : this.format,
      cachedAt: data.cachedAt.present ? data.cachedAt.value : this.cachedAt,
      lastAccessed: data.lastAccessed.present
          ? data.lastAccessed.value
          : this.lastAccessed,
      isActive: data.isActive.present ? data.isActive.value : this.isActive,
    );
  }

  @override
  String toString() {
    return (StringBuffer('DriftProfilePicture(')
          ..write('userId: $userId, ')
          ..write('imageUrl: $imageUrl, ')
          ..write('localPath: $localPath, ')
          ..write('thumbnailPath: $thumbnailPath, ')
          ..write('fileSize: $fileSize, ')
          ..write('format: $format, ')
          ..write('cachedAt: $cachedAt, ')
          ..write('lastAccessed: $lastAccessed, ')
          ..write('isActive: $isActive')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(userId, imageUrl, localPath, thumbnailPath,
      fileSize, format, cachedAt, lastAccessed, isActive);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is DriftProfilePicture &&
          other.userId == this.userId &&
          other.imageUrl == this.imageUrl &&
          other.localPath == this.localPath &&
          other.thumbnailPath == this.thumbnailPath &&
          other.fileSize == this.fileSize &&
          other.format == this.format &&
          other.cachedAt == this.cachedAt &&
          other.lastAccessed == this.lastAccessed &&
          other.isActive == this.isActive);
}

class ProfilePicturesCompanion extends UpdateCompanion<DriftProfilePicture> {
  final Value<String> userId;
  final Value<String> imageUrl;
  final Value<String> localPath;
  final Value<String?> thumbnailPath;
  final Value<int> fileSize;
  final Value<String> format;
  final Value<DateTime> cachedAt;
  final Value<DateTime> lastAccessed;
  final Value<bool> isActive;
  final Value<int> rowid;
  const ProfilePicturesCompanion({
    this.userId = const Value.absent(),
    this.imageUrl = const Value.absent(),
    this.localPath = const Value.absent(),
    this.thumbnailPath = const Value.absent(),
    this.fileSize = const Value.absent(),
    this.format = const Value.absent(),
    this.cachedAt = const Value.absent(),
    this.lastAccessed = const Value.absent(),
    this.isActive = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ProfilePicturesCompanion.insert({
    required String userId,
    required String imageUrl,
    required String localPath,
    this.thumbnailPath = const Value.absent(),
    required int fileSize,
    required String format,
    required DateTime cachedAt,
    required DateTime lastAccessed,
    this.isActive = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : userId = Value(userId),
        imageUrl = Value(imageUrl),
        localPath = Value(localPath),
        fileSize = Value(fileSize),
        format = Value(format),
        cachedAt = Value(cachedAt),
        lastAccessed = Value(lastAccessed);
  static Insertable<DriftProfilePicture> custom({
    Expression<String>? userId,
    Expression<String>? imageUrl,
    Expression<String>? localPath,
    Expression<String>? thumbnailPath,
    Expression<int>? fileSize,
    Expression<String>? format,
    Expression<DateTime>? cachedAt,
    Expression<DateTime>? lastAccessed,
    Expression<bool>? isActive,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (userId != null) 'user_id': userId,
      if (imageUrl != null) 'image_url': imageUrl,
      if (localPath != null) 'local_path': localPath,
      if (thumbnailPath != null) 'thumbnail_path': thumbnailPath,
      if (fileSize != null) 'file_size': fileSize,
      if (format != null) 'format': format,
      if (cachedAt != null) 'cached_at': cachedAt,
      if (lastAccessed != null) 'last_accessed': lastAccessed,
      if (isActive != null) 'is_active': isActive,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ProfilePicturesCompanion copyWith(
      {Value<String>? userId,
      Value<String>? imageUrl,
      Value<String>? localPath,
      Value<String?>? thumbnailPath,
      Value<int>? fileSize,
      Value<String>? format,
      Value<DateTime>? cachedAt,
      Value<DateTime>? lastAccessed,
      Value<bool>? isActive,
      Value<int>? rowid}) {
    return ProfilePicturesCompanion(
      userId: userId ?? this.userId,
      imageUrl: imageUrl ?? this.imageUrl,
      localPath: localPath ?? this.localPath,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      fileSize: fileSize ?? this.fileSize,
      format: format ?? this.format,
      cachedAt: cachedAt ?? this.cachedAt,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      isActive: isActive ?? this.isActive,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (userId.present) {
      map['user_id'] = Variable<String>(userId.value);
    }
    if (imageUrl.present) {
      map['image_url'] = Variable<String>(imageUrl.value);
    }
    if (localPath.present) {
      map['local_path'] = Variable<String>(localPath.value);
    }
    if (thumbnailPath.present) {
      map['thumbnail_path'] = Variable<String>(thumbnailPath.value);
    }
    if (fileSize.present) {
      map['file_size'] = Variable<int>(fileSize.value);
    }
    if (format.present) {
      map['format'] = Variable<String>(format.value);
    }
    if (cachedAt.present) {
      map['cached_at'] = Variable<DateTime>(cachedAt.value);
    }
    if (lastAccessed.present) {
      map['last_accessed'] = Variable<DateTime>(lastAccessed.value);
    }
    if (isActive.present) {
      map['is_active'] = Variable<bool>(isActive.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ProfilePicturesCompanion(')
          ..write('userId: $userId, ')
          ..write('imageUrl: $imageUrl, ')
          ..write('localPath: $localPath, ')
          ..write('thumbnailPath: $thumbnailPath, ')
          ..write('fileSize: $fileSize, ')
          ..write('format: $format, ')
          ..write('cachedAt: $cachedAt, ')
          ..write('lastAccessed: $lastAccessed, ')
          ..write('isActive: $isActive, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ContactRequestsTable extends ContactRequests
    with TableInfo<$ContactRequestsTable, DriftContactRequest> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ContactRequestsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _requesterIdMeta =
      const VerificationMeta('requesterId');
  @override
  late final GeneratedColumn<String> requesterId = GeneratedColumn<String>(
      'requester_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _recipientIdMeta =
      const VerificationMeta('recipientId');
  @override
  late final GeneratedColumn<String> recipientId = GeneratedColumn<String>(
      'recipient_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _statusMeta = const VerificationMeta('status');
  @override
  late final GeneratedColumn<String> status = GeneratedColumn<String>(
      'status', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _messageMeta =
      const VerificationMeta('message');
  @override
  late final GeneratedColumn<String> message = GeneratedColumn<String>(
      'message', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _cachedAtMeta =
      const VerificationMeta('cachedAt');
  @override
  late final GeneratedColumn<DateTime> cachedAt = GeneratedColumn<DateTime>(
      'cached_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _isStaleMeta =
      const VerificationMeta('isStale');
  @override
  late final GeneratedColumn<bool> isStale = GeneratedColumn<bool>(
      'is_stale', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_stale" IN (0, 1))'),
      defaultValue: const Constant(false));
  @override
  List<GeneratedColumn> get $columns => [
        id,
        requesterId,
        recipientId,
        status,
        message,
        createdAt,
        updatedAt,
        cachedAt,
        isStale
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'contact_requests';
  @override
  VerificationContext validateIntegrity(
      Insertable<DriftContactRequest> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('requester_id')) {
      context.handle(
          _requesterIdMeta,
          requesterId.isAcceptableOrUnknown(
              data['requester_id']!, _requesterIdMeta));
    } else if (isInserting) {
      context.missing(_requesterIdMeta);
    }
    if (data.containsKey('recipient_id')) {
      context.handle(
          _recipientIdMeta,
          recipientId.isAcceptableOrUnknown(
              data['recipient_id']!, _recipientIdMeta));
    } else if (isInserting) {
      context.missing(_recipientIdMeta);
    }
    if (data.containsKey('status')) {
      context.handle(_statusMeta,
          status.isAcceptableOrUnknown(data['status']!, _statusMeta));
    } else if (isInserting) {
      context.missing(_statusMeta);
    }
    if (data.containsKey('message')) {
      context.handle(_messageMeta,
          message.isAcceptableOrUnknown(data['message']!, _messageMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    } else if (isInserting) {
      context.missing(_updatedAtMeta);
    }
    if (data.containsKey('cached_at')) {
      context.handle(_cachedAtMeta,
          cachedAt.isAcceptableOrUnknown(data['cached_at']!, _cachedAtMeta));
    } else if (isInserting) {
      context.missing(_cachedAtMeta);
    }
    if (data.containsKey('is_stale')) {
      context.handle(_isStaleMeta,
          isStale.isAcceptableOrUnknown(data['is_stale']!, _isStaleMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  DriftContactRequest map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return DriftContactRequest(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      requesterId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}requester_id'])!,
      recipientId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}recipient_id'])!,
      status: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}status'])!,
      message: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}message']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
      cachedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}cached_at'])!,
      isStale: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_stale'])!,
    );
  }

  @override
  $ContactRequestsTable createAlias(String alias) {
    return $ContactRequestsTable(attachedDatabase, alias);
  }
}

class DriftContactRequest extends DataClass
    implements Insertable<DriftContactRequest> {
  final String id;
  final String requesterId;
  final String recipientId;
  final String status;
  final String? message;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime cachedAt;
  final bool isStale;
  const DriftContactRequest(
      {required this.id,
      required this.requesterId,
      required this.recipientId,
      required this.status,
      this.message,
      required this.createdAt,
      required this.updatedAt,
      required this.cachedAt,
      required this.isStale});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['requester_id'] = Variable<String>(requesterId);
    map['recipient_id'] = Variable<String>(recipientId);
    map['status'] = Variable<String>(status);
    if (!nullToAbsent || message != null) {
      map['message'] = Variable<String>(message);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    map['cached_at'] = Variable<DateTime>(cachedAt);
    map['is_stale'] = Variable<bool>(isStale);
    return map;
  }

  ContactRequestsCompanion toCompanion(bool nullToAbsent) {
    return ContactRequestsCompanion(
      id: Value(id),
      requesterId: Value(requesterId),
      recipientId: Value(recipientId),
      status: Value(status),
      message: message == null && nullToAbsent
          ? const Value.absent()
          : Value(message),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      cachedAt: Value(cachedAt),
      isStale: Value(isStale),
    );
  }

  factory DriftContactRequest.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return DriftContactRequest(
      id: serializer.fromJson<String>(json['id']),
      requesterId: serializer.fromJson<String>(json['requesterId']),
      recipientId: serializer.fromJson<String>(json['recipientId']),
      status: serializer.fromJson<String>(json['status']),
      message: serializer.fromJson<String?>(json['message']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      cachedAt: serializer.fromJson<DateTime>(json['cachedAt']),
      isStale: serializer.fromJson<bool>(json['isStale']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'requesterId': serializer.toJson<String>(requesterId),
      'recipientId': serializer.toJson<String>(recipientId),
      'status': serializer.toJson<String>(status),
      'message': serializer.toJson<String?>(message),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'cachedAt': serializer.toJson<DateTime>(cachedAt),
      'isStale': serializer.toJson<bool>(isStale),
    };
  }

  DriftContactRequest copyWith(
          {String? id,
          String? requesterId,
          String? recipientId,
          String? status,
          Value<String?> message = const Value.absent(),
          DateTime? createdAt,
          DateTime? updatedAt,
          DateTime? cachedAt,
          bool? isStale}) =>
      DriftContactRequest(
        id: id ?? this.id,
        requesterId: requesterId ?? this.requesterId,
        recipientId: recipientId ?? this.recipientId,
        status: status ?? this.status,
        message: message.present ? message.value : this.message,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        cachedAt: cachedAt ?? this.cachedAt,
        isStale: isStale ?? this.isStale,
      );
  DriftContactRequest copyWithCompanion(ContactRequestsCompanion data) {
    return DriftContactRequest(
      id: data.id.present ? data.id.value : this.id,
      requesterId:
          data.requesterId.present ? data.requesterId.value : this.requesterId,
      recipientId:
          data.recipientId.present ? data.recipientId.value : this.recipientId,
      status: data.status.present ? data.status.value : this.status,
      message: data.message.present ? data.message.value : this.message,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      cachedAt: data.cachedAt.present ? data.cachedAt.value : this.cachedAt,
      isStale: data.isStale.present ? data.isStale.value : this.isStale,
    );
  }

  @override
  String toString() {
    return (StringBuffer('DriftContactRequest(')
          ..write('id: $id, ')
          ..write('requesterId: $requesterId, ')
          ..write('recipientId: $recipientId, ')
          ..write('status: $status, ')
          ..write('message: $message, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('cachedAt: $cachedAt, ')
          ..write('isStale: $isStale')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, requesterId, recipientId, status, message,
      createdAt, updatedAt, cachedAt, isStale);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is DriftContactRequest &&
          other.id == this.id &&
          other.requesterId == this.requesterId &&
          other.recipientId == this.recipientId &&
          other.status == this.status &&
          other.message == this.message &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.cachedAt == this.cachedAt &&
          other.isStale == this.isStale);
}

class ContactRequestsCompanion extends UpdateCompanion<DriftContactRequest> {
  final Value<String> id;
  final Value<String> requesterId;
  final Value<String> recipientId;
  final Value<String> status;
  final Value<String?> message;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<DateTime> cachedAt;
  final Value<bool> isStale;
  final Value<int> rowid;
  const ContactRequestsCompanion({
    this.id = const Value.absent(),
    this.requesterId = const Value.absent(),
    this.recipientId = const Value.absent(),
    this.status = const Value.absent(),
    this.message = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.cachedAt = const Value.absent(),
    this.isStale = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ContactRequestsCompanion.insert({
    required String id,
    required String requesterId,
    required String recipientId,
    required String status,
    this.message = const Value.absent(),
    required DateTime createdAt,
    required DateTime updatedAt,
    required DateTime cachedAt,
    this.isStale = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        requesterId = Value(requesterId),
        recipientId = Value(recipientId),
        status = Value(status),
        createdAt = Value(createdAt),
        updatedAt = Value(updatedAt),
        cachedAt = Value(cachedAt);
  static Insertable<DriftContactRequest> custom({
    Expression<String>? id,
    Expression<String>? requesterId,
    Expression<String>? recipientId,
    Expression<String>? status,
    Expression<String>? message,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? cachedAt,
    Expression<bool>? isStale,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (requesterId != null) 'requester_id': requesterId,
      if (recipientId != null) 'recipient_id': recipientId,
      if (status != null) 'status': status,
      if (message != null) 'message': message,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (cachedAt != null) 'cached_at': cachedAt,
      if (isStale != null) 'is_stale': isStale,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ContactRequestsCompanion copyWith(
      {Value<String>? id,
      Value<String>? requesterId,
      Value<String>? recipientId,
      Value<String>? status,
      Value<String?>? message,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<DateTime>? cachedAt,
      Value<bool>? isStale,
      Value<int>? rowid}) {
    return ContactRequestsCompanion(
      id: id ?? this.id,
      requesterId: requesterId ?? this.requesterId,
      recipientId: recipientId ?? this.recipientId,
      status: status ?? this.status,
      message: message ?? this.message,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      cachedAt: cachedAt ?? this.cachedAt,
      isStale: isStale ?? this.isStale,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (requesterId.present) {
      map['requester_id'] = Variable<String>(requesterId.value);
    }
    if (recipientId.present) {
      map['recipient_id'] = Variable<String>(recipientId.value);
    }
    if (status.present) {
      map['status'] = Variable<String>(status.value);
    }
    if (message.present) {
      map['message'] = Variable<String>(message.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (cachedAt.present) {
      map['cached_at'] = Variable<DateTime>(cachedAt.value);
    }
    if (isStale.present) {
      map['is_stale'] = Variable<bool>(isStale.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ContactRequestsCompanion(')
          ..write('id: $id, ')
          ..write('requesterId: $requesterId, ')
          ..write('recipientId: $recipientId, ')
          ..write('status: $status, ')
          ..write('message: $message, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('cachedAt: $cachedAt, ')
          ..write('isStale: $isStale, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $FriendRequestsTable extends FriendRequests
    with TableInfo<$FriendRequestsTable, DriftFriendRequest> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $FriendRequestsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _requesterIdMeta =
      const VerificationMeta('requesterId');
  @override
  late final GeneratedColumn<String> requesterId = GeneratedColumn<String>(
      'requester_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _recipientIdMeta =
      const VerificationMeta('recipientId');
  @override
  late final GeneratedColumn<String> recipientId = GeneratedColumn<String>(
      'recipient_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _statusMeta = const VerificationMeta('status');
  @override
  late final GeneratedColumn<String> status = GeneratedColumn<String>(
      'status', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _sourceBubbleIdMeta =
      const VerificationMeta('sourceBubbleId');
  @override
  late final GeneratedColumn<String> sourceBubbleId = GeneratedColumn<String>(
      'source_bubble_id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _autoGeneratedMeta =
      const VerificationMeta('autoGenerated');
  @override
  late final GeneratedColumn<bool> autoGenerated = GeneratedColumn<bool>(
      'auto_generated', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints: GeneratedColumn.constraintIsAlways(
          'CHECK ("auto_generated" IN (0, 1))'),
      defaultValue: const Constant(true));
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _cachedAtMeta =
      const VerificationMeta('cachedAt');
  @override
  late final GeneratedColumn<DateTime> cachedAt = GeneratedColumn<DateTime>(
      'cached_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _isStaleMeta =
      const VerificationMeta('isStale');
  @override
  late final GeneratedColumn<bool> isStale = GeneratedColumn<bool>(
      'is_stale', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_stale" IN (0, 1))'),
      defaultValue: const Constant(false));
  @override
  List<GeneratedColumn> get $columns => [
        id,
        requesterId,
        recipientId,
        status,
        sourceBubbleId,
        autoGenerated,
        createdAt,
        updatedAt,
        cachedAt,
        isStale
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'friend_requests';
  @override
  VerificationContext validateIntegrity(Insertable<DriftFriendRequest> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('requester_id')) {
      context.handle(
          _requesterIdMeta,
          requesterId.isAcceptableOrUnknown(
              data['requester_id']!, _requesterIdMeta));
    } else if (isInserting) {
      context.missing(_requesterIdMeta);
    }
    if (data.containsKey('recipient_id')) {
      context.handle(
          _recipientIdMeta,
          recipientId.isAcceptableOrUnknown(
              data['recipient_id']!, _recipientIdMeta));
    } else if (isInserting) {
      context.missing(_recipientIdMeta);
    }
    if (data.containsKey('status')) {
      context.handle(_statusMeta,
          status.isAcceptableOrUnknown(data['status']!, _statusMeta));
    } else if (isInserting) {
      context.missing(_statusMeta);
    }
    if (data.containsKey('source_bubble_id')) {
      context.handle(
          _sourceBubbleIdMeta,
          sourceBubbleId.isAcceptableOrUnknown(
              data['source_bubble_id']!, _sourceBubbleIdMeta));
    } else if (isInserting) {
      context.missing(_sourceBubbleIdMeta);
    }
    if (data.containsKey('auto_generated')) {
      context.handle(
          _autoGeneratedMeta,
          autoGenerated.isAcceptableOrUnknown(
              data['auto_generated']!, _autoGeneratedMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    } else if (isInserting) {
      context.missing(_createdAtMeta);
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    } else if (isInserting) {
      context.missing(_updatedAtMeta);
    }
    if (data.containsKey('cached_at')) {
      context.handle(_cachedAtMeta,
          cachedAt.isAcceptableOrUnknown(data['cached_at']!, _cachedAtMeta));
    } else if (isInserting) {
      context.missing(_cachedAtMeta);
    }
    if (data.containsKey('is_stale')) {
      context.handle(_isStaleMeta,
          isStale.isAcceptableOrUnknown(data['is_stale']!, _isStaleMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  DriftFriendRequest map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return DriftFriendRequest(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      requesterId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}requester_id'])!,
      recipientId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}recipient_id'])!,
      status: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}status'])!,
      sourceBubbleId: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}source_bubble_id'])!,
      autoGenerated: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}auto_generated'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
      cachedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}cached_at'])!,
      isStale: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_stale'])!,
    );
  }

  @override
  $FriendRequestsTable createAlias(String alias) {
    return $FriendRequestsTable(attachedDatabase, alias);
  }
}

class DriftFriendRequest extends DataClass
    implements Insertable<DriftFriendRequest> {
  final String id;
  final String requesterId;
  final String recipientId;
  final String status;
  final String sourceBubbleId;
  final bool autoGenerated;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime cachedAt;
  final bool isStale;
  const DriftFriendRequest(
      {required this.id,
      required this.requesterId,
      required this.recipientId,
      required this.status,
      required this.sourceBubbleId,
      required this.autoGenerated,
      required this.createdAt,
      required this.updatedAt,
      required this.cachedAt,
      required this.isStale});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['requester_id'] = Variable<String>(requesterId);
    map['recipient_id'] = Variable<String>(recipientId);
    map['status'] = Variable<String>(status);
    map['source_bubble_id'] = Variable<String>(sourceBubbleId);
    map['auto_generated'] = Variable<bool>(autoGenerated);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    map['cached_at'] = Variable<DateTime>(cachedAt);
    map['is_stale'] = Variable<bool>(isStale);
    return map;
  }

  FriendRequestsCompanion toCompanion(bool nullToAbsent) {
    return FriendRequestsCompanion(
      id: Value(id),
      requesterId: Value(requesterId),
      recipientId: Value(recipientId),
      status: Value(status),
      sourceBubbleId: Value(sourceBubbleId),
      autoGenerated: Value(autoGenerated),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
      cachedAt: Value(cachedAt),
      isStale: Value(isStale),
    );
  }

  factory DriftFriendRequest.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return DriftFriendRequest(
      id: serializer.fromJson<String>(json['id']),
      requesterId: serializer.fromJson<String>(json['requesterId']),
      recipientId: serializer.fromJson<String>(json['recipientId']),
      status: serializer.fromJson<String>(json['status']),
      sourceBubbleId: serializer.fromJson<String>(json['sourceBubbleId']),
      autoGenerated: serializer.fromJson<bool>(json['autoGenerated']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
      cachedAt: serializer.fromJson<DateTime>(json['cachedAt']),
      isStale: serializer.fromJson<bool>(json['isStale']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'requesterId': serializer.toJson<String>(requesterId),
      'recipientId': serializer.toJson<String>(recipientId),
      'status': serializer.toJson<String>(status),
      'sourceBubbleId': serializer.toJson<String>(sourceBubbleId),
      'autoGenerated': serializer.toJson<bool>(autoGenerated),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
      'cachedAt': serializer.toJson<DateTime>(cachedAt),
      'isStale': serializer.toJson<bool>(isStale),
    };
  }

  DriftFriendRequest copyWith(
          {String? id,
          String? requesterId,
          String? recipientId,
          String? status,
          String? sourceBubbleId,
          bool? autoGenerated,
          DateTime? createdAt,
          DateTime? updatedAt,
          DateTime? cachedAt,
          bool? isStale}) =>
      DriftFriendRequest(
        id: id ?? this.id,
        requesterId: requesterId ?? this.requesterId,
        recipientId: recipientId ?? this.recipientId,
        status: status ?? this.status,
        sourceBubbleId: sourceBubbleId ?? this.sourceBubbleId,
        autoGenerated: autoGenerated ?? this.autoGenerated,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        cachedAt: cachedAt ?? this.cachedAt,
        isStale: isStale ?? this.isStale,
      );
  DriftFriendRequest copyWithCompanion(FriendRequestsCompanion data) {
    return DriftFriendRequest(
      id: data.id.present ? data.id.value : this.id,
      requesterId:
          data.requesterId.present ? data.requesterId.value : this.requesterId,
      recipientId:
          data.recipientId.present ? data.recipientId.value : this.recipientId,
      status: data.status.present ? data.status.value : this.status,
      sourceBubbleId: data.sourceBubbleId.present
          ? data.sourceBubbleId.value
          : this.sourceBubbleId,
      autoGenerated: data.autoGenerated.present
          ? data.autoGenerated.value
          : this.autoGenerated,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
      cachedAt: data.cachedAt.present ? data.cachedAt.value : this.cachedAt,
      isStale: data.isStale.present ? data.isStale.value : this.isStale,
    );
  }

  @override
  String toString() {
    return (StringBuffer('DriftFriendRequest(')
          ..write('id: $id, ')
          ..write('requesterId: $requesterId, ')
          ..write('recipientId: $recipientId, ')
          ..write('status: $status, ')
          ..write('sourceBubbleId: $sourceBubbleId, ')
          ..write('autoGenerated: $autoGenerated, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('cachedAt: $cachedAt, ')
          ..write('isStale: $isStale')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, requesterId, recipientId, status,
      sourceBubbleId, autoGenerated, createdAt, updatedAt, cachedAt, isStale);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is DriftFriendRequest &&
          other.id == this.id &&
          other.requesterId == this.requesterId &&
          other.recipientId == this.recipientId &&
          other.status == this.status &&
          other.sourceBubbleId == this.sourceBubbleId &&
          other.autoGenerated == this.autoGenerated &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt &&
          other.cachedAt == this.cachedAt &&
          other.isStale == this.isStale);
}

class FriendRequestsCompanion extends UpdateCompanion<DriftFriendRequest> {
  final Value<String> id;
  final Value<String> requesterId;
  final Value<String> recipientId;
  final Value<String> status;
  final Value<String> sourceBubbleId;
  final Value<bool> autoGenerated;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<DateTime> cachedAt;
  final Value<bool> isStale;
  final Value<int> rowid;
  const FriendRequestsCompanion({
    this.id = const Value.absent(),
    this.requesterId = const Value.absent(),
    this.recipientId = const Value.absent(),
    this.status = const Value.absent(),
    this.sourceBubbleId = const Value.absent(),
    this.autoGenerated = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.cachedAt = const Value.absent(),
    this.isStale = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  FriendRequestsCompanion.insert({
    required String id,
    required String requesterId,
    required String recipientId,
    required String status,
    required String sourceBubbleId,
    this.autoGenerated = const Value.absent(),
    required DateTime createdAt,
    required DateTime updatedAt,
    required DateTime cachedAt,
    this.isStale = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        requesterId = Value(requesterId),
        recipientId = Value(recipientId),
        status = Value(status),
        sourceBubbleId = Value(sourceBubbleId),
        createdAt = Value(createdAt),
        updatedAt = Value(updatedAt),
        cachedAt = Value(cachedAt);
  static Insertable<DriftFriendRequest> custom({
    Expression<String>? id,
    Expression<String>? requesterId,
    Expression<String>? recipientId,
    Expression<String>? status,
    Expression<String>? sourceBubbleId,
    Expression<bool>? autoGenerated,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<DateTime>? cachedAt,
    Expression<bool>? isStale,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (requesterId != null) 'requester_id': requesterId,
      if (recipientId != null) 'recipient_id': recipientId,
      if (status != null) 'status': status,
      if (sourceBubbleId != null) 'source_bubble_id': sourceBubbleId,
      if (autoGenerated != null) 'auto_generated': autoGenerated,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (cachedAt != null) 'cached_at': cachedAt,
      if (isStale != null) 'is_stale': isStale,
      if (rowid != null) 'rowid': rowid,
    });
  }

  FriendRequestsCompanion copyWith(
      {Value<String>? id,
      Value<String>? requesterId,
      Value<String>? recipientId,
      Value<String>? status,
      Value<String>? sourceBubbleId,
      Value<bool>? autoGenerated,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<DateTime>? cachedAt,
      Value<bool>? isStale,
      Value<int>? rowid}) {
    return FriendRequestsCompanion(
      id: id ?? this.id,
      requesterId: requesterId ?? this.requesterId,
      recipientId: recipientId ?? this.recipientId,
      status: status ?? this.status,
      sourceBubbleId: sourceBubbleId ?? this.sourceBubbleId,
      autoGenerated: autoGenerated ?? this.autoGenerated,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      cachedAt: cachedAt ?? this.cachedAt,
      isStale: isStale ?? this.isStale,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (requesterId.present) {
      map['requester_id'] = Variable<String>(requesterId.value);
    }
    if (recipientId.present) {
      map['recipient_id'] = Variable<String>(recipientId.value);
    }
    if (status.present) {
      map['status'] = Variable<String>(status.value);
    }
    if (sourceBubbleId.present) {
      map['source_bubble_id'] = Variable<String>(sourceBubbleId.value);
    }
    if (autoGenerated.present) {
      map['auto_generated'] = Variable<bool>(autoGenerated.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (cachedAt.present) {
      map['cached_at'] = Variable<DateTime>(cachedAt.value);
    }
    if (isStale.present) {
      map['is_stale'] = Variable<bool>(isStale.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('FriendRequestsCompanion(')
          ..write('id: $id, ')
          ..write('requesterId: $requesterId, ')
          ..write('recipientId: $recipientId, ')
          ..write('status: $status, ')
          ..write('sourceBubbleId: $sourceBubbleId, ')
          ..write('autoGenerated: $autoGenerated, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('cachedAt: $cachedAt, ')
          ..write('isStale: $isStale, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $UserProfilesTable extends UserProfiles
    with TableInfo<$UserProfilesTable, DriftUserProfile> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $UserProfilesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _emailMeta = const VerificationMeta('email');
  @override
  late final GeneratedColumn<String> email = GeneratedColumn<String>(
      'email', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _usernameMeta =
      const VerificationMeta('username');
  @override
  late final GeneratedColumn<String> username = GeneratedColumn<String>(
      'username', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _firstNameMeta =
      const VerificationMeta('firstName');
  @override
  late final GeneratedColumn<String> firstName = GeneratedColumn<String>(
      'first_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _lastNameMeta =
      const VerificationMeta('lastName');
  @override
  late final GeneratedColumn<String> lastName = GeneratedColumn<String>(
      'last_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _profilePictureUrlMeta =
      const VerificationMeta('profilePictureUrl');
  @override
  late final GeneratedColumn<String> profilePictureUrl =
      GeneratedColumn<String>('profile_picture_url', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _birthdayMeta =
      const VerificationMeta('birthday');
  @override
  late final GeneratedColumn<DateTime> birthday = GeneratedColumn<DateTime>(
      'birthday', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _friendIdsMeta =
      const VerificationMeta('friendIds');
  @override
  late final GeneratedColumn<String> friendIds = GeneratedColumn<String>(
      'friend_ids', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _bubbleIdMeta =
      const VerificationMeta('bubbleId');
  @override
  late final GeneratedColumn<String> bubbleId = GeneratedColumn<String>(
      'bubble_id', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _contactIdsMeta =
      const VerificationMeta('contactIds');
  @override
  late final GeneratedColumn<String> contactIds = GeneratedColumn<String>(
      'contact_ids', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _onlineStatusMeta =
      const VerificationMeta('onlineStatus');
  @override
  late final GeneratedColumn<String> onlineStatus = GeneratedColumn<String>(
      'online_status', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant('offline'));
  static const VerificationMeta _bubbleStatusMeta =
      const VerificationMeta('bubbleStatus');
  @override
  late final GeneratedColumn<String> bubbleStatus = GeneratedColumn<String>(
      'bubble_status', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      defaultValue: const Constant('noBubble'));
  static const VerificationMeta _blockedUserIdsMeta =
      const VerificationMeta('blockedUserIds');
  @override
  late final GeneratedColumn<String> blockedUserIds = GeneratedColumn<String>(
      'blocked_user_ids', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _pendingSentContactRequestIdsMeta =
      const VerificationMeta('pendingSentContactRequestIds');
  @override
  late final GeneratedColumn<String> pendingSentContactRequestIds =
      GeneratedColumn<String>(
          'pending_sent_contact_request_ids', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _pendingReceivedContactRequestIdsMeta =
      const VerificationMeta('pendingReceivedContactRequestIds');
  @override
  late final GeneratedColumn<String> pendingReceivedContactRequestIds =
      GeneratedColumn<String>(
          'pending_received_contact_request_ids', aliasedName, true,
          type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _hasCompletedOnboardingMeta =
      const VerificationMeta('hasCompletedOnboarding');
  @override
  late final GeneratedColumn<bool> hasCompletedOnboarding =
      GeneratedColumn<bool>('has_completed_onboarding', aliasedName, false,
          type: DriftSqlType.bool,
          requiredDuringInsert: false,
          defaultConstraints: GeneratedColumn.constraintIsAlways(
              'CHECK ("has_completed_onboarding" IN (0, 1))'),
          defaultValue: const Constant(false));
  static const VerificationMeta _cachedAtMeta =
      const VerificationMeta('cachedAt');
  @override
  late final GeneratedColumn<DateTime> cachedAt = GeneratedColumn<DateTime>(
      'cached_at', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _lastRefreshedMeta =
      const VerificationMeta('lastRefreshed');
  @override
  late final GeneratedColumn<DateTime> lastRefreshed =
      GeneratedColumn<DateTime>('last_refreshed', aliasedName, false,
          type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _lastAccessedMeta =
      const VerificationMeta('lastAccessed');
  @override
  late final GeneratedColumn<DateTime> lastAccessed = GeneratedColumn<DateTime>(
      'last_accessed', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _isStaleMeta =
      const VerificationMeta('isStale');
  @override
  late final GeneratedColumn<bool> isStale = GeneratedColumn<bool>(
      'is_stale', aliasedName, false,
      type: DriftSqlType.bool,
      requiredDuringInsert: false,
      defaultConstraints:
          GeneratedColumn.constraintIsAlways('CHECK ("is_stale" IN (0, 1))'),
      defaultValue: const Constant(false));
  @override
  List<GeneratedColumn> get $columns => [
        id,
        email,
        username,
        firstName,
        lastName,
        profilePictureUrl,
        birthday,
        friendIds,
        bubbleId,
        contactIds,
        onlineStatus,
        bubbleStatus,
        blockedUserIds,
        pendingSentContactRequestIds,
        pendingReceivedContactRequestIds,
        hasCompletedOnboarding,
        cachedAt,
        lastRefreshed,
        lastAccessed,
        isStale
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'user_profiles';
  @override
  VerificationContext validateIntegrity(Insertable<DriftUserProfile> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('email')) {
      context.handle(
          _emailMeta, email.isAcceptableOrUnknown(data['email']!, _emailMeta));
    }
    if (data.containsKey('username')) {
      context.handle(_usernameMeta,
          username.isAcceptableOrUnknown(data['username']!, _usernameMeta));
    }
    if (data.containsKey('first_name')) {
      context.handle(_firstNameMeta,
          firstName.isAcceptableOrUnknown(data['first_name']!, _firstNameMeta));
    }
    if (data.containsKey('last_name')) {
      context.handle(_lastNameMeta,
          lastName.isAcceptableOrUnknown(data['last_name']!, _lastNameMeta));
    }
    if (data.containsKey('profile_picture_url')) {
      context.handle(
          _profilePictureUrlMeta,
          profilePictureUrl.isAcceptableOrUnknown(
              data['profile_picture_url']!, _profilePictureUrlMeta));
    }
    if (data.containsKey('birthday')) {
      context.handle(_birthdayMeta,
          birthday.isAcceptableOrUnknown(data['birthday']!, _birthdayMeta));
    }
    if (data.containsKey('friend_ids')) {
      context.handle(_friendIdsMeta,
          friendIds.isAcceptableOrUnknown(data['friend_ids']!, _friendIdsMeta));
    }
    if (data.containsKey('bubble_id')) {
      context.handle(_bubbleIdMeta,
          bubbleId.isAcceptableOrUnknown(data['bubble_id']!, _bubbleIdMeta));
    }
    if (data.containsKey('contact_ids')) {
      context.handle(
          _contactIdsMeta,
          contactIds.isAcceptableOrUnknown(
              data['contact_ids']!, _contactIdsMeta));
    }
    if (data.containsKey('online_status')) {
      context.handle(
          _onlineStatusMeta,
          onlineStatus.isAcceptableOrUnknown(
              data['online_status']!, _onlineStatusMeta));
    }
    if (data.containsKey('bubble_status')) {
      context.handle(
          _bubbleStatusMeta,
          bubbleStatus.isAcceptableOrUnknown(
              data['bubble_status']!, _bubbleStatusMeta));
    }
    if (data.containsKey('blocked_user_ids')) {
      context.handle(
          _blockedUserIdsMeta,
          blockedUserIds.isAcceptableOrUnknown(
              data['blocked_user_ids']!, _blockedUserIdsMeta));
    }
    if (data.containsKey('pending_sent_contact_request_ids')) {
      context.handle(
          _pendingSentContactRequestIdsMeta,
          pendingSentContactRequestIds.isAcceptableOrUnknown(
              data['pending_sent_contact_request_ids']!,
              _pendingSentContactRequestIdsMeta));
    }
    if (data.containsKey('pending_received_contact_request_ids')) {
      context.handle(
          _pendingReceivedContactRequestIdsMeta,
          pendingReceivedContactRequestIds.isAcceptableOrUnknown(
              data['pending_received_contact_request_ids']!,
              _pendingReceivedContactRequestIdsMeta));
    }
    if (data.containsKey('has_completed_onboarding')) {
      context.handle(
          _hasCompletedOnboardingMeta,
          hasCompletedOnboarding.isAcceptableOrUnknown(
              data['has_completed_onboarding']!, _hasCompletedOnboardingMeta));
    }
    if (data.containsKey('cached_at')) {
      context.handle(_cachedAtMeta,
          cachedAt.isAcceptableOrUnknown(data['cached_at']!, _cachedAtMeta));
    } else if (isInserting) {
      context.missing(_cachedAtMeta);
    }
    if (data.containsKey('last_refreshed')) {
      context.handle(
          _lastRefreshedMeta,
          lastRefreshed.isAcceptableOrUnknown(
              data['last_refreshed']!, _lastRefreshedMeta));
    } else if (isInserting) {
      context.missing(_lastRefreshedMeta);
    }
    if (data.containsKey('last_accessed')) {
      context.handle(
          _lastAccessedMeta,
          lastAccessed.isAcceptableOrUnknown(
              data['last_accessed']!, _lastAccessedMeta));
    } else if (isInserting) {
      context.missing(_lastAccessedMeta);
    }
    if (data.containsKey('is_stale')) {
      context.handle(_isStaleMeta,
          isStale.isAcceptableOrUnknown(data['is_stale']!, _isStaleMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  DriftUserProfile map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return DriftUserProfile(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      email: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}email']),
      username: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}username']),
      firstName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}first_name']),
      lastName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}last_name']),
      profilePictureUrl: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}profile_picture_url']),
      birthday: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}birthday']),
      friendIds: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}friend_ids']),
      bubbleId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}bubble_id']),
      contactIds: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}contact_ids']),
      onlineStatus: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}online_status'])!,
      bubbleStatus: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}bubble_status'])!,
      blockedUserIds: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}blocked_user_ids']),
      pendingSentContactRequestIds: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}pending_sent_contact_request_ids']),
      pendingReceivedContactRequestIds: attachedDatabase.typeMapping.read(
          DriftSqlType.string,
          data['${effectivePrefix}pending_received_contact_request_ids']),
      hasCompletedOnboarding: attachedDatabase.typeMapping.read(
          DriftSqlType.bool,
          data['${effectivePrefix}has_completed_onboarding'])!,
      cachedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}cached_at'])!,
      lastRefreshed: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}last_refreshed'])!,
      lastAccessed: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}last_accessed'])!,
      isStale: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_stale'])!,
    );
  }

  @override
  $UserProfilesTable createAlias(String alias) {
    return $UserProfilesTable(attachedDatabase, alias);
  }
}

class DriftUserProfile extends DataClass
    implements Insertable<DriftUserProfile> {
  final String id;
  final String? email;
  final String? username;
  final String? firstName;
  final String? lastName;
  final String? profilePictureUrl;
  final DateTime? birthday;
  final String? friendIds;
  final String? bubbleId;
  final String? contactIds;
  final String onlineStatus;
  final String bubbleStatus;
  final String? blockedUserIds;
  final String? pendingSentContactRequestIds;
  final String? pendingReceivedContactRequestIds;
  final bool hasCompletedOnboarding;
  final DateTime cachedAt;
  final DateTime lastRefreshed;
  final DateTime lastAccessed;
  final bool isStale;
  const DriftUserProfile(
      {required this.id,
      this.email,
      this.username,
      this.firstName,
      this.lastName,
      this.profilePictureUrl,
      this.birthday,
      this.friendIds,
      this.bubbleId,
      this.contactIds,
      required this.onlineStatus,
      required this.bubbleStatus,
      this.blockedUserIds,
      this.pendingSentContactRequestIds,
      this.pendingReceivedContactRequestIds,
      required this.hasCompletedOnboarding,
      required this.cachedAt,
      required this.lastRefreshed,
      required this.lastAccessed,
      required this.isStale});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    if (!nullToAbsent || email != null) {
      map['email'] = Variable<String>(email);
    }
    if (!nullToAbsent || username != null) {
      map['username'] = Variable<String>(username);
    }
    if (!nullToAbsent || firstName != null) {
      map['first_name'] = Variable<String>(firstName);
    }
    if (!nullToAbsent || lastName != null) {
      map['last_name'] = Variable<String>(lastName);
    }
    if (!nullToAbsent || profilePictureUrl != null) {
      map['profile_picture_url'] = Variable<String>(profilePictureUrl);
    }
    if (!nullToAbsent || birthday != null) {
      map['birthday'] = Variable<DateTime>(birthday);
    }
    if (!nullToAbsent || friendIds != null) {
      map['friend_ids'] = Variable<String>(friendIds);
    }
    if (!nullToAbsent || bubbleId != null) {
      map['bubble_id'] = Variable<String>(bubbleId);
    }
    if (!nullToAbsent || contactIds != null) {
      map['contact_ids'] = Variable<String>(contactIds);
    }
    map['online_status'] = Variable<String>(onlineStatus);
    map['bubble_status'] = Variable<String>(bubbleStatus);
    if (!nullToAbsent || blockedUserIds != null) {
      map['blocked_user_ids'] = Variable<String>(blockedUserIds);
    }
    if (!nullToAbsent || pendingSentContactRequestIds != null) {
      map['pending_sent_contact_request_ids'] =
          Variable<String>(pendingSentContactRequestIds);
    }
    if (!nullToAbsent || pendingReceivedContactRequestIds != null) {
      map['pending_received_contact_request_ids'] =
          Variable<String>(pendingReceivedContactRequestIds);
    }
    map['has_completed_onboarding'] = Variable<bool>(hasCompletedOnboarding);
    map['cached_at'] = Variable<DateTime>(cachedAt);
    map['last_refreshed'] = Variable<DateTime>(lastRefreshed);
    map['last_accessed'] = Variable<DateTime>(lastAccessed);
    map['is_stale'] = Variable<bool>(isStale);
    return map;
  }

  UserProfilesCompanion toCompanion(bool nullToAbsent) {
    return UserProfilesCompanion(
      id: Value(id),
      email:
          email == null && nullToAbsent ? const Value.absent() : Value(email),
      username: username == null && nullToAbsent
          ? const Value.absent()
          : Value(username),
      firstName: firstName == null && nullToAbsent
          ? const Value.absent()
          : Value(firstName),
      lastName: lastName == null && nullToAbsent
          ? const Value.absent()
          : Value(lastName),
      profilePictureUrl: profilePictureUrl == null && nullToAbsent
          ? const Value.absent()
          : Value(profilePictureUrl),
      birthday: birthday == null && nullToAbsent
          ? const Value.absent()
          : Value(birthday),
      friendIds: friendIds == null && nullToAbsent
          ? const Value.absent()
          : Value(friendIds),
      bubbleId: bubbleId == null && nullToAbsent
          ? const Value.absent()
          : Value(bubbleId),
      contactIds: contactIds == null && nullToAbsent
          ? const Value.absent()
          : Value(contactIds),
      onlineStatus: Value(onlineStatus),
      bubbleStatus: Value(bubbleStatus),
      blockedUserIds: blockedUserIds == null && nullToAbsent
          ? const Value.absent()
          : Value(blockedUserIds),
      pendingSentContactRequestIds:
          pendingSentContactRequestIds == null && nullToAbsent
              ? const Value.absent()
              : Value(pendingSentContactRequestIds),
      pendingReceivedContactRequestIds:
          pendingReceivedContactRequestIds == null && nullToAbsent
              ? const Value.absent()
              : Value(pendingReceivedContactRequestIds),
      hasCompletedOnboarding: Value(hasCompletedOnboarding),
      cachedAt: Value(cachedAt),
      lastRefreshed: Value(lastRefreshed),
      lastAccessed: Value(lastAccessed),
      isStale: Value(isStale),
    );
  }

  factory DriftUserProfile.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return DriftUserProfile(
      id: serializer.fromJson<String>(json['id']),
      email: serializer.fromJson<String?>(json['email']),
      username: serializer.fromJson<String?>(json['username']),
      firstName: serializer.fromJson<String?>(json['firstName']),
      lastName: serializer.fromJson<String?>(json['lastName']),
      profilePictureUrl:
          serializer.fromJson<String?>(json['profilePictureUrl']),
      birthday: serializer.fromJson<DateTime?>(json['birthday']),
      friendIds: serializer.fromJson<String?>(json['friendIds']),
      bubbleId: serializer.fromJson<String?>(json['bubbleId']),
      contactIds: serializer.fromJson<String?>(json['contactIds']),
      onlineStatus: serializer.fromJson<String>(json['onlineStatus']),
      bubbleStatus: serializer.fromJson<String>(json['bubbleStatus']),
      blockedUserIds: serializer.fromJson<String?>(json['blockedUserIds']),
      pendingSentContactRequestIds:
          serializer.fromJson<String?>(json['pendingSentContactRequestIds']),
      pendingReceivedContactRequestIds: serializer
          .fromJson<String?>(json['pendingReceivedContactRequestIds']),
      hasCompletedOnboarding:
          serializer.fromJson<bool>(json['hasCompletedOnboarding']),
      cachedAt: serializer.fromJson<DateTime>(json['cachedAt']),
      lastRefreshed: serializer.fromJson<DateTime>(json['lastRefreshed']),
      lastAccessed: serializer.fromJson<DateTime>(json['lastAccessed']),
      isStale: serializer.fromJson<bool>(json['isStale']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'email': serializer.toJson<String?>(email),
      'username': serializer.toJson<String?>(username),
      'firstName': serializer.toJson<String?>(firstName),
      'lastName': serializer.toJson<String?>(lastName),
      'profilePictureUrl': serializer.toJson<String?>(profilePictureUrl),
      'birthday': serializer.toJson<DateTime?>(birthday),
      'friendIds': serializer.toJson<String?>(friendIds),
      'bubbleId': serializer.toJson<String?>(bubbleId),
      'contactIds': serializer.toJson<String?>(contactIds),
      'onlineStatus': serializer.toJson<String>(onlineStatus),
      'bubbleStatus': serializer.toJson<String>(bubbleStatus),
      'blockedUserIds': serializer.toJson<String?>(blockedUserIds),
      'pendingSentContactRequestIds':
          serializer.toJson<String?>(pendingSentContactRequestIds),
      'pendingReceivedContactRequestIds':
          serializer.toJson<String?>(pendingReceivedContactRequestIds),
      'hasCompletedOnboarding': serializer.toJson<bool>(hasCompletedOnboarding),
      'cachedAt': serializer.toJson<DateTime>(cachedAt),
      'lastRefreshed': serializer.toJson<DateTime>(lastRefreshed),
      'lastAccessed': serializer.toJson<DateTime>(lastAccessed),
      'isStale': serializer.toJson<bool>(isStale),
    };
  }

  DriftUserProfile copyWith(
          {String? id,
          Value<String?> email = const Value.absent(),
          Value<String?> username = const Value.absent(),
          Value<String?> firstName = const Value.absent(),
          Value<String?> lastName = const Value.absent(),
          Value<String?> profilePictureUrl = const Value.absent(),
          Value<DateTime?> birthday = const Value.absent(),
          Value<String?> friendIds = const Value.absent(),
          Value<String?> bubbleId = const Value.absent(),
          Value<String?> contactIds = const Value.absent(),
          String? onlineStatus,
          String? bubbleStatus,
          Value<String?> blockedUserIds = const Value.absent(),
          Value<String?> pendingSentContactRequestIds = const Value.absent(),
          Value<String?> pendingReceivedContactRequestIds =
              const Value.absent(),
          bool? hasCompletedOnboarding,
          DateTime? cachedAt,
          DateTime? lastRefreshed,
          DateTime? lastAccessed,
          bool? isStale}) =>
      DriftUserProfile(
        id: id ?? this.id,
        email: email.present ? email.value : this.email,
        username: username.present ? username.value : this.username,
        firstName: firstName.present ? firstName.value : this.firstName,
        lastName: lastName.present ? lastName.value : this.lastName,
        profilePictureUrl: profilePictureUrl.present
            ? profilePictureUrl.value
            : this.profilePictureUrl,
        birthday: birthday.present ? birthday.value : this.birthday,
        friendIds: friendIds.present ? friendIds.value : this.friendIds,
        bubbleId: bubbleId.present ? bubbleId.value : this.bubbleId,
        contactIds: contactIds.present ? contactIds.value : this.contactIds,
        onlineStatus: onlineStatus ?? this.onlineStatus,
        bubbleStatus: bubbleStatus ?? this.bubbleStatus,
        blockedUserIds:
            blockedUserIds.present ? blockedUserIds.value : this.blockedUserIds,
        pendingSentContactRequestIds: pendingSentContactRequestIds.present
            ? pendingSentContactRequestIds.value
            : this.pendingSentContactRequestIds,
        pendingReceivedContactRequestIds:
            pendingReceivedContactRequestIds.present
                ? pendingReceivedContactRequestIds.value
                : this.pendingReceivedContactRequestIds,
        hasCompletedOnboarding:
            hasCompletedOnboarding ?? this.hasCompletedOnboarding,
        cachedAt: cachedAt ?? this.cachedAt,
        lastRefreshed: lastRefreshed ?? this.lastRefreshed,
        lastAccessed: lastAccessed ?? this.lastAccessed,
        isStale: isStale ?? this.isStale,
      );
  DriftUserProfile copyWithCompanion(UserProfilesCompanion data) {
    return DriftUserProfile(
      id: data.id.present ? data.id.value : this.id,
      email: data.email.present ? data.email.value : this.email,
      username: data.username.present ? data.username.value : this.username,
      firstName: data.firstName.present ? data.firstName.value : this.firstName,
      lastName: data.lastName.present ? data.lastName.value : this.lastName,
      profilePictureUrl: data.profilePictureUrl.present
          ? data.profilePictureUrl.value
          : this.profilePictureUrl,
      birthday: data.birthday.present ? data.birthday.value : this.birthday,
      friendIds: data.friendIds.present ? data.friendIds.value : this.friendIds,
      bubbleId: data.bubbleId.present ? data.bubbleId.value : this.bubbleId,
      contactIds:
          data.contactIds.present ? data.contactIds.value : this.contactIds,
      onlineStatus: data.onlineStatus.present
          ? data.onlineStatus.value
          : this.onlineStatus,
      bubbleStatus: data.bubbleStatus.present
          ? data.bubbleStatus.value
          : this.bubbleStatus,
      blockedUserIds: data.blockedUserIds.present
          ? data.blockedUserIds.value
          : this.blockedUserIds,
      pendingSentContactRequestIds: data.pendingSentContactRequestIds.present
          ? data.pendingSentContactRequestIds.value
          : this.pendingSentContactRequestIds,
      pendingReceivedContactRequestIds:
          data.pendingReceivedContactRequestIds.present
              ? data.pendingReceivedContactRequestIds.value
              : this.pendingReceivedContactRequestIds,
      hasCompletedOnboarding: data.hasCompletedOnboarding.present
          ? data.hasCompletedOnboarding.value
          : this.hasCompletedOnboarding,
      cachedAt: data.cachedAt.present ? data.cachedAt.value : this.cachedAt,
      lastRefreshed: data.lastRefreshed.present
          ? data.lastRefreshed.value
          : this.lastRefreshed,
      lastAccessed: data.lastAccessed.present
          ? data.lastAccessed.value
          : this.lastAccessed,
      isStale: data.isStale.present ? data.isStale.value : this.isStale,
    );
  }

  @override
  String toString() {
    return (StringBuffer('DriftUserProfile(')
          ..write('id: $id, ')
          ..write('email: $email, ')
          ..write('username: $username, ')
          ..write('firstName: $firstName, ')
          ..write('lastName: $lastName, ')
          ..write('profilePictureUrl: $profilePictureUrl, ')
          ..write('birthday: $birthday, ')
          ..write('friendIds: $friendIds, ')
          ..write('bubbleId: $bubbleId, ')
          ..write('contactIds: $contactIds, ')
          ..write('onlineStatus: $onlineStatus, ')
          ..write('bubbleStatus: $bubbleStatus, ')
          ..write('blockedUserIds: $blockedUserIds, ')
          ..write(
              'pendingSentContactRequestIds: $pendingSentContactRequestIds, ')
          ..write(
              'pendingReceivedContactRequestIds: $pendingReceivedContactRequestIds, ')
          ..write('hasCompletedOnboarding: $hasCompletedOnboarding, ')
          ..write('cachedAt: $cachedAt, ')
          ..write('lastRefreshed: $lastRefreshed, ')
          ..write('lastAccessed: $lastAccessed, ')
          ..write('isStale: $isStale')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      email,
      username,
      firstName,
      lastName,
      profilePictureUrl,
      birthday,
      friendIds,
      bubbleId,
      contactIds,
      onlineStatus,
      bubbleStatus,
      blockedUserIds,
      pendingSentContactRequestIds,
      pendingReceivedContactRequestIds,
      hasCompletedOnboarding,
      cachedAt,
      lastRefreshed,
      lastAccessed,
      isStale);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is DriftUserProfile &&
          other.id == this.id &&
          other.email == this.email &&
          other.username == this.username &&
          other.firstName == this.firstName &&
          other.lastName == this.lastName &&
          other.profilePictureUrl == this.profilePictureUrl &&
          other.birthday == this.birthday &&
          other.friendIds == this.friendIds &&
          other.bubbleId == this.bubbleId &&
          other.contactIds == this.contactIds &&
          other.onlineStatus == this.onlineStatus &&
          other.bubbleStatus == this.bubbleStatus &&
          other.blockedUserIds == this.blockedUserIds &&
          other.pendingSentContactRequestIds ==
              this.pendingSentContactRequestIds &&
          other.pendingReceivedContactRequestIds ==
              this.pendingReceivedContactRequestIds &&
          other.hasCompletedOnboarding == this.hasCompletedOnboarding &&
          other.cachedAt == this.cachedAt &&
          other.lastRefreshed == this.lastRefreshed &&
          other.lastAccessed == this.lastAccessed &&
          other.isStale == this.isStale);
}

class UserProfilesCompanion extends UpdateCompanion<DriftUserProfile> {
  final Value<String> id;
  final Value<String?> email;
  final Value<String?> username;
  final Value<String?> firstName;
  final Value<String?> lastName;
  final Value<String?> profilePictureUrl;
  final Value<DateTime?> birthday;
  final Value<String?> friendIds;
  final Value<String?> bubbleId;
  final Value<String?> contactIds;
  final Value<String> onlineStatus;
  final Value<String> bubbleStatus;
  final Value<String?> blockedUserIds;
  final Value<String?> pendingSentContactRequestIds;
  final Value<String?> pendingReceivedContactRequestIds;
  final Value<bool> hasCompletedOnboarding;
  final Value<DateTime> cachedAt;
  final Value<DateTime> lastRefreshed;
  final Value<DateTime> lastAccessed;
  final Value<bool> isStale;
  final Value<int> rowid;
  const UserProfilesCompanion({
    this.id = const Value.absent(),
    this.email = const Value.absent(),
    this.username = const Value.absent(),
    this.firstName = const Value.absent(),
    this.lastName = const Value.absent(),
    this.profilePictureUrl = const Value.absent(),
    this.birthday = const Value.absent(),
    this.friendIds = const Value.absent(),
    this.bubbleId = const Value.absent(),
    this.contactIds = const Value.absent(),
    this.onlineStatus = const Value.absent(),
    this.bubbleStatus = const Value.absent(),
    this.blockedUserIds = const Value.absent(),
    this.pendingSentContactRequestIds = const Value.absent(),
    this.pendingReceivedContactRequestIds = const Value.absent(),
    this.hasCompletedOnboarding = const Value.absent(),
    this.cachedAt = const Value.absent(),
    this.lastRefreshed = const Value.absent(),
    this.lastAccessed = const Value.absent(),
    this.isStale = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  UserProfilesCompanion.insert({
    required String id,
    this.email = const Value.absent(),
    this.username = const Value.absent(),
    this.firstName = const Value.absent(),
    this.lastName = const Value.absent(),
    this.profilePictureUrl = const Value.absent(),
    this.birthday = const Value.absent(),
    this.friendIds = const Value.absent(),
    this.bubbleId = const Value.absent(),
    this.contactIds = const Value.absent(),
    this.onlineStatus = const Value.absent(),
    this.bubbleStatus = const Value.absent(),
    this.blockedUserIds = const Value.absent(),
    this.pendingSentContactRequestIds = const Value.absent(),
    this.pendingReceivedContactRequestIds = const Value.absent(),
    this.hasCompletedOnboarding = const Value.absent(),
    required DateTime cachedAt,
    required DateTime lastRefreshed,
    required DateTime lastAccessed,
    this.isStale = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        cachedAt = Value(cachedAt),
        lastRefreshed = Value(lastRefreshed),
        lastAccessed = Value(lastAccessed);
  static Insertable<DriftUserProfile> custom({
    Expression<String>? id,
    Expression<String>? email,
    Expression<String>? username,
    Expression<String>? firstName,
    Expression<String>? lastName,
    Expression<String>? profilePictureUrl,
    Expression<DateTime>? birthday,
    Expression<String>? friendIds,
    Expression<String>? bubbleId,
    Expression<String>? contactIds,
    Expression<String>? onlineStatus,
    Expression<String>? bubbleStatus,
    Expression<String>? blockedUserIds,
    Expression<String>? pendingSentContactRequestIds,
    Expression<String>? pendingReceivedContactRequestIds,
    Expression<bool>? hasCompletedOnboarding,
    Expression<DateTime>? cachedAt,
    Expression<DateTime>? lastRefreshed,
    Expression<DateTime>? lastAccessed,
    Expression<bool>? isStale,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (email != null) 'email': email,
      if (username != null) 'username': username,
      if (firstName != null) 'first_name': firstName,
      if (lastName != null) 'last_name': lastName,
      if (profilePictureUrl != null) 'profile_picture_url': profilePictureUrl,
      if (birthday != null) 'birthday': birthday,
      if (friendIds != null) 'friend_ids': friendIds,
      if (bubbleId != null) 'bubble_id': bubbleId,
      if (contactIds != null) 'contact_ids': contactIds,
      if (onlineStatus != null) 'online_status': onlineStatus,
      if (bubbleStatus != null) 'bubble_status': bubbleStatus,
      if (blockedUserIds != null) 'blocked_user_ids': blockedUserIds,
      if (pendingSentContactRequestIds != null)
        'pending_sent_contact_request_ids': pendingSentContactRequestIds,
      if (pendingReceivedContactRequestIds != null)
        'pending_received_contact_request_ids':
            pendingReceivedContactRequestIds,
      if (hasCompletedOnboarding != null)
        'has_completed_onboarding': hasCompletedOnboarding,
      if (cachedAt != null) 'cached_at': cachedAt,
      if (lastRefreshed != null) 'last_refreshed': lastRefreshed,
      if (lastAccessed != null) 'last_accessed': lastAccessed,
      if (isStale != null) 'is_stale': isStale,
      if (rowid != null) 'rowid': rowid,
    });
  }

  UserProfilesCompanion copyWith(
      {Value<String>? id,
      Value<String?>? email,
      Value<String?>? username,
      Value<String?>? firstName,
      Value<String?>? lastName,
      Value<String?>? profilePictureUrl,
      Value<DateTime?>? birthday,
      Value<String?>? friendIds,
      Value<String?>? bubbleId,
      Value<String?>? contactIds,
      Value<String>? onlineStatus,
      Value<String>? bubbleStatus,
      Value<String?>? blockedUserIds,
      Value<String?>? pendingSentContactRequestIds,
      Value<String?>? pendingReceivedContactRequestIds,
      Value<bool>? hasCompletedOnboarding,
      Value<DateTime>? cachedAt,
      Value<DateTime>? lastRefreshed,
      Value<DateTime>? lastAccessed,
      Value<bool>? isStale,
      Value<int>? rowid}) {
    return UserProfilesCompanion(
      id: id ?? this.id,
      email: email ?? this.email,
      username: username ?? this.username,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      profilePictureUrl: profilePictureUrl ?? this.profilePictureUrl,
      birthday: birthday ?? this.birthday,
      friendIds: friendIds ?? this.friendIds,
      bubbleId: bubbleId ?? this.bubbleId,
      contactIds: contactIds ?? this.contactIds,
      onlineStatus: onlineStatus ?? this.onlineStatus,
      bubbleStatus: bubbleStatus ?? this.bubbleStatus,
      blockedUserIds: blockedUserIds ?? this.blockedUserIds,
      pendingSentContactRequestIds:
          pendingSentContactRequestIds ?? this.pendingSentContactRequestIds,
      pendingReceivedContactRequestIds: pendingReceivedContactRequestIds ??
          this.pendingReceivedContactRequestIds,
      hasCompletedOnboarding:
          hasCompletedOnboarding ?? this.hasCompletedOnboarding,
      cachedAt: cachedAt ?? this.cachedAt,
      lastRefreshed: lastRefreshed ?? this.lastRefreshed,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      isStale: isStale ?? this.isStale,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (email.present) {
      map['email'] = Variable<String>(email.value);
    }
    if (username.present) {
      map['username'] = Variable<String>(username.value);
    }
    if (firstName.present) {
      map['first_name'] = Variable<String>(firstName.value);
    }
    if (lastName.present) {
      map['last_name'] = Variable<String>(lastName.value);
    }
    if (profilePictureUrl.present) {
      map['profile_picture_url'] = Variable<String>(profilePictureUrl.value);
    }
    if (birthday.present) {
      map['birthday'] = Variable<DateTime>(birthday.value);
    }
    if (friendIds.present) {
      map['friend_ids'] = Variable<String>(friendIds.value);
    }
    if (bubbleId.present) {
      map['bubble_id'] = Variable<String>(bubbleId.value);
    }
    if (contactIds.present) {
      map['contact_ids'] = Variable<String>(contactIds.value);
    }
    if (onlineStatus.present) {
      map['online_status'] = Variable<String>(onlineStatus.value);
    }
    if (bubbleStatus.present) {
      map['bubble_status'] = Variable<String>(bubbleStatus.value);
    }
    if (blockedUserIds.present) {
      map['blocked_user_ids'] = Variable<String>(blockedUserIds.value);
    }
    if (pendingSentContactRequestIds.present) {
      map['pending_sent_contact_request_ids'] =
          Variable<String>(pendingSentContactRequestIds.value);
    }
    if (pendingReceivedContactRequestIds.present) {
      map['pending_received_contact_request_ids'] =
          Variable<String>(pendingReceivedContactRequestIds.value);
    }
    if (hasCompletedOnboarding.present) {
      map['has_completed_onboarding'] =
          Variable<bool>(hasCompletedOnboarding.value);
    }
    if (cachedAt.present) {
      map['cached_at'] = Variable<DateTime>(cachedAt.value);
    }
    if (lastRefreshed.present) {
      map['last_refreshed'] = Variable<DateTime>(lastRefreshed.value);
    }
    if (lastAccessed.present) {
      map['last_accessed'] = Variable<DateTime>(lastAccessed.value);
    }
    if (isStale.present) {
      map['is_stale'] = Variable<bool>(isStale.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('UserProfilesCompanion(')
          ..write('id: $id, ')
          ..write('email: $email, ')
          ..write('username: $username, ')
          ..write('firstName: $firstName, ')
          ..write('lastName: $lastName, ')
          ..write('profilePictureUrl: $profilePictureUrl, ')
          ..write('birthday: $birthday, ')
          ..write('friendIds: $friendIds, ')
          ..write('bubbleId: $bubbleId, ')
          ..write('contactIds: $contactIds, ')
          ..write('onlineStatus: $onlineStatus, ')
          ..write('bubbleStatus: $bubbleStatus, ')
          ..write('blockedUserIds: $blockedUserIds, ')
          ..write(
              'pendingSentContactRequestIds: $pendingSentContactRequestIds, ')
          ..write(
              'pendingReceivedContactRequestIds: $pendingReceivedContactRequestIds, ')
          ..write('hasCompletedOnboarding: $hasCompletedOnboarding, ')
          ..write('cachedAt: $cachedAt, ')
          ..write('lastRefreshed: $lastRefreshed, ')
          ..write('lastAccessed: $lastAccessed, ')
          ..write('isStale: $isStale, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$AppDatabase extends GeneratedDatabase {
  _$AppDatabase(QueryExecutor e) : super(e);
  $AppDatabaseManager get managers => $AppDatabaseManager(this);
  late final $ChatMessagesTable chatMessages = $ChatMessagesTable(this);
  late final $UserSettingsTable userSettings = $UserSettingsTable(this);
  late final $ChatMetadatasTable chatMetadatas = $ChatMetadatasTable(this);
  late final $UnreadCountsTable unreadCounts = $UnreadCountsTable(this);
  late final $DraftMessagesTable draftMessages = $DraftMessagesTable(this);
  late final $FcmMessagesTable fcmMessages = $FcmMessagesTable(this);
  late final $ProfilePicturesTable profilePictures =
      $ProfilePicturesTable(this);
  late final $ContactRequestsTable contactRequests =
      $ContactRequestsTable(this);
  late final $FriendRequestsTable friendRequests = $FriendRequestsTable(this);
  late final $UserProfilesTable userProfiles = $UserProfilesTable(this);
  late final ChatMessageDao chatMessageDao =
      ChatMessageDao(this as AppDatabase);
  late final UserSettingDao userSettingDao =
      UserSettingDao(this as AppDatabase);
  late final ChatMetadataDao chatMetadataDao =
      ChatMetadataDao(this as AppDatabase);
  late final UnreadCountDao unreadCountDao =
      UnreadCountDao(this as AppDatabase);
  late final DraftMessageDao draftMessageDao =
      DraftMessageDao(this as AppDatabase);
  late final FcmMessageDao fcmMessageDao = FcmMessageDao(this as AppDatabase);
  late final ProfilePictureDao profilePictureDao =
      ProfilePictureDao(this as AppDatabase);
  late final ContactRequestDao contactRequestDao =
      ContactRequestDao(this as AppDatabase);
  late final FriendRequestDao friendRequestDao =
      FriendRequestDao(this as AppDatabase);
  late final UserProfileDao userProfileDao =
      UserProfileDao(this as AppDatabase);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities => [
        chatMessages,
        userSettings,
        chatMetadatas,
        unreadCounts,
        draftMessages,
        fcmMessages,
        profilePictures,
        contactRequests,
        friendRequests,
        userProfiles
      ];
}

typedef $$ChatMessagesTableCreateCompanionBuilder = ChatMessagesCompanion
    Function({
  required String id,
  required String chatId,
  required String textContent,
  required DateTime timestamp,
  required String senderId,
  required domain_models.MediaType mediaType,
  Value<String?> mediaUrl,
  Value<String?> mediaThumbnail,
  Value<String?> mediaTitle,
  Value<String?> mediaDescription,
  required domain_models.MessageDeliveryStatus deliveryStatus,
  Value<Map<String, dynamic>?> reactions,
  Value<String?> replyToMessageId,
  Value<bool> isForwarded,
  Value<int> rowid,
});
typedef $$ChatMessagesTableUpdateCompanionBuilder = ChatMessagesCompanion
    Function({
  Value<String> id,
  Value<String> chatId,
  Value<String> textContent,
  Value<DateTime> timestamp,
  Value<String> senderId,
  Value<domain_models.MediaType> mediaType,
  Value<String?> mediaUrl,
  Value<String?> mediaThumbnail,
  Value<String?> mediaTitle,
  Value<String?> mediaDescription,
  Value<domain_models.MessageDeliveryStatus> deliveryStatus,
  Value<Map<String, dynamic>?> reactions,
  Value<String?> replyToMessageId,
  Value<bool> isForwarded,
  Value<int> rowid,
});

class $$ChatMessagesTableFilterComposer
    extends Composer<_$AppDatabase, $ChatMessagesTable> {
  $$ChatMessagesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get chatId => $composableBuilder(
      column: $table.chatId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get textContent => $composableBuilder(
      column: $table.textContent, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get timestamp => $composableBuilder(
      column: $table.timestamp, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get senderId => $composableBuilder(
      column: $table.senderId, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<domain_models.MediaType,
          domain_models.MediaType, int>
      get mediaType => $composableBuilder(
          column: $table.mediaType,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<String> get mediaUrl => $composableBuilder(
      column: $table.mediaUrl, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get mediaThumbnail => $composableBuilder(
      column: $table.mediaThumbnail,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get mediaTitle => $composableBuilder(
      column: $table.mediaTitle, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get mediaDescription => $composableBuilder(
      column: $table.mediaDescription,
      builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<domain_models.MessageDeliveryStatus,
          domain_models.MessageDeliveryStatus, int>
      get deliveryStatus => $composableBuilder(
          column: $table.deliveryStatus,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnWithTypeConverterFilters<Map<String, dynamic>?, Map<String, dynamic>,
          String>
      get reactions => $composableBuilder(
          column: $table.reactions,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<String> get replyToMessageId => $composableBuilder(
      column: $table.replyToMessageId,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isForwarded => $composableBuilder(
      column: $table.isForwarded, builder: (column) => ColumnFilters(column));
}

class $$ChatMessagesTableOrderingComposer
    extends Composer<_$AppDatabase, $ChatMessagesTable> {
  $$ChatMessagesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get chatId => $composableBuilder(
      column: $table.chatId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get textContent => $composableBuilder(
      column: $table.textContent, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get timestamp => $composableBuilder(
      column: $table.timestamp, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get senderId => $composableBuilder(
      column: $table.senderId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get mediaType => $composableBuilder(
      column: $table.mediaType, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get mediaUrl => $composableBuilder(
      column: $table.mediaUrl, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get mediaThumbnail => $composableBuilder(
      column: $table.mediaThumbnail,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get mediaTitle => $composableBuilder(
      column: $table.mediaTitle, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get mediaDescription => $composableBuilder(
      column: $table.mediaDescription,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get deliveryStatus => $composableBuilder(
      column: $table.deliveryStatus,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get reactions => $composableBuilder(
      column: $table.reactions, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get replyToMessageId => $composableBuilder(
      column: $table.replyToMessageId,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isForwarded => $composableBuilder(
      column: $table.isForwarded, builder: (column) => ColumnOrderings(column));
}

class $$ChatMessagesTableAnnotationComposer
    extends Composer<_$AppDatabase, $ChatMessagesTable> {
  $$ChatMessagesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get chatId =>
      $composableBuilder(column: $table.chatId, builder: (column) => column);

  GeneratedColumn<String> get textContent => $composableBuilder(
      column: $table.textContent, builder: (column) => column);

  GeneratedColumn<DateTime> get timestamp =>
      $composableBuilder(column: $table.timestamp, builder: (column) => column);

  GeneratedColumn<String> get senderId =>
      $composableBuilder(column: $table.senderId, builder: (column) => column);

  GeneratedColumnWithTypeConverter<domain_models.MediaType, int>
      get mediaType => $composableBuilder(
          column: $table.mediaType, builder: (column) => column);

  GeneratedColumn<String> get mediaUrl =>
      $composableBuilder(column: $table.mediaUrl, builder: (column) => column);

  GeneratedColumn<String> get mediaThumbnail => $composableBuilder(
      column: $table.mediaThumbnail, builder: (column) => column);

  GeneratedColumn<String> get mediaTitle => $composableBuilder(
      column: $table.mediaTitle, builder: (column) => column);

  GeneratedColumn<String> get mediaDescription => $composableBuilder(
      column: $table.mediaDescription, builder: (column) => column);

  GeneratedColumnWithTypeConverter<domain_models.MessageDeliveryStatus, int>
      get deliveryStatus => $composableBuilder(
          column: $table.deliveryStatus, builder: (column) => column);

  GeneratedColumnWithTypeConverter<Map<String, dynamic>?, String>
      get reactions => $composableBuilder(
          column: $table.reactions, builder: (column) => column);

  GeneratedColumn<String> get replyToMessageId => $composableBuilder(
      column: $table.replyToMessageId, builder: (column) => column);

  GeneratedColumn<bool> get isForwarded => $composableBuilder(
      column: $table.isForwarded, builder: (column) => column);
}

class $$ChatMessagesTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ChatMessagesTable,
    DriftChatMessage,
    $$ChatMessagesTableFilterComposer,
    $$ChatMessagesTableOrderingComposer,
    $$ChatMessagesTableAnnotationComposer,
    $$ChatMessagesTableCreateCompanionBuilder,
    $$ChatMessagesTableUpdateCompanionBuilder,
    (
      DriftChatMessage,
      BaseReferences<_$AppDatabase, $ChatMessagesTable, DriftChatMessage>
    ),
    DriftChatMessage,
    PrefetchHooks Function()> {
  $$ChatMessagesTableTableManager(_$AppDatabase db, $ChatMessagesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ChatMessagesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ChatMessagesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ChatMessagesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> chatId = const Value.absent(),
            Value<String> textContent = const Value.absent(),
            Value<DateTime> timestamp = const Value.absent(),
            Value<String> senderId = const Value.absent(),
            Value<domain_models.MediaType> mediaType = const Value.absent(),
            Value<String?> mediaUrl = const Value.absent(),
            Value<String?> mediaThumbnail = const Value.absent(),
            Value<String?> mediaTitle = const Value.absent(),
            Value<String?> mediaDescription = const Value.absent(),
            Value<domain_models.MessageDeliveryStatus> deliveryStatus =
                const Value.absent(),
            Value<Map<String, dynamic>?> reactions = const Value.absent(),
            Value<String?> replyToMessageId = const Value.absent(),
            Value<bool> isForwarded = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ChatMessagesCompanion(
            id: id,
            chatId: chatId,
            textContent: textContent,
            timestamp: timestamp,
            senderId: senderId,
            mediaType: mediaType,
            mediaUrl: mediaUrl,
            mediaThumbnail: mediaThumbnail,
            mediaTitle: mediaTitle,
            mediaDescription: mediaDescription,
            deliveryStatus: deliveryStatus,
            reactions: reactions,
            replyToMessageId: replyToMessageId,
            isForwarded: isForwarded,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String chatId,
            required String textContent,
            required DateTime timestamp,
            required String senderId,
            required domain_models.MediaType mediaType,
            Value<String?> mediaUrl = const Value.absent(),
            Value<String?> mediaThumbnail = const Value.absent(),
            Value<String?> mediaTitle = const Value.absent(),
            Value<String?> mediaDescription = const Value.absent(),
            required domain_models.MessageDeliveryStatus deliveryStatus,
            Value<Map<String, dynamic>?> reactions = const Value.absent(),
            Value<String?> replyToMessageId = const Value.absent(),
            Value<bool> isForwarded = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ChatMessagesCompanion.insert(
            id: id,
            chatId: chatId,
            textContent: textContent,
            timestamp: timestamp,
            senderId: senderId,
            mediaType: mediaType,
            mediaUrl: mediaUrl,
            mediaThumbnail: mediaThumbnail,
            mediaTitle: mediaTitle,
            mediaDescription: mediaDescription,
            deliveryStatus: deliveryStatus,
            reactions: reactions,
            replyToMessageId: replyToMessageId,
            isForwarded: isForwarded,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$ChatMessagesTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $ChatMessagesTable,
    DriftChatMessage,
    $$ChatMessagesTableFilterComposer,
    $$ChatMessagesTableOrderingComposer,
    $$ChatMessagesTableAnnotationComposer,
    $$ChatMessagesTableCreateCompanionBuilder,
    $$ChatMessagesTableUpdateCompanionBuilder,
    (
      DriftChatMessage,
      BaseReferences<_$AppDatabase, $ChatMessagesTable, DriftChatMessage>
    ),
    DriftChatMessage,
    PrefetchHooks Function()>;
typedef $$UserSettingsTableCreateCompanionBuilder = UserSettingsCompanion
    Function({
  required String settingKey,
  required String settingValue,
  Value<int> rowid,
});
typedef $$UserSettingsTableUpdateCompanionBuilder = UserSettingsCompanion
    Function({
  Value<String> settingKey,
  Value<String> settingValue,
  Value<int> rowid,
});

class $$UserSettingsTableFilterComposer
    extends Composer<_$AppDatabase, $UserSettingsTable> {
  $$UserSettingsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get settingKey => $composableBuilder(
      column: $table.settingKey, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get settingValue => $composableBuilder(
      column: $table.settingValue, builder: (column) => ColumnFilters(column));
}

class $$UserSettingsTableOrderingComposer
    extends Composer<_$AppDatabase, $UserSettingsTable> {
  $$UserSettingsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get settingKey => $composableBuilder(
      column: $table.settingKey, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get settingValue => $composableBuilder(
      column: $table.settingValue,
      builder: (column) => ColumnOrderings(column));
}

class $$UserSettingsTableAnnotationComposer
    extends Composer<_$AppDatabase, $UserSettingsTable> {
  $$UserSettingsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get settingKey => $composableBuilder(
      column: $table.settingKey, builder: (column) => column);

  GeneratedColumn<String> get settingValue => $composableBuilder(
      column: $table.settingValue, builder: (column) => column);
}

class $$UserSettingsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $UserSettingsTable,
    DriftUserSetting,
    $$UserSettingsTableFilterComposer,
    $$UserSettingsTableOrderingComposer,
    $$UserSettingsTableAnnotationComposer,
    $$UserSettingsTableCreateCompanionBuilder,
    $$UserSettingsTableUpdateCompanionBuilder,
    (
      DriftUserSetting,
      BaseReferences<_$AppDatabase, $UserSettingsTable, DriftUserSetting>
    ),
    DriftUserSetting,
    PrefetchHooks Function()> {
  $$UserSettingsTableTableManager(_$AppDatabase db, $UserSettingsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$UserSettingsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$UserSettingsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$UserSettingsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> settingKey = const Value.absent(),
            Value<String> settingValue = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              UserSettingsCompanion(
            settingKey: settingKey,
            settingValue: settingValue,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String settingKey,
            required String settingValue,
            Value<int> rowid = const Value.absent(),
          }) =>
              UserSettingsCompanion.insert(
            settingKey: settingKey,
            settingValue: settingValue,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$UserSettingsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $UserSettingsTable,
    DriftUserSetting,
    $$UserSettingsTableFilterComposer,
    $$UserSettingsTableOrderingComposer,
    $$UserSettingsTableAnnotationComposer,
    $$UserSettingsTableCreateCompanionBuilder,
    $$UserSettingsTableUpdateCompanionBuilder,
    (
      DriftUserSetting,
      BaseReferences<_$AppDatabase, $UserSettingsTable, DriftUserSetting>
    ),
    DriftUserSetting,
    PrefetchHooks Function()>;
typedef $$ChatMetadatasTableCreateCompanionBuilder = ChatMetadatasCompanion
    Function({
  required String chatId,
  required Map<String, dynamic> metadata,
  Value<int> rowid,
});
typedef $$ChatMetadatasTableUpdateCompanionBuilder = ChatMetadatasCompanion
    Function({
  Value<String> chatId,
  Value<Map<String, dynamic>> metadata,
  Value<int> rowid,
});

class $$ChatMetadatasTableFilterComposer
    extends Composer<_$AppDatabase, $ChatMetadatasTable> {
  $$ChatMetadatasTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get chatId => $composableBuilder(
      column: $table.chatId, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<Map<String, dynamic>, Map<String, dynamic>,
          String>
      get metadata => $composableBuilder(
          column: $table.metadata,
          builder: (column) => ColumnWithTypeConverterFilters(column));
}

class $$ChatMetadatasTableOrderingComposer
    extends Composer<_$AppDatabase, $ChatMetadatasTable> {
  $$ChatMetadatasTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get chatId => $composableBuilder(
      column: $table.chatId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get metadata => $composableBuilder(
      column: $table.metadata, builder: (column) => ColumnOrderings(column));
}

class $$ChatMetadatasTableAnnotationComposer
    extends Composer<_$AppDatabase, $ChatMetadatasTable> {
  $$ChatMetadatasTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get chatId =>
      $composableBuilder(column: $table.chatId, builder: (column) => column);

  GeneratedColumnWithTypeConverter<Map<String, dynamic>, String> get metadata =>
      $composableBuilder(column: $table.metadata, builder: (column) => column);
}

class $$ChatMetadatasTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ChatMetadatasTable,
    DriftChatMetadata,
    $$ChatMetadatasTableFilterComposer,
    $$ChatMetadatasTableOrderingComposer,
    $$ChatMetadatasTableAnnotationComposer,
    $$ChatMetadatasTableCreateCompanionBuilder,
    $$ChatMetadatasTableUpdateCompanionBuilder,
    (
      DriftChatMetadata,
      BaseReferences<_$AppDatabase, $ChatMetadatasTable, DriftChatMetadata>
    ),
    DriftChatMetadata,
    PrefetchHooks Function()> {
  $$ChatMetadatasTableTableManager(_$AppDatabase db, $ChatMetadatasTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ChatMetadatasTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ChatMetadatasTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ChatMetadatasTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> chatId = const Value.absent(),
            Value<Map<String, dynamic>> metadata = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ChatMetadatasCompanion(
            chatId: chatId,
            metadata: metadata,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String chatId,
            required Map<String, dynamic> metadata,
            Value<int> rowid = const Value.absent(),
          }) =>
              ChatMetadatasCompanion.insert(
            chatId: chatId,
            metadata: metadata,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$ChatMetadatasTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $ChatMetadatasTable,
    DriftChatMetadata,
    $$ChatMetadatasTableFilterComposer,
    $$ChatMetadatasTableOrderingComposer,
    $$ChatMetadatasTableAnnotationComposer,
    $$ChatMetadatasTableCreateCompanionBuilder,
    $$ChatMetadatasTableUpdateCompanionBuilder,
    (
      DriftChatMetadata,
      BaseReferences<_$AppDatabase, $ChatMetadatasTable, DriftChatMetadata>
    ),
    DriftChatMetadata,
    PrefetchHooks Function()>;
typedef $$UnreadCountsTableCreateCompanionBuilder = UnreadCountsCompanion
    Function({
  required String chatId,
  required int count,
  Value<int> rowid,
});
typedef $$UnreadCountsTableUpdateCompanionBuilder = UnreadCountsCompanion
    Function({
  Value<String> chatId,
  Value<int> count,
  Value<int> rowid,
});

class $$UnreadCountsTableFilterComposer
    extends Composer<_$AppDatabase, $UnreadCountsTable> {
  $$UnreadCountsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get chatId => $composableBuilder(
      column: $table.chatId, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get count => $composableBuilder(
      column: $table.count, builder: (column) => ColumnFilters(column));
}

class $$UnreadCountsTableOrderingComposer
    extends Composer<_$AppDatabase, $UnreadCountsTable> {
  $$UnreadCountsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get chatId => $composableBuilder(
      column: $table.chatId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get count => $composableBuilder(
      column: $table.count, builder: (column) => ColumnOrderings(column));
}

class $$UnreadCountsTableAnnotationComposer
    extends Composer<_$AppDatabase, $UnreadCountsTable> {
  $$UnreadCountsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get chatId =>
      $composableBuilder(column: $table.chatId, builder: (column) => column);

  GeneratedColumn<int> get count =>
      $composableBuilder(column: $table.count, builder: (column) => column);
}

class $$UnreadCountsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $UnreadCountsTable,
    DriftUnreadCount,
    $$UnreadCountsTableFilterComposer,
    $$UnreadCountsTableOrderingComposer,
    $$UnreadCountsTableAnnotationComposer,
    $$UnreadCountsTableCreateCompanionBuilder,
    $$UnreadCountsTableUpdateCompanionBuilder,
    (
      DriftUnreadCount,
      BaseReferences<_$AppDatabase, $UnreadCountsTable, DriftUnreadCount>
    ),
    DriftUnreadCount,
    PrefetchHooks Function()> {
  $$UnreadCountsTableTableManager(_$AppDatabase db, $UnreadCountsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$UnreadCountsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$UnreadCountsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$UnreadCountsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> chatId = const Value.absent(),
            Value<int> count = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              UnreadCountsCompanion(
            chatId: chatId,
            count: count,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String chatId,
            required int count,
            Value<int> rowid = const Value.absent(),
          }) =>
              UnreadCountsCompanion.insert(
            chatId: chatId,
            count: count,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$UnreadCountsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $UnreadCountsTable,
    DriftUnreadCount,
    $$UnreadCountsTableFilterComposer,
    $$UnreadCountsTableOrderingComposer,
    $$UnreadCountsTableAnnotationComposer,
    $$UnreadCountsTableCreateCompanionBuilder,
    $$UnreadCountsTableUpdateCompanionBuilder,
    (
      DriftUnreadCount,
      BaseReferences<_$AppDatabase, $UnreadCountsTable, DriftUnreadCount>
    ),
    DriftUnreadCount,
    PrefetchHooks Function()>;
typedef $$DraftMessagesTableCreateCompanionBuilder = DraftMessagesCompanion
    Function({
  required String chatId,
  required String draftText,
  Value<int> rowid,
});
typedef $$DraftMessagesTableUpdateCompanionBuilder = DraftMessagesCompanion
    Function({
  Value<String> chatId,
  Value<String> draftText,
  Value<int> rowid,
});

class $$DraftMessagesTableFilterComposer
    extends Composer<_$AppDatabase, $DraftMessagesTable> {
  $$DraftMessagesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get chatId => $composableBuilder(
      column: $table.chatId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get draftText => $composableBuilder(
      column: $table.draftText, builder: (column) => ColumnFilters(column));
}

class $$DraftMessagesTableOrderingComposer
    extends Composer<_$AppDatabase, $DraftMessagesTable> {
  $$DraftMessagesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get chatId => $composableBuilder(
      column: $table.chatId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get draftText => $composableBuilder(
      column: $table.draftText, builder: (column) => ColumnOrderings(column));
}

class $$DraftMessagesTableAnnotationComposer
    extends Composer<_$AppDatabase, $DraftMessagesTable> {
  $$DraftMessagesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get chatId =>
      $composableBuilder(column: $table.chatId, builder: (column) => column);

  GeneratedColumn<String> get draftText =>
      $composableBuilder(column: $table.draftText, builder: (column) => column);
}

class $$DraftMessagesTableTableManager extends RootTableManager<
    _$AppDatabase,
    $DraftMessagesTable,
    DriftDraftMessage,
    $$DraftMessagesTableFilterComposer,
    $$DraftMessagesTableOrderingComposer,
    $$DraftMessagesTableAnnotationComposer,
    $$DraftMessagesTableCreateCompanionBuilder,
    $$DraftMessagesTableUpdateCompanionBuilder,
    (
      DriftDraftMessage,
      BaseReferences<_$AppDatabase, $DraftMessagesTable, DriftDraftMessage>
    ),
    DriftDraftMessage,
    PrefetchHooks Function()> {
  $$DraftMessagesTableTableManager(_$AppDatabase db, $DraftMessagesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$DraftMessagesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$DraftMessagesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$DraftMessagesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> chatId = const Value.absent(),
            Value<String> draftText = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              DraftMessagesCompanion(
            chatId: chatId,
            draftText: draftText,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String chatId,
            required String draftText,
            Value<int> rowid = const Value.absent(),
          }) =>
              DraftMessagesCompanion.insert(
            chatId: chatId,
            draftText: draftText,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$DraftMessagesTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $DraftMessagesTable,
    DriftDraftMessage,
    $$DraftMessagesTableFilterComposer,
    $$DraftMessagesTableOrderingComposer,
    $$DraftMessagesTableAnnotationComposer,
    $$DraftMessagesTableCreateCompanionBuilder,
    $$DraftMessagesTableUpdateCompanionBuilder,
    (
      DriftDraftMessage,
      BaseReferences<_$AppDatabase, $DraftMessagesTable, DriftDraftMessage>
    ),
    DriftDraftMessage,
    PrefetchHooks Function()>;
typedef $$FcmMessagesTableCreateCompanionBuilder = FcmMessagesCompanion
    Function({
  Value<int> id,
  Value<String?> messageId,
  required Map<String, dynamic> data,
  required DateTime receivedTimestamp,
  Value<String?> notificationTitle,
  Value<String?> notificationBody,
});
typedef $$FcmMessagesTableUpdateCompanionBuilder = FcmMessagesCompanion
    Function({
  Value<int> id,
  Value<String?> messageId,
  Value<Map<String, dynamic>> data,
  Value<DateTime> receivedTimestamp,
  Value<String?> notificationTitle,
  Value<String?> notificationBody,
});

class $$FcmMessagesTableFilterComposer
    extends Composer<_$AppDatabase, $FcmMessagesTable> {
  $$FcmMessagesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get messageId => $composableBuilder(
      column: $table.messageId, builder: (column) => ColumnFilters(column));

  ColumnWithTypeConverterFilters<Map<String, dynamic>, Map<String, dynamic>,
          String>
      get data => $composableBuilder(
          column: $table.data,
          builder: (column) => ColumnWithTypeConverterFilters(column));

  ColumnFilters<DateTime> get receivedTimestamp => $composableBuilder(
      column: $table.receivedTimestamp,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get notificationTitle => $composableBuilder(
      column: $table.notificationTitle,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get notificationBody => $composableBuilder(
      column: $table.notificationBody,
      builder: (column) => ColumnFilters(column));
}

class $$FcmMessagesTableOrderingComposer
    extends Composer<_$AppDatabase, $FcmMessagesTable> {
  $$FcmMessagesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<int> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get messageId => $composableBuilder(
      column: $table.messageId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get data => $composableBuilder(
      column: $table.data, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get receivedTimestamp => $composableBuilder(
      column: $table.receivedTimestamp,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get notificationTitle => $composableBuilder(
      column: $table.notificationTitle,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get notificationBody => $composableBuilder(
      column: $table.notificationBody,
      builder: (column) => ColumnOrderings(column));
}

class $$FcmMessagesTableAnnotationComposer
    extends Composer<_$AppDatabase, $FcmMessagesTable> {
  $$FcmMessagesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<int> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get messageId =>
      $composableBuilder(column: $table.messageId, builder: (column) => column);

  GeneratedColumnWithTypeConverter<Map<String, dynamic>, String> get data =>
      $composableBuilder(column: $table.data, builder: (column) => column);

  GeneratedColumn<DateTime> get receivedTimestamp => $composableBuilder(
      column: $table.receivedTimestamp, builder: (column) => column);

  GeneratedColumn<String> get notificationTitle => $composableBuilder(
      column: $table.notificationTitle, builder: (column) => column);

  GeneratedColumn<String> get notificationBody => $composableBuilder(
      column: $table.notificationBody, builder: (column) => column);
}

class $$FcmMessagesTableTableManager extends RootTableManager<
    _$AppDatabase,
    $FcmMessagesTable,
    DriftFcmMessage,
    $$FcmMessagesTableFilterComposer,
    $$FcmMessagesTableOrderingComposer,
    $$FcmMessagesTableAnnotationComposer,
    $$FcmMessagesTableCreateCompanionBuilder,
    $$FcmMessagesTableUpdateCompanionBuilder,
    (
      DriftFcmMessage,
      BaseReferences<_$AppDatabase, $FcmMessagesTable, DriftFcmMessage>
    ),
    DriftFcmMessage,
    PrefetchHooks Function()> {
  $$FcmMessagesTableTableManager(_$AppDatabase db, $FcmMessagesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$FcmMessagesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$FcmMessagesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$FcmMessagesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> messageId = const Value.absent(),
            Value<Map<String, dynamic>> data = const Value.absent(),
            Value<DateTime> receivedTimestamp = const Value.absent(),
            Value<String?> notificationTitle = const Value.absent(),
            Value<String?> notificationBody = const Value.absent(),
          }) =>
              FcmMessagesCompanion(
            id: id,
            messageId: messageId,
            data: data,
            receivedTimestamp: receivedTimestamp,
            notificationTitle: notificationTitle,
            notificationBody: notificationBody,
          ),
          createCompanionCallback: ({
            Value<int> id = const Value.absent(),
            Value<String?> messageId = const Value.absent(),
            required Map<String, dynamic> data,
            required DateTime receivedTimestamp,
            Value<String?> notificationTitle = const Value.absent(),
            Value<String?> notificationBody = const Value.absent(),
          }) =>
              FcmMessagesCompanion.insert(
            id: id,
            messageId: messageId,
            data: data,
            receivedTimestamp: receivedTimestamp,
            notificationTitle: notificationTitle,
            notificationBody: notificationBody,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$FcmMessagesTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $FcmMessagesTable,
    DriftFcmMessage,
    $$FcmMessagesTableFilterComposer,
    $$FcmMessagesTableOrderingComposer,
    $$FcmMessagesTableAnnotationComposer,
    $$FcmMessagesTableCreateCompanionBuilder,
    $$FcmMessagesTableUpdateCompanionBuilder,
    (
      DriftFcmMessage,
      BaseReferences<_$AppDatabase, $FcmMessagesTable, DriftFcmMessage>
    ),
    DriftFcmMessage,
    PrefetchHooks Function()>;
typedef $$ProfilePicturesTableCreateCompanionBuilder = ProfilePicturesCompanion
    Function({
  required String userId,
  required String imageUrl,
  required String localPath,
  Value<String?> thumbnailPath,
  required int fileSize,
  required String format,
  required DateTime cachedAt,
  required DateTime lastAccessed,
  Value<bool> isActive,
  Value<int> rowid,
});
typedef $$ProfilePicturesTableUpdateCompanionBuilder = ProfilePicturesCompanion
    Function({
  Value<String> userId,
  Value<String> imageUrl,
  Value<String> localPath,
  Value<String?> thumbnailPath,
  Value<int> fileSize,
  Value<String> format,
  Value<DateTime> cachedAt,
  Value<DateTime> lastAccessed,
  Value<bool> isActive,
  Value<int> rowid,
});

class $$ProfilePicturesTableFilterComposer
    extends Composer<_$AppDatabase, $ProfilePicturesTable> {
  $$ProfilePicturesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get userId => $composableBuilder(
      column: $table.userId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get imageUrl => $composableBuilder(
      column: $table.imageUrl, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get localPath => $composableBuilder(
      column: $table.localPath, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get thumbnailPath => $composableBuilder(
      column: $table.thumbnailPath, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get fileSize => $composableBuilder(
      column: $table.fileSize, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get format => $composableBuilder(
      column: $table.format, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get cachedAt => $composableBuilder(
      column: $table.cachedAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get lastAccessed => $composableBuilder(
      column: $table.lastAccessed, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isActive => $composableBuilder(
      column: $table.isActive, builder: (column) => ColumnFilters(column));
}

class $$ProfilePicturesTableOrderingComposer
    extends Composer<_$AppDatabase, $ProfilePicturesTable> {
  $$ProfilePicturesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get userId => $composableBuilder(
      column: $table.userId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get imageUrl => $composableBuilder(
      column: $table.imageUrl, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get localPath => $composableBuilder(
      column: $table.localPath, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get thumbnailPath => $composableBuilder(
      column: $table.thumbnailPath,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get fileSize => $composableBuilder(
      column: $table.fileSize, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get format => $composableBuilder(
      column: $table.format, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get cachedAt => $composableBuilder(
      column: $table.cachedAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get lastAccessed => $composableBuilder(
      column: $table.lastAccessed,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isActive => $composableBuilder(
      column: $table.isActive, builder: (column) => ColumnOrderings(column));
}

class $$ProfilePicturesTableAnnotationComposer
    extends Composer<_$AppDatabase, $ProfilePicturesTable> {
  $$ProfilePicturesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get userId =>
      $composableBuilder(column: $table.userId, builder: (column) => column);

  GeneratedColumn<String> get imageUrl =>
      $composableBuilder(column: $table.imageUrl, builder: (column) => column);

  GeneratedColumn<String> get localPath =>
      $composableBuilder(column: $table.localPath, builder: (column) => column);

  GeneratedColumn<String> get thumbnailPath => $composableBuilder(
      column: $table.thumbnailPath, builder: (column) => column);

  GeneratedColumn<int> get fileSize =>
      $composableBuilder(column: $table.fileSize, builder: (column) => column);

  GeneratedColumn<String> get format =>
      $composableBuilder(column: $table.format, builder: (column) => column);

  GeneratedColumn<DateTime> get cachedAt =>
      $composableBuilder(column: $table.cachedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get lastAccessed => $composableBuilder(
      column: $table.lastAccessed, builder: (column) => column);

  GeneratedColumn<bool> get isActive =>
      $composableBuilder(column: $table.isActive, builder: (column) => column);
}

class $$ProfilePicturesTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ProfilePicturesTable,
    DriftProfilePicture,
    $$ProfilePicturesTableFilterComposer,
    $$ProfilePicturesTableOrderingComposer,
    $$ProfilePicturesTableAnnotationComposer,
    $$ProfilePicturesTableCreateCompanionBuilder,
    $$ProfilePicturesTableUpdateCompanionBuilder,
    (
      DriftProfilePicture,
      BaseReferences<_$AppDatabase, $ProfilePicturesTable, DriftProfilePicture>
    ),
    DriftProfilePicture,
    PrefetchHooks Function()> {
  $$ProfilePicturesTableTableManager(
      _$AppDatabase db, $ProfilePicturesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ProfilePicturesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ProfilePicturesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ProfilePicturesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> userId = const Value.absent(),
            Value<String> imageUrl = const Value.absent(),
            Value<String> localPath = const Value.absent(),
            Value<String?> thumbnailPath = const Value.absent(),
            Value<int> fileSize = const Value.absent(),
            Value<String> format = const Value.absent(),
            Value<DateTime> cachedAt = const Value.absent(),
            Value<DateTime> lastAccessed = const Value.absent(),
            Value<bool> isActive = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ProfilePicturesCompanion(
            userId: userId,
            imageUrl: imageUrl,
            localPath: localPath,
            thumbnailPath: thumbnailPath,
            fileSize: fileSize,
            format: format,
            cachedAt: cachedAt,
            lastAccessed: lastAccessed,
            isActive: isActive,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String userId,
            required String imageUrl,
            required String localPath,
            Value<String?> thumbnailPath = const Value.absent(),
            required int fileSize,
            required String format,
            required DateTime cachedAt,
            required DateTime lastAccessed,
            Value<bool> isActive = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ProfilePicturesCompanion.insert(
            userId: userId,
            imageUrl: imageUrl,
            localPath: localPath,
            thumbnailPath: thumbnailPath,
            fileSize: fileSize,
            format: format,
            cachedAt: cachedAt,
            lastAccessed: lastAccessed,
            isActive: isActive,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$ProfilePicturesTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $ProfilePicturesTable,
    DriftProfilePicture,
    $$ProfilePicturesTableFilterComposer,
    $$ProfilePicturesTableOrderingComposer,
    $$ProfilePicturesTableAnnotationComposer,
    $$ProfilePicturesTableCreateCompanionBuilder,
    $$ProfilePicturesTableUpdateCompanionBuilder,
    (
      DriftProfilePicture,
      BaseReferences<_$AppDatabase, $ProfilePicturesTable, DriftProfilePicture>
    ),
    DriftProfilePicture,
    PrefetchHooks Function()>;
typedef $$ContactRequestsTableCreateCompanionBuilder = ContactRequestsCompanion
    Function({
  required String id,
  required String requesterId,
  required String recipientId,
  required String status,
  Value<String?> message,
  required DateTime createdAt,
  required DateTime updatedAt,
  required DateTime cachedAt,
  Value<bool> isStale,
  Value<int> rowid,
});
typedef $$ContactRequestsTableUpdateCompanionBuilder = ContactRequestsCompanion
    Function({
  Value<String> id,
  Value<String> requesterId,
  Value<String> recipientId,
  Value<String> status,
  Value<String?> message,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<DateTime> cachedAt,
  Value<bool> isStale,
  Value<int> rowid,
});

class $$ContactRequestsTableFilterComposer
    extends Composer<_$AppDatabase, $ContactRequestsTable> {
  $$ContactRequestsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get requesterId => $composableBuilder(
      column: $table.requesterId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get recipientId => $composableBuilder(
      column: $table.recipientId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get message => $composableBuilder(
      column: $table.message, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get cachedAt => $composableBuilder(
      column: $table.cachedAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isStale => $composableBuilder(
      column: $table.isStale, builder: (column) => ColumnFilters(column));
}

class $$ContactRequestsTableOrderingComposer
    extends Composer<_$AppDatabase, $ContactRequestsTable> {
  $$ContactRequestsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get requesterId => $composableBuilder(
      column: $table.requesterId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get recipientId => $composableBuilder(
      column: $table.recipientId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get message => $composableBuilder(
      column: $table.message, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get cachedAt => $composableBuilder(
      column: $table.cachedAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isStale => $composableBuilder(
      column: $table.isStale, builder: (column) => ColumnOrderings(column));
}

class $$ContactRequestsTableAnnotationComposer
    extends Composer<_$AppDatabase, $ContactRequestsTable> {
  $$ContactRequestsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get requesterId => $composableBuilder(
      column: $table.requesterId, builder: (column) => column);

  GeneratedColumn<String> get recipientId => $composableBuilder(
      column: $table.recipientId, builder: (column) => column);

  GeneratedColumn<String> get status =>
      $composableBuilder(column: $table.status, builder: (column) => column);

  GeneratedColumn<String> get message =>
      $composableBuilder(column: $table.message, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get cachedAt =>
      $composableBuilder(column: $table.cachedAt, builder: (column) => column);

  GeneratedColumn<bool> get isStale =>
      $composableBuilder(column: $table.isStale, builder: (column) => column);
}

class $$ContactRequestsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $ContactRequestsTable,
    DriftContactRequest,
    $$ContactRequestsTableFilterComposer,
    $$ContactRequestsTableOrderingComposer,
    $$ContactRequestsTableAnnotationComposer,
    $$ContactRequestsTableCreateCompanionBuilder,
    $$ContactRequestsTableUpdateCompanionBuilder,
    (
      DriftContactRequest,
      BaseReferences<_$AppDatabase, $ContactRequestsTable, DriftContactRequest>
    ),
    DriftContactRequest,
    PrefetchHooks Function()> {
  $$ContactRequestsTableTableManager(
      _$AppDatabase db, $ContactRequestsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ContactRequestsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ContactRequestsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ContactRequestsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> requesterId = const Value.absent(),
            Value<String> recipientId = const Value.absent(),
            Value<String> status = const Value.absent(),
            Value<String?> message = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<DateTime> cachedAt = const Value.absent(),
            Value<bool> isStale = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ContactRequestsCompanion(
            id: id,
            requesterId: requesterId,
            recipientId: recipientId,
            status: status,
            message: message,
            createdAt: createdAt,
            updatedAt: updatedAt,
            cachedAt: cachedAt,
            isStale: isStale,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String requesterId,
            required String recipientId,
            required String status,
            Value<String?> message = const Value.absent(),
            required DateTime createdAt,
            required DateTime updatedAt,
            required DateTime cachedAt,
            Value<bool> isStale = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ContactRequestsCompanion.insert(
            id: id,
            requesterId: requesterId,
            recipientId: recipientId,
            status: status,
            message: message,
            createdAt: createdAt,
            updatedAt: updatedAt,
            cachedAt: cachedAt,
            isStale: isStale,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$ContactRequestsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $ContactRequestsTable,
    DriftContactRequest,
    $$ContactRequestsTableFilterComposer,
    $$ContactRequestsTableOrderingComposer,
    $$ContactRequestsTableAnnotationComposer,
    $$ContactRequestsTableCreateCompanionBuilder,
    $$ContactRequestsTableUpdateCompanionBuilder,
    (
      DriftContactRequest,
      BaseReferences<_$AppDatabase, $ContactRequestsTable, DriftContactRequest>
    ),
    DriftContactRequest,
    PrefetchHooks Function()>;
typedef $$FriendRequestsTableCreateCompanionBuilder = FriendRequestsCompanion
    Function({
  required String id,
  required String requesterId,
  required String recipientId,
  required String status,
  required String sourceBubbleId,
  Value<bool> autoGenerated,
  required DateTime createdAt,
  required DateTime updatedAt,
  required DateTime cachedAt,
  Value<bool> isStale,
  Value<int> rowid,
});
typedef $$FriendRequestsTableUpdateCompanionBuilder = FriendRequestsCompanion
    Function({
  Value<String> id,
  Value<String> requesterId,
  Value<String> recipientId,
  Value<String> status,
  Value<String> sourceBubbleId,
  Value<bool> autoGenerated,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<DateTime> cachedAt,
  Value<bool> isStale,
  Value<int> rowid,
});

class $$FriendRequestsTableFilterComposer
    extends Composer<_$AppDatabase, $FriendRequestsTable> {
  $$FriendRequestsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get requesterId => $composableBuilder(
      column: $table.requesterId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get recipientId => $composableBuilder(
      column: $table.recipientId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get sourceBubbleId => $composableBuilder(
      column: $table.sourceBubbleId,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get autoGenerated => $composableBuilder(
      column: $table.autoGenerated, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get cachedAt => $composableBuilder(
      column: $table.cachedAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isStale => $composableBuilder(
      column: $table.isStale, builder: (column) => ColumnFilters(column));
}

class $$FriendRequestsTableOrderingComposer
    extends Composer<_$AppDatabase, $FriendRequestsTable> {
  $$FriendRequestsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get requesterId => $composableBuilder(
      column: $table.requesterId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get recipientId => $composableBuilder(
      column: $table.recipientId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get status => $composableBuilder(
      column: $table.status, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get sourceBubbleId => $composableBuilder(
      column: $table.sourceBubbleId,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get autoGenerated => $composableBuilder(
      column: $table.autoGenerated,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get cachedAt => $composableBuilder(
      column: $table.cachedAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isStale => $composableBuilder(
      column: $table.isStale, builder: (column) => ColumnOrderings(column));
}

class $$FriendRequestsTableAnnotationComposer
    extends Composer<_$AppDatabase, $FriendRequestsTable> {
  $$FriendRequestsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get requesterId => $composableBuilder(
      column: $table.requesterId, builder: (column) => column);

  GeneratedColumn<String> get recipientId => $composableBuilder(
      column: $table.recipientId, builder: (column) => column);

  GeneratedColumn<String> get status =>
      $composableBuilder(column: $table.status, builder: (column) => column);

  GeneratedColumn<String> get sourceBubbleId => $composableBuilder(
      column: $table.sourceBubbleId, builder: (column) => column);

  GeneratedColumn<bool> get autoGenerated => $composableBuilder(
      column: $table.autoGenerated, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get cachedAt =>
      $composableBuilder(column: $table.cachedAt, builder: (column) => column);

  GeneratedColumn<bool> get isStale =>
      $composableBuilder(column: $table.isStale, builder: (column) => column);
}

class $$FriendRequestsTableTableManager extends RootTableManager<
    _$AppDatabase,
    $FriendRequestsTable,
    DriftFriendRequest,
    $$FriendRequestsTableFilterComposer,
    $$FriendRequestsTableOrderingComposer,
    $$FriendRequestsTableAnnotationComposer,
    $$FriendRequestsTableCreateCompanionBuilder,
    $$FriendRequestsTableUpdateCompanionBuilder,
    (
      DriftFriendRequest,
      BaseReferences<_$AppDatabase, $FriendRequestsTable, DriftFriendRequest>
    ),
    DriftFriendRequest,
    PrefetchHooks Function()> {
  $$FriendRequestsTableTableManager(
      _$AppDatabase db, $FriendRequestsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$FriendRequestsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$FriendRequestsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$FriendRequestsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> requesterId = const Value.absent(),
            Value<String> recipientId = const Value.absent(),
            Value<String> status = const Value.absent(),
            Value<String> sourceBubbleId = const Value.absent(),
            Value<bool> autoGenerated = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<DateTime> cachedAt = const Value.absent(),
            Value<bool> isStale = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              FriendRequestsCompanion(
            id: id,
            requesterId: requesterId,
            recipientId: recipientId,
            status: status,
            sourceBubbleId: sourceBubbleId,
            autoGenerated: autoGenerated,
            createdAt: createdAt,
            updatedAt: updatedAt,
            cachedAt: cachedAt,
            isStale: isStale,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String requesterId,
            required String recipientId,
            required String status,
            required String sourceBubbleId,
            Value<bool> autoGenerated = const Value.absent(),
            required DateTime createdAt,
            required DateTime updatedAt,
            required DateTime cachedAt,
            Value<bool> isStale = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              FriendRequestsCompanion.insert(
            id: id,
            requesterId: requesterId,
            recipientId: recipientId,
            status: status,
            sourceBubbleId: sourceBubbleId,
            autoGenerated: autoGenerated,
            createdAt: createdAt,
            updatedAt: updatedAt,
            cachedAt: cachedAt,
            isStale: isStale,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$FriendRequestsTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $FriendRequestsTable,
    DriftFriendRequest,
    $$FriendRequestsTableFilterComposer,
    $$FriendRequestsTableOrderingComposer,
    $$FriendRequestsTableAnnotationComposer,
    $$FriendRequestsTableCreateCompanionBuilder,
    $$FriendRequestsTableUpdateCompanionBuilder,
    (
      DriftFriendRequest,
      BaseReferences<_$AppDatabase, $FriendRequestsTable, DriftFriendRequest>
    ),
    DriftFriendRequest,
    PrefetchHooks Function()>;
typedef $$UserProfilesTableCreateCompanionBuilder = UserProfilesCompanion
    Function({
  required String id,
  Value<String?> email,
  Value<String?> username,
  Value<String?> firstName,
  Value<String?> lastName,
  Value<String?> profilePictureUrl,
  Value<DateTime?> birthday,
  Value<String?> friendIds,
  Value<String?> bubbleId,
  Value<String?> contactIds,
  Value<String> onlineStatus,
  Value<String> bubbleStatus,
  Value<String?> blockedUserIds,
  Value<String?> pendingSentContactRequestIds,
  Value<String?> pendingReceivedContactRequestIds,
  Value<bool> hasCompletedOnboarding,
  required DateTime cachedAt,
  required DateTime lastRefreshed,
  required DateTime lastAccessed,
  Value<bool> isStale,
  Value<int> rowid,
});
typedef $$UserProfilesTableUpdateCompanionBuilder = UserProfilesCompanion
    Function({
  Value<String> id,
  Value<String?> email,
  Value<String?> username,
  Value<String?> firstName,
  Value<String?> lastName,
  Value<String?> profilePictureUrl,
  Value<DateTime?> birthday,
  Value<String?> friendIds,
  Value<String?> bubbleId,
  Value<String?> contactIds,
  Value<String> onlineStatus,
  Value<String> bubbleStatus,
  Value<String?> blockedUserIds,
  Value<String?> pendingSentContactRequestIds,
  Value<String?> pendingReceivedContactRequestIds,
  Value<bool> hasCompletedOnboarding,
  Value<DateTime> cachedAt,
  Value<DateTime> lastRefreshed,
  Value<DateTime> lastAccessed,
  Value<bool> isStale,
  Value<int> rowid,
});

class $$UserProfilesTableFilterComposer
    extends Composer<_$AppDatabase, $UserProfilesTable> {
  $$UserProfilesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get email => $composableBuilder(
      column: $table.email, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get username => $composableBuilder(
      column: $table.username, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get firstName => $composableBuilder(
      column: $table.firstName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get lastName => $composableBuilder(
      column: $table.lastName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get profilePictureUrl => $composableBuilder(
      column: $table.profilePictureUrl,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get birthday => $composableBuilder(
      column: $table.birthday, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get friendIds => $composableBuilder(
      column: $table.friendIds, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get bubbleId => $composableBuilder(
      column: $table.bubbleId, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get contactIds => $composableBuilder(
      column: $table.contactIds, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get onlineStatus => $composableBuilder(
      column: $table.onlineStatus, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get bubbleStatus => $composableBuilder(
      column: $table.bubbleStatus, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get blockedUserIds => $composableBuilder(
      column: $table.blockedUserIds,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get pendingSentContactRequestIds => $composableBuilder(
      column: $table.pendingSentContactRequestIds,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get pendingReceivedContactRequestIds =>
      $composableBuilder(
          column: $table.pendingReceivedContactRequestIds,
          builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get hasCompletedOnboarding => $composableBuilder(
      column: $table.hasCompletedOnboarding,
      builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get cachedAt => $composableBuilder(
      column: $table.cachedAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get lastRefreshed => $composableBuilder(
      column: $table.lastRefreshed, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get lastAccessed => $composableBuilder(
      column: $table.lastAccessed, builder: (column) => ColumnFilters(column));

  ColumnFilters<bool> get isStale => $composableBuilder(
      column: $table.isStale, builder: (column) => ColumnFilters(column));
}

class $$UserProfilesTableOrderingComposer
    extends Composer<_$AppDatabase, $UserProfilesTable> {
  $$UserProfilesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get email => $composableBuilder(
      column: $table.email, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get username => $composableBuilder(
      column: $table.username, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get firstName => $composableBuilder(
      column: $table.firstName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get lastName => $composableBuilder(
      column: $table.lastName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get profilePictureUrl => $composableBuilder(
      column: $table.profilePictureUrl,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get birthday => $composableBuilder(
      column: $table.birthday, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get friendIds => $composableBuilder(
      column: $table.friendIds, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get bubbleId => $composableBuilder(
      column: $table.bubbleId, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get contactIds => $composableBuilder(
      column: $table.contactIds, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get onlineStatus => $composableBuilder(
      column: $table.onlineStatus,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get bubbleStatus => $composableBuilder(
      column: $table.bubbleStatus,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get blockedUserIds => $composableBuilder(
      column: $table.blockedUserIds,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get pendingSentContactRequestIds =>
      $composableBuilder(
          column: $table.pendingSentContactRequestIds,
          builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get pendingReceivedContactRequestIds =>
      $composableBuilder(
          column: $table.pendingReceivedContactRequestIds,
          builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get hasCompletedOnboarding => $composableBuilder(
      column: $table.hasCompletedOnboarding,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get cachedAt => $composableBuilder(
      column: $table.cachedAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get lastRefreshed => $composableBuilder(
      column: $table.lastRefreshed,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get lastAccessed => $composableBuilder(
      column: $table.lastAccessed,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<bool> get isStale => $composableBuilder(
      column: $table.isStale, builder: (column) => ColumnOrderings(column));
}

class $$UserProfilesTableAnnotationComposer
    extends Composer<_$AppDatabase, $UserProfilesTable> {
  $$UserProfilesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get email =>
      $composableBuilder(column: $table.email, builder: (column) => column);

  GeneratedColumn<String> get username =>
      $composableBuilder(column: $table.username, builder: (column) => column);

  GeneratedColumn<String> get firstName =>
      $composableBuilder(column: $table.firstName, builder: (column) => column);

  GeneratedColumn<String> get lastName =>
      $composableBuilder(column: $table.lastName, builder: (column) => column);

  GeneratedColumn<String> get profilePictureUrl => $composableBuilder(
      column: $table.profilePictureUrl, builder: (column) => column);

  GeneratedColumn<DateTime> get birthday =>
      $composableBuilder(column: $table.birthday, builder: (column) => column);

  GeneratedColumn<String> get friendIds =>
      $composableBuilder(column: $table.friendIds, builder: (column) => column);

  GeneratedColumn<String> get bubbleId =>
      $composableBuilder(column: $table.bubbleId, builder: (column) => column);

  GeneratedColumn<String> get contactIds => $composableBuilder(
      column: $table.contactIds, builder: (column) => column);

  GeneratedColumn<String> get onlineStatus => $composableBuilder(
      column: $table.onlineStatus, builder: (column) => column);

  GeneratedColumn<String> get bubbleStatus => $composableBuilder(
      column: $table.bubbleStatus, builder: (column) => column);

  GeneratedColumn<String> get blockedUserIds => $composableBuilder(
      column: $table.blockedUserIds, builder: (column) => column);

  GeneratedColumn<String> get pendingSentContactRequestIds =>
      $composableBuilder(
          column: $table.pendingSentContactRequestIds,
          builder: (column) => column);

  GeneratedColumn<String> get pendingReceivedContactRequestIds =>
      $composableBuilder(
          column: $table.pendingReceivedContactRequestIds,
          builder: (column) => column);

  GeneratedColumn<bool> get hasCompletedOnboarding => $composableBuilder(
      column: $table.hasCompletedOnboarding, builder: (column) => column);

  GeneratedColumn<DateTime> get cachedAt =>
      $composableBuilder(column: $table.cachedAt, builder: (column) => column);

  GeneratedColumn<DateTime> get lastRefreshed => $composableBuilder(
      column: $table.lastRefreshed, builder: (column) => column);

  GeneratedColumn<DateTime> get lastAccessed => $composableBuilder(
      column: $table.lastAccessed, builder: (column) => column);

  GeneratedColumn<bool> get isStale =>
      $composableBuilder(column: $table.isStale, builder: (column) => column);
}

class $$UserProfilesTableTableManager extends RootTableManager<
    _$AppDatabase,
    $UserProfilesTable,
    DriftUserProfile,
    $$UserProfilesTableFilterComposer,
    $$UserProfilesTableOrderingComposer,
    $$UserProfilesTableAnnotationComposer,
    $$UserProfilesTableCreateCompanionBuilder,
    $$UserProfilesTableUpdateCompanionBuilder,
    (
      DriftUserProfile,
      BaseReferences<_$AppDatabase, $UserProfilesTable, DriftUserProfile>
    ),
    DriftUserProfile,
    PrefetchHooks Function()> {
  $$UserProfilesTableTableManager(_$AppDatabase db, $UserProfilesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$UserProfilesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$UserProfilesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$UserProfilesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String?> email = const Value.absent(),
            Value<String?> username = const Value.absent(),
            Value<String?> firstName = const Value.absent(),
            Value<String?> lastName = const Value.absent(),
            Value<String?> profilePictureUrl = const Value.absent(),
            Value<DateTime?> birthday = const Value.absent(),
            Value<String?> friendIds = const Value.absent(),
            Value<String?> bubbleId = const Value.absent(),
            Value<String?> contactIds = const Value.absent(),
            Value<String> onlineStatus = const Value.absent(),
            Value<String> bubbleStatus = const Value.absent(),
            Value<String?> blockedUserIds = const Value.absent(),
            Value<String?> pendingSentContactRequestIds = const Value.absent(),
            Value<String?> pendingReceivedContactRequestIds =
                const Value.absent(),
            Value<bool> hasCompletedOnboarding = const Value.absent(),
            Value<DateTime> cachedAt = const Value.absent(),
            Value<DateTime> lastRefreshed = const Value.absent(),
            Value<DateTime> lastAccessed = const Value.absent(),
            Value<bool> isStale = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              UserProfilesCompanion(
            id: id,
            email: email,
            username: username,
            firstName: firstName,
            lastName: lastName,
            profilePictureUrl: profilePictureUrl,
            birthday: birthday,
            friendIds: friendIds,
            bubbleId: bubbleId,
            contactIds: contactIds,
            onlineStatus: onlineStatus,
            bubbleStatus: bubbleStatus,
            blockedUserIds: blockedUserIds,
            pendingSentContactRequestIds: pendingSentContactRequestIds,
            pendingReceivedContactRequestIds: pendingReceivedContactRequestIds,
            hasCompletedOnboarding: hasCompletedOnboarding,
            cachedAt: cachedAt,
            lastRefreshed: lastRefreshed,
            lastAccessed: lastAccessed,
            isStale: isStale,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            Value<String?> email = const Value.absent(),
            Value<String?> username = const Value.absent(),
            Value<String?> firstName = const Value.absent(),
            Value<String?> lastName = const Value.absent(),
            Value<String?> profilePictureUrl = const Value.absent(),
            Value<DateTime?> birthday = const Value.absent(),
            Value<String?> friendIds = const Value.absent(),
            Value<String?> bubbleId = const Value.absent(),
            Value<String?> contactIds = const Value.absent(),
            Value<String> onlineStatus = const Value.absent(),
            Value<String> bubbleStatus = const Value.absent(),
            Value<String?> blockedUserIds = const Value.absent(),
            Value<String?> pendingSentContactRequestIds = const Value.absent(),
            Value<String?> pendingReceivedContactRequestIds =
                const Value.absent(),
            Value<bool> hasCompletedOnboarding = const Value.absent(),
            required DateTime cachedAt,
            required DateTime lastRefreshed,
            required DateTime lastAccessed,
            Value<bool> isStale = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              UserProfilesCompanion.insert(
            id: id,
            email: email,
            username: username,
            firstName: firstName,
            lastName: lastName,
            profilePictureUrl: profilePictureUrl,
            birthday: birthday,
            friendIds: friendIds,
            bubbleId: bubbleId,
            contactIds: contactIds,
            onlineStatus: onlineStatus,
            bubbleStatus: bubbleStatus,
            blockedUserIds: blockedUserIds,
            pendingSentContactRequestIds: pendingSentContactRequestIds,
            pendingReceivedContactRequestIds: pendingReceivedContactRequestIds,
            hasCompletedOnboarding: hasCompletedOnboarding,
            cachedAt: cachedAt,
            lastRefreshed: lastRefreshed,
            lastAccessed: lastAccessed,
            isStale: isStale,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$UserProfilesTableProcessedTableManager = ProcessedTableManager<
    _$AppDatabase,
    $UserProfilesTable,
    DriftUserProfile,
    $$UserProfilesTableFilterComposer,
    $$UserProfilesTableOrderingComposer,
    $$UserProfilesTableAnnotationComposer,
    $$UserProfilesTableCreateCompanionBuilder,
    $$UserProfilesTableUpdateCompanionBuilder,
    (
      DriftUserProfile,
      BaseReferences<_$AppDatabase, $UserProfilesTable, DriftUserProfile>
    ),
    DriftUserProfile,
    PrefetchHooks Function()>;

class $AppDatabaseManager {
  final _$AppDatabase _db;
  $AppDatabaseManager(this._db);
  $$ChatMessagesTableTableManager get chatMessages =>
      $$ChatMessagesTableTableManager(_db, _db.chatMessages);
  $$UserSettingsTableTableManager get userSettings =>
      $$UserSettingsTableTableManager(_db, _db.userSettings);
  $$ChatMetadatasTableTableManager get chatMetadatas =>
      $$ChatMetadatasTableTableManager(_db, _db.chatMetadatas);
  $$UnreadCountsTableTableManager get unreadCounts =>
      $$UnreadCountsTableTableManager(_db, _db.unreadCounts);
  $$DraftMessagesTableTableManager get draftMessages =>
      $$DraftMessagesTableTableManager(_db, _db.draftMessages);
  $$FcmMessagesTableTableManager get fcmMessages =>
      $$FcmMessagesTableTableManager(_db, _db.fcmMessages);
  $$ProfilePicturesTableTableManager get profilePictures =>
      $$ProfilePicturesTableTableManager(_db, _db.profilePictures);
  $$ContactRequestsTableTableManager get contactRequests =>
      $$ContactRequestsTableTableManager(_db, _db.contactRequests);
  $$FriendRequestsTableTableManager get friendRequests =>
      $$FriendRequestsTableTableManager(_db, _db.friendRequests);
  $$UserProfilesTableTableManager get userProfiles =>
      $$UserProfilesTableTableManager(_db, _db.userProfiles);
}

mixin _$ChatMessageDaoMixin on DatabaseAccessor<AppDatabase> {
  $ChatMessagesTable get chatMessages => attachedDatabase.chatMessages;
}
mixin _$UserSettingDaoMixin on DatabaseAccessor<AppDatabase> {
  $UserSettingsTable get userSettings => attachedDatabase.userSettings;
}
mixin _$ChatMetadataDaoMixin on DatabaseAccessor<AppDatabase> {
  $ChatMetadatasTable get chatMetadatas => attachedDatabase.chatMetadatas;
}
mixin _$UnreadCountDaoMixin on DatabaseAccessor<AppDatabase> {
  $UnreadCountsTable get unreadCounts => attachedDatabase.unreadCounts;
}
mixin _$DraftMessageDaoMixin on DatabaseAccessor<AppDatabase> {
  $DraftMessagesTable get draftMessages => attachedDatabase.draftMessages;
}
mixin _$FcmMessageDaoMixin on DatabaseAccessor<AppDatabase> {
  $FcmMessagesTable get fcmMessages => attachedDatabase.fcmMessages;
}
mixin _$ProfilePictureDaoMixin on DatabaseAccessor<AppDatabase> {
  $ProfilePicturesTable get profilePictures => attachedDatabase.profilePictures;
}
mixin _$ContactRequestDaoMixin on DatabaseAccessor<AppDatabase> {
  $ContactRequestsTable get contactRequests => attachedDatabase.contactRequests;
}
mixin _$FriendRequestDaoMixin on DatabaseAccessor<AppDatabase> {
  $FriendRequestsTable get friendRequests => attachedDatabase.friendRequests;
}
mixin _$UserProfileDaoMixin on DatabaseAccessor<AppDatabase> {
  $UserProfilesTable get userProfiles => attachedDatabase.userProfiles;
}
