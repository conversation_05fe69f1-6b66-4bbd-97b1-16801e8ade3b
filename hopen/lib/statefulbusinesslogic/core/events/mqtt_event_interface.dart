/// MQTT Event Interface for Real-Time Events
///
/// This interface defines the contract for handling MQTT events in the business logic layer.
/// Following the four-layer dependency rule:
/// - Business Logic Layer defines this interface
/// - Provider Layer implements this interface
/// - Presentation Layer uses this interface through dependency injection

abstract class MqttEventHandler {
  /// Handle contact request events
  Future<void> handleContactRequestEvent(ContactRequestEvent event);

  /// Handle friend request events
  Future<void> handleFriendRequestEvent(FriendRequestEvent event);

  /// Handle bubble events
  Future<void> handleBubbleEvent(BubbleEvent event);

  /// Handle system notification events
  Future<void> handleSystemEvent(SystemEvent event);

  /// Handle raw MQTT message and route to appropriate event handler
  Future<void> handleMqttMessage(String topic, String payload);
}

/// Base class for all MQTT events
abstract class MqttEvent {
  const MqttEvent({
    required this.userId,
    required this.timestamp,
    required this.eventId,
  });

  final String userId;
  final DateTime timestamp;
  final String eventId;

  /// Convert event to JSON for MQTT payload
  Map<String, dynamic> toJson();

  /// Create event from MQTT payload
  static MqttEvent fromJson(Map<String, dynamic> json, String eventType) {
    switch (eventType) {
      case 'contact_request':
        return ContactRequestEvent.fromJson(json);
      case 'friend_request':
        return FriendRequestEvent.fromJson(json);
      case 'bubble':
        return BubbleEvent.fromJson(json);
      case 'system':
        return SystemEvent.fromJson(json);
      default:
        throw ArgumentError('Unknown event type: $eventType');
    }
  }
}

/// Contact request events (new, accepted, declined, expired)
class ContactRequestEvent extends MqttEvent {
  const ContactRequestEvent({
    required super.userId,
    required super.timestamp,
    required super.eventId,
    required this.requestId,
    required this.action,
    required this.senderId,
    required this.receiverId,
    this.senderName,
    this.message,
  });

  final String requestId;
  final ContactRequestAction action;
  final String senderId;
  final String receiverId;
  final String? senderName;
  final String? message;

  factory ContactRequestEvent.fromJson(Map<String, dynamic> json) {
    return ContactRequestEvent(
      userId: json['userId'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      eventId: json['eventId'] as String,
      requestId: json['requestId'] as String,
      action: ContactRequestAction.values.firstWhere(
        (e) => e.name == json['action'],
      ),
      senderId: json['senderId'] as String,
      receiverId: json['receiverId'] as String,
      senderName: json['senderName'] as String?,
      message: json['message'] as String?,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'timestamp': timestamp.toIso8601String(),
      'eventId': eventId,
      'requestId': requestId,
      'action': action.name,
      'senderId': senderId,
      'receiverId': receiverId,
      'senderName': senderName,
      'message': message,
    };
  }
}

enum ContactRequestAction { received, accepted, declined, expired, cancelled }

/// Friend request events (new, accepted, declined)
class FriendRequestEvent extends MqttEvent {
  const FriendRequestEvent({
    required super.userId,
    required super.timestamp,
    required super.eventId,
    required this.requestId,
    required this.action,
    required this.senderId,
    required this.receiverId,
    this.bubbleId,
    this.senderName,
  });

  final String requestId;
  final FriendRequestAction action;
  final String senderId;
  final String receiverId;
  final String? bubbleId; // For auto-generated friend requests from bubbles
  final String? senderName;

  factory FriendRequestEvent.fromJson(Map<String, dynamic> json) {
    return FriendRequestEvent(
      userId: json['userId'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      eventId: json['eventId'] as String,
      requestId: json['requestId'] as String,
      action: FriendRequestAction.values.firstWhere(
        (e) => e.name == json['action'],
      ),
      senderId: json['senderId'] as String,
      receiverId: json['receiverId'] as String,
      bubbleId: json['bubbleId'] as String?,
      senderName: json['senderName'] as String?,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'timestamp': timestamp.toIso8601String(),
      'eventId': eventId,
      'requestId': requestId,
      'action': action.name,
      'senderId': senderId,
      'receiverId': receiverId,
      'bubbleId': bubbleId,
      'senderName': senderName,
    };
  }
}

enum FriendRequestAction {
  received,
  accepted,
  declined,
  auto_generated, // From bubble expiry
}

/// Bubble events (created, joined, left, expired)
class BubbleEvent extends MqttEvent {
  const BubbleEvent({
    required super.userId,
    required super.timestamp,
    required super.eventId,
    required this.bubbleId,
    required this.action,
    this.participantId,
    this.bubbleTitle,
  });

  final String bubbleId;
  final BubbleAction action;
  final String? participantId;
  final String? bubbleTitle;

  factory BubbleEvent.fromJson(Map<String, dynamic> json) {
    return BubbleEvent(
      userId: json['userId'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      eventId: json['eventId'] as String,
      bubbleId: json['bubbleId'] as String,
      action: BubbleAction.values.firstWhere((e) => e.name == json['action']),
      participantId: json['participantId'] as String?,
      bubbleTitle: json['bubbleTitle'] as String?,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'timestamp': timestamp.toIso8601String(),
      'eventId': eventId,
      'bubbleId': bubbleId,
      'action': action.name,
      'participantId': participantId,
      'bubbleTitle': bubbleTitle,
    };
  }
}

enum BubbleAction { created, joined, left, expired, invite_received }

/// System events (notifications, updates)
class SystemEvent extends MqttEvent {
  const SystemEvent({
    required super.userId,
    required super.timestamp,
    required super.eventId,
    required this.type,
    required this.title,
    required this.message,
    this.data,
  });

  final String type;
  final String title;
  final String message;
  final Map<String, dynamic>? data;

  factory SystemEvent.fromJson(Map<String, dynamic> json) {
    return SystemEvent(
      userId: json['userId'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      eventId: json['eventId'] as String,
      type: json['type'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      data: json['data'] as Map<String, dynamic>?,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'timestamp': timestamp.toIso8601String(),
      'eventId': eventId,
      'type': type,
      'title': title,
      'message': message,
      'data': data,
    };
  }
}

/// MQTT Topic Structure
class MqttTopics {
  static const String _basePrefix = 'hopen';

  /// Contact request topics
  static String contactRequests(String userId) =>
      '$_basePrefix/contact/requests/$userId';

  /// Friend request topics
  static String friendRequests(String userId) =>
      '$_basePrefix/friendship/requests/$userId';

  /// Bubble topics
  static String bubbleEvents(String userId) => '$_basePrefix/bubbles/$userId';

  /// System notification topics
  static String systemEvents(String userId) => '$_basePrefix/system/$userId';

  /// Call topics (existing)
  static String callEvents(String userId) => '$_basePrefix/calls/$userId';

  /// Chat topics (existing)
  static String chatEvents(String userId) => '$_basePrefix/chat/$userId';

  /// Get all topics for a user
  static List<String> getAllTopics(String userId) => [
    contactRequests(userId),
    friendRequests(userId),
    bubbleEvents(userId),
    systemEvents(userId),
    callEvents(userId),
    chatEvents(userId),
  ];
}
