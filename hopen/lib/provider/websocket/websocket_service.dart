import 'dart:async';
import 'dart:convert';

import '../../statefulbusinesslogic/core/services/logging_service.dart';
import '../services/mqtt/mqtt_service.dart';

class WebSocketService {

  WebSocketService(this._mqttService);
  final MqttService _mqttService;
  final Map<String, StreamController<Map<String, dynamic>>> _channelControllers = {};
  final Set<String> _subscribedChannels = {};
  StreamSubscription<Map<String, dynamic>>? _mqttSubscription;
  bool _isInitialized = false;

  Future<void> initialize() async {
    if (_isInitialized) return;

    // Listen to MQTT message stream and route to appropriate channels
    _mqttSubscription = _mqttService.messageStream.listen(_handleMqttMessage);
    _isInitialized = true;
    LoggingService.info('WebSocket service initialized');
  }

  void _handleMqttMessage(Map<String, dynamic> message) {
    try {
      final metadata = message['mqtt_metadata'] as Map<String, dynamic>?;
      if (metadata == null) return;

      final topic = metadata['topic'] as String;
      
      // Route message to appropriate channel based on topic
      final channel = _getChannelFromTopic(topic);
      if (channel != null && _channelControllers.containsKey(channel)) {
        _channelControllers[channel]!.add(message);
      }

      // Also route to topic-specific controllers
      if (_channelControllers.containsKey(topic)) {
        _channelControllers[topic]!.add(message);
      }
    } catch (e) {
      LoggingService.error('Error handling MQTT message in WebSocket service: $e');
    }
  }

  String? _getChannelFromTopic(String topic) {
    // Map MQTT topics to WebSocket channels
    if (topic.startsWith('calls/')) {
      return 'calls';
    } else if (topic.startsWith('chats/')) {
      return 'chats';
    } else if (topic.startsWith('bubbles/')) {
      return 'bubbles';
    } else if (topic.startsWith('hopen/requests/')) {
      return 'requests';
    } else if (topic.startsWith('users/')) {
      return 'users';
    }
    return null;
  }

  Future<void> connect(String url) async {
    // Since we're using MQTT as the underlying transport,
    // we don't need to establish a separate WebSocket connection
    LoggingService.info('WebSocket service connected (via MQTT)');
  }

  Stream<Map<String, dynamic>> subscribe(String channel) {
    if (!_channelControllers.containsKey(channel)) {
      _channelControllers[channel] = StreamController<Map<String, dynamic>>.broadcast();
    }

    // Subscribe to relevant MQTT topics based on channel
    _subscribeToMqttTopics(channel);
    
    return _channelControllers[channel]!.stream;
  }

  void _subscribeToMqttTopics(String channel) {
    if (_subscribedChannels.contains(channel)) return;

    switch (channel) {
      case 'calls':
        _mqttService.subscribe('calls/+/+'); // Subscribe to all call events
        break;
      case 'chats':
        _mqttService.subscribe('chats/+/+'); // Subscribe to all chat events
        break;
      case 'bubbles':
        _mqttService.subscribe('bubbles/+/+'); // Subscribe to all bubble events
        break;
      case 'requests':
        _mqttService.subscribe('hopen/requests/+'); // Subscribe to all requests
        break;
      case 'users':
        _mqttService.subscribe('users/+/+'); // Subscribe to all user events
        break;
    }

    _subscribedChannels.add(channel);
    LoggingService.info('Subscribed to WebSocket channel: $channel');
  }

  void unsubscribe(String channel) {
    if (_channelControllers.containsKey(channel)) {
      _channelControllers[channel]!.close();
      _channelControllers.remove(channel);
    }

    // Unsubscribe from MQTT topics
    _unsubscribeFromMqttTopics(channel);
    _subscribedChannels.remove(channel);
    LoggingService.info('Unsubscribed from WebSocket channel: $channel');
  }

  void _unsubscribeFromMqttTopics(String channel) {
    switch (channel) {
      case 'calls':
        _mqttService.unsubscribe('calls/+/+');
        break;
      case 'chats':
        _mqttService.unsubscribe('chats/+/+');
        break;
      case 'bubbles':
        _mqttService.unsubscribe('bubbles/+/+');
        break;
      case 'requests':
        _mqttService.unsubscribe('hopen/requests/+');
        break;
      case 'users':
        _mqttService.unsubscribe('users/+/+');
        break;
    }
  }

  void send(String channel, Map<String, dynamic> data) {
    // Convert WebSocket send to MQTT publish
    final topic = _getTopicFromChannel(channel, data);
    if (topic != null) {
      final payload = jsonEncode(data);
      _mqttService.publish(topic, payload);
    }
  }

  String? _getTopicFromChannel(String channel, Map<String, dynamic> data) {
    switch (channel) {
      case 'calls':
        final callId = data['call_id'] as String?;
        final eventType = data['event_type'] as String? ?? 'event';
        if (callId != null) {
          return 'calls/$callId/$eventType';
        }
        break;
      case 'chats':
        final chatId = data['chat_id'] as String?;
        if (chatId != null) {
          return 'chats/$chatId/messages';
        }
        break;
      case 'bubbles':
        final bubbleId = data['bubble_id'] as String?;
        if (bubbleId != null) {
          return 'bubbles/$bubbleId/events';
        }
        break;
      case 'requests':
        final userId = data['user_id'] as String?;
        if (userId != null) {
          return 'hopen/requests/$userId';
        }
        break;
      case 'users':
        final userId = data['user_id'] as String?;
        final eventType = data['event_type'] as String? ?? 'status';
        if (userId != null) {
          return 'users/$userId/$eventType';
        }
        break;
    }
    return null;
  }

  Future<void> disconnect() async {
    // Close all channel controllers
    for (final controller in _channelControllers.values) {
      controller.close();
    }
    _channelControllers.clear();
    _subscribedChannels.clear();

    // Cancel MQTT subscription
    await _mqttSubscription?.cancel();
    _mqttSubscription = null;

    _isInitialized = false;
    LoggingService.info('WebSocket service disconnected');
  }

  bool get isConnected => _mqttService.isConnected;

  // Convenience methods for specific use cases
  Stream<Map<String, dynamic>> subscribeToCall(String callId) {
    final topic = 'calls/$callId/events';
    if (!_channelControllers.containsKey(topic)) {
      _channelControllers[topic] = StreamController<Map<String, dynamic>>.broadcast();
      _mqttService.subscribe(topic);
    }
    return _channelControllers[topic]!.stream;
  }

  Stream<Map<String, dynamic>> subscribeToChat(String chatId) {
    final topic = 'chats/$chatId/messages';
    if (!_channelControllers.containsKey(topic)) {
      _channelControllers[topic] = StreamController<Map<String, dynamic>>.broadcast();
      _mqttService.subscribe(topic);
    }
    return _channelControllers[topic]!.stream;
  }

  Stream<Map<String, dynamic>> subscribeToBubble(String bubbleId) {
    final topic = 'bubbles/$bubbleId/events';
    if (!_channelControllers.containsKey(topic)) {
      _channelControllers[topic] = StreamController<Map<String, dynamic>>.broadcast();
      _mqttService.subscribe(topic);
    }
    return _channelControllers[topic]!.stream;
  }

  Stream<Map<String, dynamic>> subscribeToUser(String userId) {
    final topic = 'users/$userId/events';
    if (!_channelControllers.containsKey(topic)) {
      _channelControllers[topic] = StreamController<Map<String, dynamic>>.broadcast();
      _mqttService.subscribe(topic);
    }
    return _channelControllers[topic]!.stream;
  }

  void sendToCall(String callId, String eventType, Map<String, dynamic> data) {
    final topic = 'calls/$callId/$eventType';
    final payload = jsonEncode(data);
    _mqttService.publish(topic, payload);
  }

  void sendToChat(String chatId, Map<String, dynamic> message) {
    final topic = 'chats/$chatId/messages';
    final payload = jsonEncode(message);
    _mqttService.publish(topic, payload);
  }

  void sendToBubble(String bubbleId, Map<String, dynamic> event) {
    final topic = 'bubbles/$bubbleId/events';
    final payload = jsonEncode(event);
    _mqttService.publish(topic, payload);
  }

  void sendToUser(String userId, String eventType, Map<String, dynamic> data) {
    final topic = 'users/$userId/$eventType';
    final payload = jsonEncode(data);
    _mqttService.publish(topic, payload);
  }
} 