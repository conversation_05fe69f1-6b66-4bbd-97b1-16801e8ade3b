import 'dart:typed_data';

import 'package:image_picker/image_picker.dart';

import '../../repositories/image_processing/image_processing_repository.dart';
import '../../statefulbusinesslogic/core/models/image_processing_result.dart';
import '../repositories/image_processing/image_processing_repository_impl.dart';

/// Comprehensive image processing service following industry best practices
/// This service now delegates to the ImageProcessingRepository for better architecture
/// and eliminates code duplication across the codebase.
class ImageProcessingService {
  const ImageProcessingService._();

  static final ImageProcessingRepository _repository =
      ImageProcessingRepositoryImpl();

  /// Pick image from gallery with comprehensive validation
  static Future<XFile?> pickFromGallery() => _repository.pickFromGallery();

  /// Take photo with camera with comprehensive validation
  static Future<XFile?> takePhoto() => _repository.takePhoto();

  /// Process image from file with compression and format conversion
  static Future<Uint8List> processImageFromFile(XFile imageFile) =>
      _repository.processImageFromFile(imageFile);

  /// Validate an image file before processing
  static Future<void> validateImageFile(XFile imageFile) =>
      _repository.validateImageFile(imageFile);

  /// Get image information without processing
  static Future<ImageInfo> getImageInfo(String imagePath) =>
      _repository.getImageInfo(imagePath);

  /// Process image in isolate for heavy operations (legacy method for backward compatibility)
  static Future<ImageProcessingResult> processImageInIsolate({
    required String imagePath,
    required int minResolution,
    required int maxResolution,
    required int maxFileSizeBytes,
    required double compressionQuality,
  }) => _repository.processImageInIsolate(
    imagePath: imagePath,
    minResolution: minResolution,
    maxResolution: maxResolution,
    maxFileSizeBytes: maxFileSizeBytes,
    compressionQuality: compressionQuality,
  );
}
