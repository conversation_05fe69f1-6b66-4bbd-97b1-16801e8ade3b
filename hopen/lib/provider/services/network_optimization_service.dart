import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/services.dart';
import '../../config/app_config.dart';

/// Network optimization service with caching, batching, and compression
/// Optimizes API calls, implements request batching, and manages response caching
class NetworkOptimizationService {
  factory NetworkOptimizationService() => _instance;
  NetworkOptimizationService._internal();
  static final NetworkOptimizationService _instance = NetworkOptimizationService._internal();

  // Request caching
  final Map<String, CachedResponse> _responseCache = {};
  final Map<String, List<Completer<http.Response>>> _pendingRequests = {};
  
  // Request batching
  final List<BatchedRequest> _batchQueue = [];
  Timer? _batchTimer;
  
  // Connection pooling
  late http.Client _httpClient;
  bool _initialized = false;

  // Enhanced cache monitoring
  final Map<String, int> _cacheHits = {};
  final Map<String, int> _cacheMisses = {};
  final List<Duration> _responseTimes = [];
  final int _maxResponseTimeHistory = 100;

  // Memory pressure handling
  bool _isMemoryPressureActive = false;

  /// Initialize the network service
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Create HTTP client with connection pooling
      _httpClient = http.Client();
      
      // Start periodic cache cleanup
      _startCacheCleanup();
      
      // Start memory pressure monitoring
      _startMemoryPressureMonitoring();
      
      AppConfig.logInfo('Network optimization service initialized');
      _initialized = true;
    } catch (error, stackTrace) {
      AppConfig.logError('Failed to initialize network service', error, stackTrace);
    }
  }

  /// Make an optimized HTTP GET request with caching
  Future<http.Response> get(
    String url, {
    Map<String, String>? headers,
    Duration? cacheDuration,
    bool forceRefresh = false,
  }) async {
    final startTime = DateTime.now();
    final cacheKey = _generateCacheKey('GET', url, headers);
    
    // Check cache first (unless force refresh)
    if (!forceRefresh) {
      final cached = _getCachedResponse(cacheKey);
      if (cached != null) {
        _recordCacheHit(cacheKey);
        _recordResponseTime(DateTime.now().difference(startTime));
        AppConfig.logInfo('Cache hit for: $url');
        return cached;
      }
    }

    _recordCacheMiss(cacheKey);

    // Check for pending identical requests
    if (_pendingRequests.containsKey(cacheKey)) {
      AppConfig.logInfo('Deduplicating request for: $url');
      final completer = Completer<http.Response>();
      _pendingRequests[cacheKey]!.add(completer);
      return completer.future;
    }

    // Make the request
    _pendingRequests[cacheKey] = [];
    
    try {
      final response = await _makeRequest('GET', url, headers: headers);
      
      // Cache successful responses
      if (response.statusCode >= 200 && response.statusCode < 300) {
        _cacheResponse(cacheKey, response, cacheDuration ?? AppConfig.cacheExpiration);
      }

      // Complete any pending identical requests
      final pending = _pendingRequests.remove(cacheKey) ?? [];
      for (final completer in pending) {
        completer.complete(response);
      }

      _recordResponseTime(DateTime.now().difference(startTime));
      return response;
    } catch (error) {
      // Complete pending requests with error
      final pending = _pendingRequests.remove(cacheKey) ?? [];
      for (final completer in pending) {
        completer.completeError(error);
      }
      rethrow;
    }
  }

  /// Make an optimized HTTP POST request with batching support
  Future<http.Response> post(
    String url, {
    Map<String, String>? headers,
    body,
    bool enableBatching = false,
  }) async {
    if (enableBatching) {
      return _addToBatch('POST', url, headers: headers, body: body);
    }

    return _makeRequest('POST', url, headers: headers, body: body);
  }

  /// Make an optimized HTTP PUT request
  Future<http.Response> put(
    String url, {
    Map<String, String>? headers,
    body,
  }) async {
    return _makeRequest('PUT', url, headers: headers, body: body);
  }

  /// Make an optimized HTTP DELETE request
  Future<http.Response> delete(
    String url, {
    Map<String, String>? headers,
  }) async {
    return _makeRequest('DELETE', url, headers: headers);
  }

  /// Make a basic HTTP request
  Future<http.Response> _makeRequest(
    String method,
    String url, {
    Map<String, String>? headers,
    body,
  }) async {
    try {
      switch (method) {
        case 'GET':
          return await _httpClient.get(Uri.parse(url), headers: headers);
        case 'POST':
          return await _httpClient.post(Uri.parse(url), headers: headers, body: body);
        case 'PUT':
          return await _httpClient.put(Uri.parse(url), headers: headers, body: body);
        case 'DELETE':
          return await _httpClient.delete(Uri.parse(url), headers: headers);
        default:
          throw ArgumentError('Unsupported HTTP method: $method');
      }
    } catch (error) {
      AppConfig.logError('HTTP request failed: $method $url', error);
      rethrow;
    }
  }

  /// Add request to batch queue
  Future<http.Response> _addToBatch(
    String method,
    String url, {
    Map<String, String>? headers,
    body,
  }) async {
    final completer = Completer<http.Response>();
    
    _batchQueue.add(BatchedRequest(
      method: method,
      url: url,
      headers: headers,
      body: body,
      completer: completer,
    ));

    // Start batch timer if not already running
    _batchTimer ??= Timer(const Duration(milliseconds: 100), _processBatch);

    return completer.future;
  }

  /// Process batched requests
  Future<void> _processBatch() async {
    _batchTimer?.cancel();
    _batchTimer = null;

    if (_batchQueue.isEmpty) return;

    final batch = List<BatchedRequest>.from(_batchQueue);
    _batchQueue.clear();

    // Process batch in parallel
    final futures = batch.map((request) async {
      try {
        final response = await _makeRequest(
          request.method,
          request.url,
          headers: request.headers,
          body: request.body,
        );
        request.completer.complete(response);
      } catch (error) {
        request.completer.completeError(error);
      }
    });

    await Future.wait(futures);
  }

  /// Generate cache key for request
  String _generateCacheKey(String method, String url, Map<String, String>? headers) {
    final headerString = headers?.entries
        .map((e) => '${e.key}:${e.value}')
        .join('|') ?? '';
    return '$method:$url:$headerString';
  }

  /// Get cached response if available and not expired
  http.Response? _getCachedResponse(String cacheKey) {
    final cached = _responseCache[cacheKey];
    if (cached != null && !cached.isExpired) {
      return cached.response;
    }
    
    // Remove expired cache entry
    if (cached != null) {
      _responseCache.remove(cacheKey);
    }
    
    return null;
  }

  /// Cache response
  void _cacheResponse(String cacheKey, http.Response response, Duration duration) {
    // Don't cache error responses or very large responses
    if (response.statusCode >= 400 || response.bodyBytes.length > 1024 * 1024) {
      return;
    }

    _responseCache[cacheKey] = CachedResponse(
      response: response,
      expiresAt: DateTime.now().add(duration),
    );

    // Limit cache size
    if (_responseCache.length > 1000) {
      _cleanupCache();
    }
  }

  /// Start periodic cache cleanup
  void _startCacheCleanup() {
    Timer.periodic(const Duration(minutes: 10), (_) => _cleanupCache());
  }

  /// Start memory pressure monitoring
  void _startMemoryPressureMonitoring() {
    // Monitor memory pressure on platforms that support it
    if (Platform.isAndroid || Platform.isIOS) {
      SystemChrome.setSystemUIChangeCallback((systemOverlaysAreVisible) async {
        // This is a simple proxy for memory pressure
        // In a real implementation, you'd use platform-specific memory monitoring
        _checkMemoryPressure();
      });
    }
  }

  /// Check for memory pressure and handle it
  void _checkMemoryPressure() {
    if (_isMemoryPressureActive) return;
    
    // Simple memory pressure detection based on cache size
    if (_responseCache.length > 800) {
      _handleMemoryPressure();
    }
  }

  /// Handle memory pressure by clearing caches
  void _handleMemoryPressure() {
    if (_isMemoryPressureActive) return;
    
    _isMemoryPressureActive = true;
    AppConfig.logInfo('Handling memory pressure - clearing caches');
    
    try {
      // Clear response cache
      _responseCache.clear();
      
      // Clear pending requests
      _pendingRequests.clear();
      
      // Clear batch queue
      _batchQueue.clear();
      _batchTimer?.cancel();
      _batchTimer = null;
      
      // Clear cache statistics
      _cacheHits.clear();
      _cacheMisses.clear();
      _responseTimes.clear();
      
      AppConfig.logInfo('Memory pressure handled - caches cleared');
    } finally {
      _isMemoryPressureActive = false;
    }
  }

  /// Clean up expired cache entries
  void _cleanupCache() {
    final now = DateTime.now();
    _responseCache.removeWhere((key, cached) => cached.expiresAt.isBefore(now));
    AppConfig.logInfo('Cache cleanup completed. ${_responseCache.length} entries remaining');
  }

  /// Record cache hit
  void _recordCacheHit(String cacheKey) {
    _cacheHits[cacheKey] = (_cacheHits[cacheKey] ?? 0) + 1;
  }

  /// Record cache miss
  void _recordCacheMiss(String cacheKey) {
    _cacheMisses[cacheKey] = (_cacheMisses[cacheKey] ?? 0) + 1;
  }

  /// Record response time
  void _recordResponseTime(Duration responseTime) {
    _responseTimes.add(responseTime);
    if (_responseTimes.length > _maxResponseTimeHistory) {
      _responseTimes.removeAt(0);
    }
  }

  /// Calculate cache hit rate
  double _calculateHitRate() {
    final totalHits = _cacheHits.values.fold(0, (sum, hits) => sum + hits);
    final totalMisses = _cacheMisses.values.fold(0, (sum, misses) => sum + misses);
    final total = totalHits + totalMisses;
    
    return total > 0 ? totalHits / total : 0.0;
  }

  /// Calculate average response time
  Duration _calculateAverageResponseTime() {
    if (_responseTimes.isEmpty) return Duration.zero;
    
    final totalMicroseconds = _responseTimes
        .map((duration) => duration.inMicroseconds)
        .reduce((sum, microseconds) => sum + microseconds);
    
    return Duration(microseconds: totalMicroseconds ~/ _responseTimes.length);
  }

  /// Calculate memory usage (estimated)
  int _calculateMemoryUsage() {
    int totalBytes = 0;
    
    // Estimate response cache memory usage
    for (final cached in _responseCache.values) {
      totalBytes += cached.response.bodyBytes.length;
      totalBytes += cached.response.headers.toString().length;
    }
    
    // Estimate pending requests memory usage
    totalBytes += _pendingRequests.length * 100; // Rough estimate
    
    return totalBytes;
  }

  /// Calculate disk usage (estimated)
  Future<int> _calculateDiskUsage() async {
    // This would require platform-specific implementation
    // For now, return estimated usage based on cache size
    return _responseCache.length * 1024; // 1KB per entry estimate
  }

  /// Clear all cached responses
  void clearCache() {
    _responseCache.clear();
    _cacheHits.clear();
    _cacheMisses.clear();
    _responseTimes.clear();
    AppConfig.logInfo('Response cache cleared');
  }

  /// Enhanced cache statistics
  Map<String, dynamic> getCacheStats() {
    final now = DateTime.now();
    final validEntries = _responseCache.values.where((cached) => !cached.expiresAt.isBefore(now)).length;
    final expiredEntries = _responseCache.length - validEntries;

    return {
      'totalEntries': _responseCache.length,
      'validEntries': validEntries,
      'expiredEntries': expiredEntries,
      'cacheHitRate': _calculateHitRate(),
      'memoryUsage': _calculateMemoryUsage(),
      'averageResponseTime': _calculateAverageResponseTime().inMilliseconds,
      'totalCacheHits': _cacheHits.values.fold(0, (sum, hits) => sum + hits),
      'totalCacheMisses': _cacheMisses.values.fold(0, (sum, misses) => sum + misses),
      'isMemoryPressureActive': _isMemoryPressureActive,
    };
  }

  /// Get network statistics
  Map<String, dynamic> getNetworkStats() => {
      'pendingRequests': _pendingRequests.length,
      'batchQueueSize': _batchQueue.length,
      'cacheSize': _responseCache.length,
      'responseTimeHistory': _responseTimes.length,
    };

  /// Cache invalidation methods
  Future<void> invalidateByPattern(String pattern) async {
    final keysToRemove = _responseCache.keys.where((key) => key.contains(pattern)).toList();
    for (final key in keysToRemove) {
      _responseCache.remove(key);
      _cacheHits.remove(key);
      _cacheMisses.remove(key);
    }
    AppConfig.logInfo('Invalidated ${keysToRemove.length} cache entries matching pattern: $pattern');
  }

  Future<void> invalidateByUrl(String url) async {
    final keysToRemove = _responseCache.keys.where((key) => key.contains(url)).toList();
    for (final key in keysToRemove) {
      _responseCache.remove(key);
      _cacheHits.remove(key);
      _cacheMisses.remove(key);
    }
    AppConfig.logInfo('Invalidated ${keysToRemove.length} cache entries for URL: $url');
  }

  Future<void> invalidateAll() async {
    clearCache();
    AppConfig.logInfo('All cache entries invalidated');
  }

  /// Dispose of the service
  void dispose() {
    _batchTimer?.cancel();
    _httpClient.close();
    _responseCache.clear();
    _pendingRequests.clear();
    _batchQueue.clear();
    _cacheHits.clear();
    _cacheMisses.clear();
    _responseTimes.clear();
    _initialized = false;
  }
}

/// Cached response data structure
class CachedResponse {

  CachedResponse({
    required this.response,
    required this.expiresAt,
  });
  final http.Response response;
  final DateTime expiresAt;

  bool get isExpired => DateTime.now().isAfter(expiresAt);
}

/// Batched request data structure
class BatchedRequest {

  BatchedRequest({
    required this.method,
    required this.url,
    required this.completer, this.headers,
    this.body,
  });
  final String method;
  final String url;
  final Map<String, String>? headers;
  final dynamic body;
  final Completer<http.Response> completer;
} 