import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../../config/app_config.dart';

/// Cache monitoring service for comprehensive cache analytics
/// Provides detailed monitoring, metrics, and performance insights
class CacheMonitoringService {
  factory CacheMonitoringService() => _instance;
  CacheMonitoringService._internal();
  static final CacheMonitoringService _instance = CacheMonitoringService._internal();

  // Monitoring data
  final Map<String, CacheMetrics> _cacheMetrics = {};
  final List<CacheEvent> _cacheEvents = [];
  final Map<String, Timer> _monitoringTimers = {};
  
  // Performance tracking
  final List<Duration> _responseTimes = [];
  final Map<String, int> _errorCounts = {};
  final Map<String, DateTime> _lastAccess = {};
  
  // Configuration
  static const int maxEventHistory = 1000;
  static const int maxResponseTimeHistory = 100;
  
  bool _initialized = false;

  /// Initialize the cache monitoring service
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Start periodic monitoring
      _startPeriodicMonitoring();
      
      // Start performance tracking
      _startPerformanceTracking();
      
      AppConfig.logInfo('Cache monitoring service initialized');
      _initialized = true;
    } catch (error, stackTrace) {
      AppConfig.logError('Failed to initialize cache monitoring service', error, stackTrace);
    }
  }

  /// Register a cache for monitoring
  void registerCache(String cacheName, {String? description}) {
    _cacheMetrics[cacheName] = CacheMetrics(
      name: cacheName,
      description: description ?? 'Cache for $cacheName',
      createdAt: DateTime.now(),
    );
    
    AppConfig.logInfo('Registered cache for monitoring: $cacheName');
  }

  /// Record cache hit
  void recordCacheHit(String cacheName, String key, {Duration? responseTime}) {
    _updateCacheMetrics(cacheName, isHit: true, key: key, responseTime: responseTime);
    _recordEvent(CacheEventType.hit, cacheName, key);
  }

  /// Record cache miss
  void recordCacheMiss(String cacheName, String key, {Duration? responseTime}) {
    _updateCacheMetrics(cacheName, isHit: false, key: key, responseTime: responseTime);
    _recordEvent(CacheEventType.miss, cacheName, key);
  }

  /// Record cache eviction
  void recordCacheEviction(String cacheName, String key, {String? reason}) {
    _updateCacheMetrics(cacheName, isEviction: true, key: key);
    _recordEvent(CacheEventType.eviction, cacheName, key, reason: reason);
  }

  /// Record cache error
  void recordCacheError(String cacheName, String key, String error) {
    _updateCacheMetrics(cacheName, isError: true, key: key);
    _recordEvent(CacheEventType.error, cacheName, key, reason: error);
    
    // Track error count
    _errorCounts[cacheName] = (_errorCounts[cacheName] ?? 0) + 1;
  }

  /// Record cache size change
  void recordCacheSizeChange(String cacheName, int oldSize, int newSize) {
    final metrics = _cacheMetrics[cacheName];
    if (metrics != null) {
      metrics.sizeHistory.add(CacheSizePoint(
        timestamp: DateTime.now(),
        size: newSize,
      ));
      
      // Keep only recent history
      if (metrics.sizeHistory.length > 100) {
        metrics.sizeHistory.removeAt(0);
      }
    }
    
    _recordEvent(CacheEventType.sizeChange, cacheName, '', 
      reason: 'Size changed from $oldSize to $newSize');
  }

  /// Record memory usage
  void recordMemoryUsage(String cacheName, int memoryBytes) {
    final metrics = _cacheMetrics[cacheName];
    if (metrics != null) {
      metrics.memoryHistory.add(CacheMemoryPoint(
        timestamp: DateTime.now(),
        memoryBytes: memoryBytes,
      ));
      
      // Keep only recent history
      if (metrics.memoryHistory.length > 100) {
        metrics.memoryHistory.removeAt(0);
      }
    }
  }

  /// Get comprehensive cache statistics
  Map<String, dynamic> getCacheStatistics() {
    final stats = <String, dynamic>{};
    
    for (final entry in _cacheMetrics.entries) {
      final cacheName = entry.key;
      final metrics = entry.value;
      
      stats[cacheName] = {
        'name': metrics.name,
        'description': metrics.description,
        'createdAt': metrics.createdAt.toIso8601String(),
        'totalHits': metrics.totalHits,
        'totalMisses': metrics.totalMisses,
        'totalEvictions': metrics.totalEvictions,
        'totalErrors': _errorCounts[cacheName] ?? 0,
        'hitRate': _calculateHitRate(metrics),
        'averageResponseTime': _calculateAverageResponseTime(metrics),
        'currentSize': metrics.sizeHistory.isNotEmpty 
            ? metrics.sizeHistory.last.size 
            : 0,
        'peakSize': _calculatePeakSize(metrics),
        'currentMemoryUsage': metrics.memoryHistory.isNotEmpty 
            ? metrics.memoryHistory.last.memoryBytes 
            : 0,
        'peakMemoryUsage': _calculatePeakMemory(metrics),
        'lastAccess': _lastAccess[cacheName]?.toIso8601String(),
        'uptime': DateTime.now().difference(metrics.createdAt).inSeconds,
      };
    }
    
    return stats;
  }

  /// Get performance analytics
  Map<String, dynamic> getPerformanceAnalytics() {
    final analytics = <String, dynamic>{};
    
    // Overall performance
    analytics['overall'] = {
      'totalCaches': _cacheMetrics.length,
      'totalEvents': _cacheEvents.length,
      'averageResponseTime': _calculateOverallAverageResponseTime(),
      'totalErrors': _errorCounts.values.fold(0, (sum, count) => sum + count),
      'errorRate': _calculateOverallErrorRate(),
    };
    
    // Performance trends
    analytics['trends'] = {
      'responseTimeTrend': _calculateResponseTimeTrend(),
      'hitRateTrend': _calculateHitRateTrend(),
      'memoryUsageTrend': _calculateMemoryUsageTrend(),
    };
    
    // Top performing caches
    analytics['topPerformers'] = _getTopPerformingCaches();
    
    // Problematic caches
    analytics['problematicCaches'] = _getProblematicCaches();
    
    return analytics;
  }

  /// Get cache events history
  List<Map<String, dynamic>> getCacheEvents({int limit = 100}) {
    final events = _cacheEvents.take(limit).map((event) => {
      'type': event.type.name,
      'cacheName': event.cacheName,
      'key': event.key,
      'timestamp': event.timestamp.toIso8601String(),
      'reason': event.reason,
    }).toList();
    
    return events;
  }

  /// Get cache health report
  Map<String, dynamic> getCacheHealthReport() {
    final report = <String, dynamic>{};
    
    for (final entry in _cacheMetrics.entries) {
      final cacheName = entry.key;
      final metrics = entry.value;
      
      final health = _calculateCacheHealth(metrics);
      report[cacheName] = {
        'status': health.status,
        'score': health.score,
        'issues': health.issues,
        'recommendations': health.recommendations,
      };
    }
    
    return report;
  }

  /// Start periodic monitoring
  void _startPeriodicMonitoring() {
    // Monitor every 5 minutes
    _monitoringTimers['periodic'] = Timer.periodic(
      const Duration(minutes: 5),
      (_) => _performPeriodicMonitoring(),
    );
  }

  /// Start performance tracking
  void _startPerformanceTracking() {
    // Track performance every minute
    _monitoringTimers['performance'] = Timer.periodic(
      const Duration(minutes: 1),
      (_) => _trackPerformance(),
    );
  }

  /// Perform periodic monitoring
  void _performPeriodicMonitoring() {
    for (final entry in _cacheMetrics.entries) {
      final cacheName = entry.key;
      final metrics = entry.value;
      
      // Check for issues
      _checkForIssues(cacheName, metrics);
      
      // Update last access time
      if (metrics.totalHits > 0 || metrics.totalMisses > 0) {
        _lastAccess[cacheName] = DateTime.now();
      }
    }
  }

  /// Track performance metrics
  void _trackPerformance() {
    // Calculate overall performance metrics
    final totalHits = _cacheMetrics.values.fold(0, (sum, m) => sum + m.totalHits);
    final totalMisses = _cacheMetrics.values.fold(0, (sum, m) => sum + m.totalMisses);
    
    if (totalHits + totalMisses > 0) {
      final overallHitRate = totalHits / (totalHits + totalMisses);
      AppConfig.logDebug('Overall cache hit rate: ${(overallHitRate * 100).toStringAsFixed(1)}%');
    }
  }

  /// Check for issues in cache
  void _checkForIssues(String cacheName, CacheMetrics metrics) {
    final issues = <String>[];
    
    // Check hit rate
    final hitRate = _calculateHitRate(metrics);
    if (hitRate < 0.5) {
      issues.add('Low hit rate: ${(hitRate * 100).toStringAsFixed(1)}%');
    }
    
    // Check error rate
    final errorCount = _errorCounts[cacheName] ?? 0;
    final totalRequests = metrics.totalHits + metrics.totalMisses;
    if (totalRequests > 0 && errorCount / totalRequests > 0.1) {
      issues.add('High error rate: ${(errorCount / totalRequests * 100).toStringAsFixed(1)}%');
    }
    
    // Check response time
    final avgResponseTime = _calculateAverageResponseTime(metrics);
    if (avgResponseTime.inMilliseconds > 100) {
      issues.add('Slow response time: ${avgResponseTime.inMilliseconds}ms');
    }
    
    if (issues.isNotEmpty) {
      AppConfig.logWarning('Cache issues detected for $cacheName: ${issues.join(', ')}');
    }
  }

  /// Update cache metrics
  void _updateCacheMetrics(
    String cacheName, {
    bool isHit = false,
    bool isEviction = false,
    bool isError = false,
    String? key,
    Duration? responseTime,
  }) {
    final metrics = _cacheMetrics[cacheName];
    if (metrics == null) return;
    
    if (isHit) {
      metrics.totalHits++;
    } else if (!isEviction && !isError) {
      metrics.totalMisses++;
    }
    
    if (isEviction) {
      metrics.totalEvictions++;
    }
    
    if (responseTime != null) {
      metrics.responseTimes.add(responseTime);
      if (metrics.responseTimes.length > maxResponseTimeHistory) {
        metrics.responseTimes.removeAt(0);
      }
    }
    
    _lastAccess[cacheName] = DateTime.now();
  }

  /// Record cache event
  void _recordEvent(
    CacheEventType type,
    String cacheName,
    String key, {
    String? reason,
  }) {
    _cacheEvents.add(CacheEvent(
      type: type,
      cacheName: cacheName,
      key: key,
      timestamp: DateTime.now(),
      reason: reason,
    ));
    
    // Keep event history manageable
    if (_cacheEvents.length > maxEventHistory) {
      _cacheEvents.removeAt(0);
    }
  }

  /// Calculate hit rate
  double _calculateHitRate(CacheMetrics metrics) {
    final total = metrics.totalHits + metrics.totalMisses;
    return total > 0 ? metrics.totalHits / total : 0.0;
  }

  /// Calculate average response time
  Duration _calculateAverageResponseTime(CacheMetrics metrics) {
    if (metrics.responseTimes.isEmpty) return Duration.zero;
    
    final totalMicroseconds = metrics.responseTimes
        .map((duration) => duration.inMicroseconds)
        .reduce((sum, microseconds) => sum + microseconds);
    
    return Duration(microseconds: totalMicroseconds ~/ metrics.responseTimes.length);
  }

  /// Calculate peak size
  int _calculatePeakSize(CacheMetrics metrics) {
    if (metrics.sizeHistory.isEmpty) return 0;
    
    return metrics.sizeHistory
        .map((point) => point.size)
        .reduce((max, size) => size > max ? size : max);
  }

  /// Calculate peak memory usage
  int _calculatePeakMemory(CacheMetrics metrics) {
    if (metrics.memoryHistory.isEmpty) return 0;
    
    return metrics.memoryHistory
        .map((point) => point.memoryBytes)
        .reduce((max, bytes) => bytes > max ? bytes : max);
  }

  /// Calculate overall average response time
  Duration _calculateOverallAverageResponseTime() {
    final allResponseTimes = <Duration>[];
    for (final metrics in _cacheMetrics.values) {
      allResponseTimes.addAll(metrics.responseTimes);
    }
    
    if (allResponseTimes.isEmpty) return Duration.zero;
    
    final totalMicroseconds = allResponseTimes
        .map((duration) => duration.inMicroseconds)
        .reduce((sum, microseconds) => sum + microseconds);
    
    return Duration(microseconds: totalMicroseconds ~/ allResponseTimes.length);
  }

  /// Calculate overall error rate
  double _calculateOverallErrorRate() {
    final totalErrors = _errorCounts.values.fold(0, (sum, count) => sum + count);
    final totalRequests = _cacheMetrics.values.fold(0, (sum, m) => sum + m.totalHits + m.totalMisses);
    
    return totalRequests > 0 ? totalErrors / totalRequests : 0.0;
  }

  /// Calculate response time trend
  List<double> _calculateResponseTimeTrend() {
    // Simple trend calculation - would be more sophisticated in production
    return [0.0, 0.0, 0.0]; // Placeholder
  }

  /// Calculate hit rate trend
  List<double> _calculateHitRateTrend() {
    // Simple trend calculation - would be more sophisticated in production
    return [0.0, 0.0, 0.0]; // Placeholder
  }

  /// Calculate memory usage trend
  List<double> _calculateMemoryUsageTrend() {
    // Simple trend calculation - would be more sophisticated in production
    return [0.0, 0.0, 0.0]; // Placeholder
  }

  /// Get top performing caches
  List<Map<String, dynamic>> _getTopPerformingCaches() {
    final performers = <Map<String, dynamic>>[];
    
    for (final entry in _cacheMetrics.entries) {
      final hitRate = _calculateHitRate(entry.value);
      performers.add({
        'name': entry.key,
        'hitRate': hitRate,
        'totalRequests': entry.value.totalHits + entry.value.totalMisses,
      });
    }
    
    performers.sort((a, b) => (b['hitRate'] as double).compareTo(a['hitRate'] as double));
    return performers.take(5).toList();
  }

  /// Get problematic caches
  List<Map<String, dynamic>> _getProblematicCaches() {
    final problematic = <Map<String, dynamic>>[];
    
    for (final entry in _cacheMetrics.entries) {
      final cacheName = entry.key;
      final metrics = entry.value;
      final hitRate = _calculateHitRate(metrics);
      final errorCount = _errorCounts[cacheName] ?? 0;
      
      if (hitRate < 0.5 || errorCount > 10) {
        problematic.add({
          'name': cacheName,
          'hitRate': hitRate,
          'errorCount': errorCount,
          'issues': hitRate < 0.5 ? 'Low hit rate' : 'High error count',
        });
      }
    }
    
    return problematic;
  }

  /// Calculate cache health
  CacheHealth _calculateCacheHealth(CacheMetrics metrics) {
    double score = 100.0;
    final issues = <String>[];
    final recommendations = <String>[];
    
    // Check hit rate
    final hitRate = _calculateHitRate(metrics);
    if (hitRate < 0.5) {
      score -= 30;
      issues.add('Low hit rate');
      recommendations.add('Consider increasing cache size or improving cache keys');
    }
    
    // Check error rate
    final errorCount = _errorCounts[metrics.name] ?? 0;
    final totalRequests = metrics.totalHits + metrics.totalMisses;
    if (totalRequests > 0 && errorCount / totalRequests > 0.1) {
      score -= 25;
      issues.add('High error rate');
      recommendations.add('Investigate cache errors and improve error handling');
    }
    
    // Check response time
    final avgResponseTime = _calculateAverageResponseTime(metrics);
    if (avgResponseTime.inMilliseconds > 100) {
      score -= 20;
      issues.add('Slow response time');
      recommendations.add('Optimize cache operations or consider faster storage');
    }
    
    // Determine status
    String status;
    if (score >= 80) {
      status = 'Healthy';
    } else if (score >= 60) {
      status = 'Warning';
    } else {
      status = 'Critical';
    }
    
    return CacheHealth(
      status: status,
      score: score,
      issues: issues,
      recommendations: recommendations,
    );
  }

  /// Clear all monitoring data
  void clearMonitoringData() {
    _cacheMetrics.clear();
    _cacheEvents.clear();
    _errorCounts.clear();
    _lastAccess.clear();
    
    for (final timer in _monitoringTimers.values) {
      timer.cancel();
    }
    _monitoringTimers.clear();
    
    AppConfig.logInfo('Cache monitoring data cleared');
  }

  /// Dispose of the service
  void dispose() {
    for (final timer in _monitoringTimers.values) {
      timer.cancel();
    }
    _monitoringTimers.clear();
    
    _cacheMetrics.clear();
    _cacheEvents.clear();
    _errorCounts.clear();
    _lastAccess.clear();
    _responseTimes.clear();
    
    _initialized = false;
  }
}

/// Cache metrics data structure
class CacheMetrics {
  final String name;
  final String description;
  final DateTime createdAt;
  
  int totalHits = 0;
  int totalMisses = 0;
  int totalEvictions = 0;
  final List<Duration> responseTimes = [];
  final List<CacheSizePoint> sizeHistory = [];
  final List<CacheMemoryPoint> memoryHistory = [];

  CacheMetrics({
    required this.name,
    required this.description,
    required this.createdAt,
  });
}

/// Cache size point
class CacheSizePoint {
  final DateTime timestamp;
  final int size;

  CacheSizePoint({
    required this.timestamp,
    required this.size,
  });
}

/// Cache memory point
class CacheMemoryPoint {
  final DateTime timestamp;
  final int memoryBytes;

  CacheMemoryPoint({
    required this.timestamp,
    required this.memoryBytes,
  });
}

/// Cache event
class CacheEvent {
  final CacheEventType type;
  final String cacheName;
  final String key;
  final DateTime timestamp;
  final String? reason;

  CacheEvent({
    required this.type,
    required this.cacheName,
    required this.key,
    required this.timestamp,
    this.reason,
  });
}

/// Cache event type
enum CacheEventType {
  hit,
  miss,
  eviction,
  error,
  sizeChange,
}

/// Cache health
class CacheHealth {
  final String status;
  final double score;
  final List<String> issues;
  final List<String> recommendations;

  CacheHealth({
    required this.status,
    required this.score,
    required this.issues,
    required this.recommendations,
  });
} 