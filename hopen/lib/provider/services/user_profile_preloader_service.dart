import 'package:flutter/foundation.dart';

import '../../di/injection_container_refactored.dart' as di;
import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../notifiers/user_profile_notifier.dart';
import '../notifiers/profile_picture_notifier.dart';

/// Service responsible for preloading user profile data and profile pictures
/// immediately after authentication.
/// 
/// This ensures that:
/// 1. User profile data is cached in Drift database
/// 2. Profile picture is cached for instant display
/// 3. All user data is ready for immediate access
class UserProfilePreloaderService {
  static UserProfilePreloaderService? _instance;
  
  UserProfilePreloaderService._();
  
  static UserProfilePreloaderService get instance {
    _instance ??= UserProfilePreloaderService._();
    return _instance!;
  }

  bool _isPreloading = false;
  String? _lastPreloadedUserId;

  /// Preload user profile and profile picture immediately after authentication
  /// 
  /// This method:
  /// 1. Caches the user profile data in Drift database
  /// 2. Initializes the UserProfileNotifier with the user
  /// 3. Triggers profile picture caching
  /// 4. Ensures all data is ready for instant access
  Future<void> preloadUserData(UserModel user) async {
    // Skip if already preloading for this user
    if (_isPreloading && _lastPreloadedUserId == user.id) {
      debugPrint('👤 UserProfilePreloaderService: Already preloading for user ${user.id}');
      return;
    }

    _isPreloading = true;
    _lastPreloadedUserId = user.id;

    try {
      debugPrint('👤 UserProfilePreloaderService: Starting preload for user ${user.id}');

      // 1. Cache user profile data
      await _cacheUserProfile(user);

      // 2. Initialize UserProfileNotifier
      await _initializeUserProfileNotifier(user);

      // 3. Preload profile picture if available
      await _preloadProfilePicture(user);

      debugPrint('👤 UserProfilePreloaderService: Successfully preloaded all data for user ${user.id}');
    } catch (e) {
      debugPrint('👤 UserProfilePreloaderService: Error during preload for user ${user.id}: $e');
    } finally {
      _isPreloading = false;
    }
  }

  /// Cache user profile data in Drift database
  Future<void> _cacheUserProfile(UserModel user) async {
    try {
      debugPrint('👤 UserProfilePreloaderService: Caching user profile for ${user.id}');
      
      final userProfileNotifier = di.sl<UserProfileNotifier>();
      await userProfileNotifier.cacheUserProfile(user);
      
      debugPrint('👤 UserProfilePreloaderService: User profile cached successfully');
    } catch (e) {
      debugPrint('👤 UserProfilePreloaderService: Error caching user profile: $e');
    }
  }

  /// Initialize UserProfileNotifier with the authenticated user
  Future<void> _initializeUserProfileNotifier(UserModel user) async {
    try {
      debugPrint('👤 UserProfilePreloaderService: Initializing UserProfileNotifier for ${user.id}');
      
      final userProfileNotifier = di.sl<UserProfileNotifier>();
      await userProfileNotifier.initializeUser(user.id);
      
      debugPrint('👤 UserProfilePreloaderService: UserProfileNotifier initialized successfully');
    } catch (e) {
      debugPrint('👤 UserProfilePreloaderService: Error initializing UserProfileNotifier: $e');
    }
  }

  /// Preload profile picture for instant display
  Future<void> _preloadProfilePicture(UserModel user) async {
    final avatarUrl = user.profilePictureUrl;
    
    if (avatarUrl == null || !avatarUrl.startsWith('http')) {
      debugPrint('👤 UserProfilePreloaderService: No valid avatar URL for user ${user.id}');
      return;
    }

    try {
      debugPrint('👤 UserProfilePreloaderService: Preloading profile picture: $avatarUrl');
      
      final profilePictureNotifier = di.sl<ProfilePictureNotifier>();
      await profilePictureNotifier.fetchProfilePicture(user.id, avatarUrl);
      
      debugPrint('👤 UserProfilePreloaderService: Profile picture preloaded successfully');
    } catch (e) {
      debugPrint('👤 UserProfilePreloaderService: Error preloading profile picture: $e');
    }
  }

  /// Alternative method for preloading by user ID (when you don't have the full UserModel)
  /// 
  /// This will trigger the UserProfileNotifier to load the user data from cache or network
  Future<void> preloadUserDataById(String userId) async {
    try {
      debugPrint('👤 UserProfilePreloaderService: Preloading user data by ID: $userId');
      
      final userProfileNotifier = di.sl<UserProfileNotifier>();
      await userProfileNotifier.initializeUser(userId);
      
      debugPrint('👤 UserProfilePreloaderService: User data preloaded by ID successfully');
    } catch (e) {
      debugPrint('👤 UserProfilePreloaderService: Error preloading user data by ID: $e');
    }
  }

  /// Clear the preloader state (useful for logout)
  void reset() {
    _isPreloading = false;
    _lastPreloadedUserId = null;
    debugPrint('👤 UserProfilePreloaderService: Reset preloader state');
  }

  /// Get preloader status
  bool get isPreloading => _isPreloading;
  String? get lastPreloadedUserId => _lastPreloadedUserId;
}
