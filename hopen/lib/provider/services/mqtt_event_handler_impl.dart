/// MQTT Event Handler Implementation
///
/// This service handles MQTT events and coordinates updates to repositories
/// following the four-layer dependency rule.

import 'dart:async';
import 'dart:convert';
import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';
import '../../repositories/contact/contact_request_repository.dart';
import '../../repositories/friendship/friend_request_repository.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';

class MqttEventHandlerImpl implements MqttEventHandler {
  MqttEventHandlerImpl({
    required ContactRequestRepository contactRequestRepository,
    required FriendRequestRepository friendRequestRepository,
  }) : _contactRequestRepository = contactRequestRepository,
       _friendRequestRepository = friendRequestRepository;

  final ContactRequestRepository _contactRequestRepository;
  final FriendRequestRepository _friendRequestRepository;

  @override
  Future<void> handleContactRequestEvent(ContactRequestEvent event) async {
    try {
      LoggingService.info(
        'MqttEventHandler: Processing contact request event: ${event.action}',
      );
      await _contactRequestRepository.processContactRequestEvent(event);
      LoggingService.info(
        'MqttEventHandler: Contact request event processed successfully',
      );
    } catch (e) {
      LoggingService.error(
        'MqttEventHandler: Failed to handle contact request event: $e',
      );
    }
  }

  @override
  Future<void> handleFriendRequestEvent(FriendRequestEvent event) async {
    try {
      LoggingService.info(
        'MqttEventHandler: Processing friend request event: ${event.action}',
      );
      await _friendRequestRepository.processFriendRequestEvent(event);
      LoggingService.info(
        'MqttEventHandler: Friend request event processed successfully',
      );
    } catch (e) {
      LoggingService.error(
        'MqttEventHandler: Failed to handle friend request event: $e',
      );
    }
  }

  @override
  Future<void> handleBubbleEvent(BubbleEvent event) async {
    try {
      LoggingService.info(
        'MqttEventHandler: Processing bubble event: ${event.action}',
      );
      // Handle bubble events (e.g., update bubble repository)
      LoggingService.info(
        'MqttEventHandler: Bubble event processed successfully',
      );
    } catch (e) {
      LoggingService.error(
        'MqttEventHandler: Failed to handle bubble event: $e',
      );
    }
  }

  @override
  Future<void> handleSystemEvent(SystemEvent event) async {
    try {
      LoggingService.info(
        'MqttEventHandler: Processing system event: ${event.type}',
      );
      // Handle system events (e.g., show notifications)
      LoggingService.info(
        'MqttEventHandler: System event processed successfully',
      );
    } catch (e) {
      LoggingService.error(
        'MqttEventHandler: Failed to handle system event: $e',
      );
    }
  }

  /// Parse MQTT message and route to appropriate handler
  @override
  Future<void> handleMqttMessage(String topic, String payload) async {
    try {
      LoggingService.debug(
        'MqttEventHandler: Received message on topic: $topic',
      );

      final data = jsonDecode(payload) as Map<String, dynamic>;
      final eventType = _extractEventTypeFromTopic(topic);

      if (eventType == null) {
        LoggingService.warning(
          'MqttEventHandler: Unknown topic format: $topic',
        );
        return;
      }

      final event = MqttEvent.fromJson(data, eventType);

      switch (event) {
        case ContactRequestEvent():
          await handleContactRequestEvent(event);
        case FriendRequestEvent():
          await handleFriendRequestEvent(event);
        case BubbleEvent():
          await handleBubbleEvent(event);
        case SystemEvent():
          await handleSystemEvent(event);
      }
    } catch (e) {
      LoggingService.error(
        'MqttEventHandler: Failed to parse MQTT message: $e',
      );
    }
  }

  /// Extract event type from MQTT topic
  String? _extractEventTypeFromTopic(String topic) {
    // Topic format: hopen/{event_type}/{user_id}
    final parts = topic.split('/');
    if (parts.length >= 3 && parts[0] == 'hopen') {
      switch (parts[1]) {
        case 'contact':
          return 'contact_request';
        case 'friendship':
          return 'friend_request';
        case 'bubbles':
          return 'bubble';
        case 'system':
          return 'system';
        default:
          return null;
      }
    }
    return null;
  }
}

/// MQTT-driven Real-Time Service
///
/// This service replaces the polling-based RealTimeNotificationService
/// with a pure MQTT-driven approach.
class MqttDrivenRealTimeService {
  MqttDrivenRealTimeService({required MqttEventHandler eventHandler})
    : _eventHandler = eventHandler;

  final MqttEventHandler _eventHandler;
  String? _currentUserId;
  bool _isInitialized = false;

  /// Initialize the service for a specific user
  Future<void> initialize(String userId) async {
    if (_isInitialized && _currentUserId == userId) {
      return;
    }

    _currentUserId = userId;
    _isInitialized = true;

    LoggingService.info(
      'MqttDrivenRealTimeService: Initialized for user $userId',
    );
  }

  /// Handle incoming MQTT message
  Future<void> handleMessage(String topic, String payload) async {
    if (!_isInitialized) {
      LoggingService.warning(
        'MqttDrivenRealTimeService: Service not initialized',
      );
      return;
    }

    await _eventHandler.handleMqttMessage(topic, payload);
  }

  /// Get topics to subscribe to for the current user
  List<String> getSubscriptionTopics() {
    if (_currentUserId == null) {
      return [];
    }

    return MqttTopics.getAllTopics(_currentUserId!);
  }

  /// Stop the service
  Future<void> stop() async {
    _isInitialized = false;
    _currentUserId = null;
    LoggingService.info('MqttDrivenRealTimeService: Stopped');
  }
}
