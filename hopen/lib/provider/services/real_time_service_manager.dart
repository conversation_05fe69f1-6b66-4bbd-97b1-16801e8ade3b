import 'dart:async';

import '../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../statefulbusinesslogic/core/services/notification_orchestrator_interface.dart';
import '../../repositories/auth/auth_repository.dart';
import '../services/auth/ory_auth_service.dart';
// Removed RealTimeNotificationService import - service has been deleted
import 'mqtt_only_real_time_service.dart';
import '../../di/injection_container_refactored.dart' as di;

/// Simplified real-time service manager using unified notification service
///
/// This manager provides a single point of coordination for ALL real-time features:
/// - Contact requests
/// - Bubble invitations & requests
/// - Friend requests
/// - Call notifications
/// - Chat messages
/// - System notifications
/// - Presence updates
///
/// **Benefits of Unified Approach:**
/// - Single MQTT connection for all features
/// - Consistent lifecycle management
/// - Better resource utilization
/// - Easier maintenance and debugging
/// - Scalable architecture for new features
class RealTimeServiceManager {
  static RealTimeServiceManager? _instance;
  static RealTimeServiceManager get instance {
    _instance ??= RealTimeServiceManager._internal();
    return _instance!;
  }

  RealTimeServiceManager._internal();

  // Single unified service for all real-time features (MQTT-only)
  MqttOnlyRealTimeService? _mqttService;

  // State tracking
  bool _isInitialized = false;
  String? _currentUserId;
  StreamSubscription? _authStateSubscription;

  /// Initialize the service manager
  Future<void> initialize() async {
    if (_isInitialized) {
      LoggingService.info('RealTimeServiceManager: Already initialized');
      return;
    }

    LoggingService.info('RealTimeServiceManager: Initializing');

    // Listen to Ory authentication state changes
    _listenToOryAuthState();

    // Check current authentication state
    _checkCurrentAuthStateAsync();

    _isInitialized = true;
    LoggingService.info('RealTimeServiceManager: Initialization complete');
  }

  /// Listen to Ory authentication state changes
  void _listenToOryAuthState() {
    try {
      final oryAuthService = di.sl<OryAuthService>();

      // Listen to auth state stream
      _authStateSubscription = oryAuthService.authStateStream.listen((
        authState,
      ) {
        LoggingService.info(
          'RealTimeServiceManager: Auth state changed: $authState',
        );

        switch (authState) {
          case OryAuthState.signedIn:
            final user = oryAuthService.currentUser;
            if (user != null) {
              _onUserAuthenticated(user.id);
            }
            break;
          case OryAuthState.signedOut:
            _onUserUnauthenticated();
            break;
          case OryAuthState.loading:
            // Do nothing while loading
            break;
        }
      });

      LoggingService.info(
        'RealTimeServiceManager: Listening to Ory auth state changes',
      );
    } catch (e) {
      LoggingService.error(
        'RealTimeServiceManager: Error setting up auth state listener: $e',
      );
    }
  }

  /// Check current authentication state asynchronously
  void _checkCurrentAuthStateAsync() {
    Future.microtask(() async {
      try {
        final oryAuthService = di.sl<OryAuthService>();

        // Check if user is currently signed in
        if (oryAuthService.isSignedIn && oryAuthService.currentUser != null) {
          _onUserAuthenticated(oryAuthService.currentUser!.id);
        } else {
          // Try to get current user from auth repository as fallback
          final authRepository = di.sl<AuthRepository>();
          final result = await authRepository.getCurrentUser();

          result.fold(
            onFailure: (_) => _onUserUnauthenticated(),
            onSuccess: (user) => _onUserAuthenticated(user.id),
          );
        }
      } catch (e) {
        LoggingService.error(
          'RealTimeServiceManager: Error checking current auth state: $e',
        );
        _onUserUnauthenticated();
      }
    });
  }

  /// Handle user authentication
  Future<void> _onUserAuthenticated(String? userId) async {
    if (userId == null) {
      LoggingService.warning(
        'RealTimeServiceManager: User authenticated but no user ID provided',
      );
      return;
    }

    if (_currentUserId == userId) {
      LoggingService.info(
        'RealTimeServiceManager: User already authenticated with same ID',
      );
      return;
    }

    LoggingService.info('RealTimeServiceManager: User authenticated: $userId');
    _currentUserId = userId;

    // Stop existing service if any
    await _stopService();

    // Start unified real-time service
    await _startService(userId);
  }

  /// Handle user unauthentication
  Future<void> _onUserUnauthenticated() async {
    LoggingService.info('RealTimeServiceManager: User unauthenticated');
    _currentUserId = null;

    // Stop unified real-time service
    await _stopService();
  }

  /// Start unified real-time service for authenticated user
  Future<void> _startService(String userId) async {
    LoggingService.info(
      'RealTimeServiceManager: Starting unified real-time service for user $userId',
    );

    try {
      // Create MQTT-only service instance if not exists
      _mqttService ??= di.sl<MqttOnlyRealTimeService>();

      // Initialize the MQTT-only service
      await _mqttService!.initialize();

      // Start the service for the specific user
      await _mqttService!.start(userId);

      // Fetch pending notifications after successful initialization
      await _fetchPendingNotifications();

      LoggingService.info(
        'RealTimeServiceManager: Unified real-time service started successfully',
      );
    } catch (e, stackTrace) {
      LoggingService.error(
        'RealTimeServiceManager: Failed to start unified real-time service: $e',
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Fetch pending notifications when user logs in
  Future<void> _fetchPendingNotifications() async {
    try {
      LoggingService.info(
        'RealTimeServiceManager: Fetching pending notifications',
      );

      // Use NotificationOrchestratorInterface to fetch notifications
      // This follows the four-layer dependency rule properly
      final notificationOrchestrator =
          di.sl<NotificationOrchestratorInterface>();
      await notificationOrchestrator.getNotifications();

      LoggingService.info(
        'RealTimeServiceManager: Triggered notification fetch via orchestrator',
      );
    } catch (e) {
      LoggingService.error(
        'RealTimeServiceManager: Failed to fetch pending notifications: $e',
      );
    }
  }

  /// Stop unified real-time service
  Future<void> _stopService() async {
    LoggingService.info(
      'RealTimeServiceManager: Stopping unified real-time service',
    );

    try {
      // Stop MQTT-only service
      if (_mqttService != null) {
        await _mqttService!.stop();
        _mqttService = null;
        LoggingService.info(
          'RealTimeServiceManager: MQTT-only real-time service stopped',
        );
      }

      // Legacy notification service has been removed - only using MqttOnlyRealTimeService

      LoggingService.info(
        'RealTimeServiceManager: Unified real-time service stopped successfully',
      );
    } catch (e) {
      LoggingService.error(
        'RealTimeServiceManager: Error stopping unified real-time service: $e',
      );
    }
  }

  // Public getters for accessing notification streams

  // Stream getters removed - use callback-based approach instead
  // The RealTimeNotificationService uses callbacks for notification handling

  // Status getters

  /// Get current user ID
  String? get currentUserId => _currentUserId;

  /// Get initialization status
  bool get isInitialized => _isInitialized;

  /// Get unified service status
  bool get isServiceActive => _mqttService?.isConnected ?? false;

  /// Dispose of the service manager
  Future<void> dispose() async {
    LoggingService.info('RealTimeServiceManager: Disposing service manager');

    try {
      // Cancel auth state subscription
      await _authStateSubscription?.cancel();
      _authStateSubscription = null;

      // Stop the unified service
      await _stopService();

      _isInitialized = false;
      _currentUserId = null;

      LoggingService.info(
        'RealTimeServiceManager: Service manager disposed successfully',
      );
    } catch (e) {
      LoggingService.error(
        'RealTimeServiceManager: Error disposing service manager: $e',
      );
    }
  }
}
