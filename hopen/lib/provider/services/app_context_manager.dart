import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';

// Removed direct import of DialogServiceImpl to follow four-layer dependency rule
import '../../statefulbusinesslogic/core/services/dialog_service.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../di/injection_container_refactored.dart' as di;
import 'real_time_notification_service.dart';
import 'real_time_service_manager.dart';
import 'notification_service_fcm.dart';

/// Manages app-wide context for services that need access to the current BuildContext
/// Handles proper initialization and cleanup of context-dependent services
class AppContextManager extends StatefulWidget {
  final Widget child;

  AppContextManager({super.key, required this.child}) {
    print('AppContextManager: Constructor called');
  }

  @override
  State<AppContextManager> createState() {
    print('AppContextManager: createState called');
    return _AppContextManagerState();
  }
}

class _AppContextManagerState extends State<AppContextManager>
    with WidgetsBindingObserver {
  DialogService? _dialogService;
  RealTimeServiceManager? _realTimeServiceManager;
  NotificationServiceFCM? _fcmService;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    print('AppContextManager: initState called');
    WidgetsBinding.instance.addObserver(this);

    // Initialize services after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      print(
        'AppContextManager: Post-frame callback triggered, calling _initializeServices',
      );
      _initializeServices();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _cleanupServices();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        _onAppResumed();
        break;
      case AppLifecycleState.paused:
        _onAppPaused();
        break;
      case AppLifecycleState.detached:
        _onAppDetached();
        break;
      case AppLifecycleState.inactive:
        // App is transitioning between states
        break;
      case AppLifecycleState.hidden:
        // App is hidden but still running
        break;
    }
  }

  /// Initialize context-dependent services
  Future<void> _initializeServices() async {
    if (_isInitialized) return;

    try {
      LoggingService.info(
        'AppContextManager: Initializing context-dependent services',
      );

      // Get services from dependency injection
      _dialogService = di.sl<DialogService>();
      LoggingService.info('AppContextManager: Got DialogService from DI');

      try {
        _realTimeServiceManager = di.sl<RealTimeServiceManager>();
      } catch (e) {
        LoggingService.warning(
          'AppContextManager: RealTimeServiceManager not available: $e',
        );
      }

      try {
        _fcmService = NotificationServiceFCM();
      } catch (e) {
        LoggingService.warning(
          'AppContextManager: FCM Service not available: $e',
        );
      }

      // Dialog service context is handled by the presentation layer
      // Provider layer only uses the DialogService interface methods
      LoggingService.info('AppContextManager: Dialog service ready');

      // Initialize real-time service manager
      if (_realTimeServiceManager != null) {
        await _realTimeServiceManager!.initialize();
        LoggingService.info(
          'AppContextManager: Real-time service manager initialized',
        );
      }

      // Initialize FCM service
      if (_fcmService != null) {
        await _initializeFCMService();
      }

      _isInitialized = true;
      LoggingService.success(
        'AppContextManager: All services initialized successfully',
      );
    } catch (e, stackTrace) {
      LoggingService.error(
        'AppContextManager: Error initializing services: $e',
        stackTrace: stackTrace,
      );
    }
  }

  /// Initialize FCM service
  Future<void> _initializeFCMService() async {
    try {
      // NotificationServiceFCM doesn't have isInitialized property, just initialize
      await _fcmService!.initialize();
      LoggingService.info('AppContextManager: FCM service initialized');
    } catch (e) {
      LoggingService.error(
        'AppContextManager: Error initializing FCM service: $e',
      );
    }
  }

  /// Handle app resumed state
  void _onAppResumed() {
    LoggingService.info('AppContextManager: App resumed');

    // Dialog service context is managed by the presentation layer
    // Provider layer only uses interface methods

    // Real-time service manager handles reconnection internally
  }

  /// Handle app paused state
  void _onAppPaused() {
    LoggingService.info('AppContextManager: App paused');

    // Services will handle background state internally
    // No need to clear context as dialogs shouldn't show when paused
  }

  /// Handle app detached state
  void _onAppDetached() {
    LoggingService.info('AppContextManager: App detached');
    _cleanupServices();
  }

  /// Cleanup services when app is closing
  void _cleanupServices() {
    try {
      LoggingService.info('AppContextManager: Cleaning up services');

      // Clear dialog service context
      if (_dialogService != null) {
        (_dialogService as dynamic).clearContext();
      }

      // Dispose real-time service manager
      if (_realTimeServiceManager != null) {
        _realTimeServiceManager!.dispose();
      }

      _isInitialized = false;
      LoggingService.info('AppContextManager: Services cleaned up');
    } catch (e) {
      LoggingService.error('AppContextManager: Error cleaning up services: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Widget that provides app context management
/// Wrap your app's main content with this widget
class AppContextProvider extends StatefulWidget {
  final Widget child;

  const AppContextProvider({super.key, required this.child});

  @override
  State<AppContextProvider> createState() => _AppContextProviderState();
}

class _AppContextProviderState extends State<AppContextProvider> {
  bool _isDialogServiceInitialized = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Initialize DialogService context only once when dependencies are available
    // This is the correct lifecycle method for context-dependent initialization
    if (!_isDialogServiceInitialized) {
      _initializeDialogService();
      _isDialogServiceInitialized = true;
    }
  }

  void _initializeDialogService() {
    print(
      'AppContextProvider: Skipping DialogService context initialization - will be handled by NavigatorContextProvider',
    );
    // Context will be set by NavigatorContextProvider which has access to Navigator
  }

  @override
  Widget build(BuildContext context) {
    print('AppContextProvider: build() called, creating AppContextManager');
    // Build method is now pure - no side effects, following Flutter best practices
    return AppContextManager(child: widget.child);
  }
}

/// Widget that sets the DialogService context with Navigator access
/// This should be placed inside the MaterialApp.router widget tree
class NavigatorContextProvider extends StatefulWidget {
  final Widget child;

  const NavigatorContextProvider({super.key, required this.child});

  @override
  State<NavigatorContextProvider> createState() =>
      _NavigatorContextProviderState();
}

class _NavigatorContextProviderState extends State<NavigatorContextProvider> {
  bool _isDialogServiceInitialized = false;

  @override
  void initState() {
    super.initState();
    print('NavigatorContextProvider: initState() called');

    // Use addPostFrameCallback to ensure the Navigator is available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_isDialogServiceInitialized) {
        _initializeDialogService();
        _isDialogServiceInitialized = true;
      }
    });
  }

  void _initializeDialogService() {
    print(
      'NavigatorContextProvider: Initializing DialogService context with Navigator access',
    );
    try {
      // DialogService context setting is handled by the presentation layer
      // Provider layer should not access implementation-specific methods
      print(
        'NavigatorContextProvider: DialogService context management delegated to presentation layer',
      );
    } catch (e) {
      print(
        'NavigatorContextProvider: Error setting DialogService context: $e',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    print('NavigatorContextProvider: build() called');
    return widget.child;
  }
}
