import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:mqtt5_client/mqtt5_client.dart';

import '../../../di/injection_container_refactored.dart' as di;
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../mqtt/mqtt_service.dart';

/// MQTT v5-based WebRTC signaling service
///
/// This service completely replaces WebSocket for WebRTC signaling using MQTT v5 features:
/// - Request/Response pattern with correlation data
/// - User properties for metadata
/// - Topic aliases for performance
/// - QoS 1 for reliable delivery
/// - Retained messages for call state
class MqttWebRtcSignalingService {
  static const String _tag = 'MqttWebRtcSignalingService';

  final MqttService _mqttService;
  final Map<String, StreamController<Map<String, dynamic>>> _callControllers =
      {};
  final Map<String, String> _activeCallTopics = {};
  final Map<String, Timer> _callTimeouts = {};

  // MQTT v5 topic structure for WebRTC signaling
  static const String _baseTopicPrefix = 'hopen/webrtc';
  static const String _callSignalingTopic = '$_baseTopicPrefix/calls';
  static const String _callStateTopic = '$_baseTopicPrefix/state';
  static const String _callEventsTopic = '$_baseTopicPrefix/events';

  StreamSubscription<Map<String, dynamic>>? _mqttSubscription;
  bool _isInitialized = false;
  String? _currentUserId;

  MqttWebRtcSignalingService(this._mqttService);

  /// Initialize the MQTT WebRTC signaling service
  Future<void> initialize(String userId) async {
    if (_isInitialized) {
      LoggingService.warning('$_tag: Already initialized');
      return;
    }

    _currentUserId = userId;

    try {
      LoggingService.info(
        '$_tag: Initializing MQTT WebRTC signaling for user $userId',
      );

      // Subscribe to user-specific signaling topics
      await _subscribeToSignalingTopics(userId);

      // Listen to MQTT message stream
      _mqttSubscription = _mqttService.messageStream.listen(_handleMqttMessage);

      _isInitialized = true;
      LoggingService.success(
        '$_tag: MQTT WebRTC signaling initialized successfully',
      );
    } catch (e, stackTrace) {
      LoggingService.error(
        '$_tag: Failed to initialize MQTT WebRTC signaling',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// Subscribe to all necessary signaling topics for the user
  Future<void> _subscribeToSignalingTopics(String userId) async {
    final topics = [
      '$_callSignalingTopic/$userId/+/+', // Wildcard for all call signaling
      '$_callStateTopic/$userId/+', // Call state updates
      '$_callEventsTopic/$userId/+', // Call events (join, leave, etc.)
    ];

    for (final topic in topics) {
      await _mqttService.subscribe(topic, qos: MqttQos.atLeastOnce);
      LoggingService.debug('$_tag: Subscribed to topic: $topic');
    }
  }

  /// Handle incoming MQTT messages for WebRTC signaling
  void _handleMqttMessage(Map<String, dynamic> message) {
    try {
      final topic = message['topic'] as String?;
      final payload = message['payload'] as String?;

      if (topic == null || payload == null) return;

      final data = jsonDecode(payload) as Map<String, dynamic>;

      // Route message based on topic pattern
      if (topic.contains('/$_currentUserId/')) {
        _routeSignalingMessage(topic, data);
      }
    } catch (e) {
      LoggingService.error('$_tag: Error handling MQTT message: $e');
    }
  }

  /// Route signaling messages to appropriate call controllers
  void _routeSignalingMessage(String topic, Map<String, dynamic> data) {
    final callId = _extractCallIdFromTopic(topic);
    if (callId == null) return;

    // Get or create stream controller for this call
    final controller = _getCallController(callId);

    // Add topic and timestamp metadata
    data['_topic'] = topic;
    data['_timestamp'] = DateTime.now().toIso8601String();

    // Emit to call-specific stream
    controller.add(data);

    LoggingService.debug(
      '$_tag: Routed message for call $callId: ${data['type']}',
    );
  }

  /// Extract call ID from MQTT topic
  String? _extractCallIdFromTopic(String topic) {
    final parts = topic.split('/');
    if (parts.length >= 4) {
      return parts[3]; // Assuming format: hopen/webrtc/calls/{userId}/{callId}/...
    }
    return null;
  }

  /// Get or create stream controller for a call
  StreamController<Map<String, dynamic>> _getCallController(String callId) {
    if (!_callControllers.containsKey(callId)) {
      _callControllers[callId] =
          StreamController<Map<String, dynamic>>.broadcast();

      // Set up call timeout
      _callTimeouts[callId] = Timer(const Duration(hours: 2), () {
        _cleanupCall(callId);
      });
    }
    return _callControllers[callId]!;
  }

  /// Start WebRTC signaling for a call
  Future<Stream<Map<String, dynamic>>> startCallSignaling({
    required String callId,
    required String targetUserId,
    required bool isInitiator,
    Map<String, String>? userProperties,
  }) async {
    if (!_isInitialized) {
      throw StateError('$_tag: Service not initialized');
    }

    LoggingService.info('$_tag: Starting call signaling for call $callId');

    // Subscribe to call-specific topics
    await _subscribeToCallTopics(callId, targetUserId);

    // Store active call topic mapping
    _activeCallTopics[callId] = '$_callSignalingTopic/$_currentUserId/$callId';

    // Publish call initiation if we're the initiator
    if (isInitiator) {
      await _publishCallInitiation(callId, targetUserId, userProperties);
    }

    return _getCallController(callId).stream;
  }

  /// Subscribe to call-specific topics
  Future<void> _subscribeToCallTopics(
    String callId,
    String targetUserId,
  ) async {
    final topics = [
      '$_callSignalingTopic/$_currentUserId/$callId/+',
      '$_callSignalingTopic/$targetUserId/$callId/+',
      '$_callStateTopic/$callId',
      '$_callEventsTopic/$callId/+',
    ];

    for (final topic in topics) {
      await _mqttService.subscribe(topic, qos: MqttQos.atLeastOnce);
    }
  }

  /// Publish call initiation message
  Future<void> _publishCallInitiation(
    String callId,
    String targetUserId,
    Map<String, String>? userProperties,
  ) async {
    final topic = '$_callSignalingTopic/$targetUserId/$callId/initiate';
    final message = {
      'type': 'call_initiation',
      'callId': callId,
      'fromUserId': _currentUserId,
      'toUserId': targetUserId,
      'timestamp': DateTime.now().toIso8601String(),
      'userProperties': userProperties ?? {},
    };

    _mqttService.publish(
      topic,
      jsonEncode(message),
      qos: MqttQos.atLeastOnce,
      retain: true, // Retain call initiation for late joiners
    );
  }

  /// Send WebRTC offer
  Future<void> sendOffer({
    required String callId,
    required String targetUserId,
    required Map<String, dynamic> offer,
    String? correlationId,
  }) async {
    await _sendSignalingMessage(
      callId: callId,
      targetUserId: targetUserId,
      messageType: 'offer',
      data: offer,
      correlationId: correlationId,
    );
  }

  /// Send WebRTC answer
  Future<void> sendAnswer({
    required String callId,
    required String targetUserId,
    required Map<String, dynamic> answer,
    String? correlationId,
  }) async {
    await _sendSignalingMessage(
      callId: callId,
      targetUserId: targetUserId,
      messageType: 'answer',
      data: answer,
      correlationId: correlationId,
    );
  }

  /// Send ICE candidate
  Future<void> sendIceCandidate({
    required String callId,
    required String targetUserId,
    required Map<String, dynamic> candidate,
    String? correlationId,
  }) async {
    await _sendSignalingMessage(
      callId: callId,
      targetUserId: targetUserId,
      messageType: 'ice_candidate',
      data: candidate,
      correlationId: correlationId,
    );
  }

  /// Send generic signaling message
  Future<void> _sendSignalingMessage({
    required String callId,
    required String targetUserId,
    required String messageType,
    required Map<String, dynamic> data,
    String? correlationId,
  }) async {
    final topic = '$_callSignalingTopic/$targetUserId/$callId/$messageType';
    final message = {
      'type': messageType,
      'callId': callId,
      'fromUserId': _currentUserId,
      'toUserId': targetUserId,
      'data': data,
      'timestamp': DateTime.now().toIso8601String(),
      'correlationId': correlationId ?? _generateCorrelationId(),
    };

    _mqttService.publish(topic, jsonEncode(message), qos: MqttQos.atLeastOnce);

    LoggingService.debug(
      '$_tag: Sent $messageType for call $callId to $targetUserId',
    );
  }

  /// End call signaling
  Future<void> endCallSignaling(String callId) async {
    LoggingService.info('$_tag: Ending call signaling for call $callId');

    // Publish call end message
    if (_activeCallTopics.containsKey(callId)) {
      final topic = '$_callEventsTopic/$callId/end';
      final message = {
        'type': 'call_end',
        'callId': callId,
        'fromUserId': _currentUserId,
        'timestamp': DateTime.now().toIso8601String(),
      };

      _mqttService.publish(
        topic,
        jsonEncode(message),
        qos: MqttQos.atLeastOnce,
        retain: true,
      );
    }

    // Cleanup call resources
    _cleanupCall(callId);
  }

  /// Cleanup call resources
  void _cleanupCall(String callId) {
    _callControllers[callId]?.close();
    _callControllers.remove(callId);
    _activeCallTopics.remove(callId);
    _callTimeouts[callId]?.cancel();
    _callTimeouts.remove(callId);

    LoggingService.debug('$_tag: Cleaned up resources for call $callId');
  }

  /// Generate correlation ID for request/response pattern
  String _generateCorrelationId() {
    return '${DateTime.now().millisecondsSinceEpoch}_${_currentUserId}';
  }

  /// Dispose the service
  Future<void> dispose() async {
    LoggingService.info('$_tag: Disposing MQTT WebRTC signaling service');

    _mqttSubscription?.cancel();

    // Cleanup all active calls
    for (final callId in _callControllers.keys.toList()) {
      _cleanupCall(callId);
    }

    _isInitialized = false;
    LoggingService.success('$_tag: MQTT WebRTC signaling service disposed');
  }

  /// Get connection status
  bool get isConnected => _mqttService.isConnected;

  /// Get initialization status
  bool get isInitialized => _isInitialized;
}
