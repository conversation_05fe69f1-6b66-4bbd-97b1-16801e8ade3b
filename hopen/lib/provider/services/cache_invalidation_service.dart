import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../config/app_config.dart';

/// Cache invalidation service for managing cache lifecycle
/// Provides sophisticated cache invalidation strategies
class CacheInvalidationService {
  factory CacheInvalidationService() => _instance;
  CacheInvalidationService._internal();
  static final CacheInvalidationService _instance = CacheInvalidationService._internal();

  // Cache invalidation patterns
  final Map<String, List<String>> _cachePatterns = {};
  final Map<String, Timer> _invalidationTimers = {};
  final Map<String, DateTime> _lastInvalidation = {};
  
  // Invalidation strategies
  final Map<String, InvalidationStrategy> _strategies = {};
  
  bool _initialized = false;

  /// Initialize the cache invalidation service
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Register default invalidation strategies
      _registerDefaultStrategies();
      
      // Start periodic cleanup
      _startPeriodicCleanup();
      
      AppConfig.logInfo('Cache invalidation service initialized');
      _initialized = true;
    } catch (error, stackTrace) {
      AppConfig.logError('Failed to initialize cache invalidation service', error, stackTrace);
    }
  }

  /// Register default invalidation strategies
  void _registerDefaultStrategies() {
    // User data invalidation
    registerStrategy('user_data', InvalidationStrategy(
      patterns: ['/api/users/*', '/api/profile/*', '/api/settings/*'],
      dependencies: ['profile_pictures', 'user_settings'],
      invalidationDelay: const Duration(seconds: 5),
    ));

    // Chat data invalidation
    registerStrategy('chat_data', InvalidationStrategy(
      patterns: ['/api/chats/*', '/api/messages/*'],
      dependencies: ['chat_metadata', 'unread_counts'],
      invalidationDelay: const Duration(seconds: 2),
    ));

    // Media data invalidation
    registerStrategy('media_data', InvalidationStrategy(
      patterns: ['/api/media/*', '/api/files/*'],
      dependencies: ['profile_pictures', 'media_cache'],
      invalidationDelay: const Duration(seconds: 10),
    ));

    // Authentication invalidation
    registerStrategy('auth_data', InvalidationStrategy(
      patterns: ['/api/auth/*', '/api/session/*'],
      dependencies: ['user_data', 'chat_data', 'media_data'],
      invalidationDelay: const Duration(seconds: 1),
    ));
  }

  /// Register a new invalidation strategy
  void registerStrategy(String name, InvalidationStrategy strategy) {
    _strategies[name] = strategy;
    AppConfig.logInfo('Registered cache invalidation strategy: $name');
  }

  /// Invalidate cache by user ID
  Future<void> invalidateUserData(String userId) async {
    AppConfig.logInfo('Invalidating cache for user: $userId');
    
    try {
      // Clear user-specific caches
      await _clearProfilePictureCache(userId);
      await _clearUserSettingsCache(userId);
      await _clearUserMessagesCache(userId);
      await _clearUserContactsCache(userId);
      
      // Invalidate related patterns
      await _invalidateByPatterns([
        '/api/users/$userId/*',
        '/api/profile/$userId/*',
        '/api/settings/$userId/*',
      ]);
      
      AppConfig.logSuccess('Successfully invalidated cache for user: $userId');
    } catch (error, stackTrace) {
      AppConfig.logError('Failed to invalidate cache for user: $userId', error, stackTrace);
    }
  }

  /// Invalidate cache by pattern
  Future<void> invalidateByPattern(String pattern) async {
    AppConfig.logInfo('Invalidating cache by pattern: $pattern');
    
    try {
      // Find matching strategy
      final strategy = _findMatchingStrategy(pattern);
      if (strategy != null) {
        await _executeStrategy(strategy, [pattern]);
      } else {
        // Default invalidation
        await _invalidateByPatterns([pattern]);
      }
      
      AppConfig.logSuccess('Successfully invalidated cache for pattern: $pattern');
    } catch (error, stackTrace) {
      AppConfig.logError('Failed to invalidate cache for pattern: $pattern', error, stackTrace);
    }
  }

  /// Invalidate cache by URL
  Future<void> invalidateByUrl(String url) async {
    AppConfig.logInfo('Invalidating cache by URL: $url');
    
    try {
      // Extract path from URL
      final uri = Uri.parse(url);
      final path = uri.path;
      
      // Find matching strategy
      final strategy = _findMatchingStrategy(path);
      if (strategy != null) {
        await _executeStrategy(strategy, [path]);
      } else {
        // Default invalidation
        await _invalidateByPatterns([path]);
      }
      
      AppConfig.logSuccess('Successfully invalidated cache for URL: $url');
    } catch (error, stackTrace) {
      AppConfig.logError('Failed to invalidate cache for URL: $url', error, stackTrace);
    }
  }

  /// Invalidate cache by strategy name
  Future<void> invalidateByStrategy(String strategyName) async {
    final strategy = _strategies[strategyName];
    if (strategy == null) {
      AppConfig.logWarning('Strategy not found: $strategyName');
      return;
    }

    AppConfig.logInfo('Invalidating cache by strategy: $strategyName');
    
    try {
      await _executeStrategy(strategy, strategy.patterns);
      AppConfig.logSuccess('Successfully invalidated cache by strategy: $strategyName');
    } catch (error, stackTrace) {
      AppConfig.logError('Failed to invalidate cache by strategy: $strategyName', error, stackTrace);
    }
  }

  /// Schedule delayed invalidation
  Future<void> scheduleInvalidation(String pattern, {Duration? delay}) async {
    final invalidationDelay = delay ?? const Duration(seconds: 5);
    final key = 'scheduled_${pattern.hashCode}';
    
    // Cancel existing timer if any
    _invalidationTimers[key]?.cancel();
    
    // Schedule new invalidation
    _invalidationTimers[key] = Timer(invalidationDelay, () async {
      await invalidateByPattern(pattern);
      _invalidationTimers.remove(key);
    });
    
    AppConfig.logInfo('Scheduled cache invalidation for pattern: $pattern in ${invalidationDelay.inSeconds}s');
  }

  /// Invalidate all caches
  Future<void> invalidateAll() async {
    AppConfig.logInfo('Invalidating all caches');
    
    try {
      // Cancel all scheduled invalidations
      for (final timer in _invalidationTimers.values) {
        timer.cancel();
      }
      _invalidationTimers.clear();
      
      // Clear all cache patterns
      _cachePatterns.clear();
      _lastInvalidation.clear();
      
      // Execute all strategies
      for (final strategy in _strategies.values) {
        await _executeStrategy(strategy, strategy.patterns);
      }
      
      AppConfig.logSuccess('Successfully invalidated all caches');
    } catch (error, stackTrace) {
      AppConfig.logError('Failed to invalidate all caches', error, stackTrace);
    }
  }

  /// Find matching strategy for a pattern
  InvalidationStrategy? _findMatchingStrategy(String pattern) {
    for (final entry in _strategies.entries) {
      for (final strategyPattern in entry.value.patterns) {
        if (_patternMatches(strategyPattern, pattern)) {
          return entry.value;
        }
      }
    }
    return null;
  }

  /// Check if pattern matches
  bool _patternMatches(String pattern, String path) {
    // Simple wildcard matching
    final regexPattern = pattern
        .replaceAll('*', '.*')
        .replaceAll('/', '\\/');
    
    try {
      final regex = RegExp('^$regexPattern\$');
      return regex.hasMatch(path);
    } catch (e) {
      return pattern == path;
    }
  }

  /// Execute invalidation strategy
  Future<void> _executeStrategy(InvalidationStrategy strategy, List<String> patterns) async {
    // Check if we should delay invalidation
    if (strategy.invalidationDelay > Duration.zero) {
      await Future.delayed(strategy.invalidationDelay);
    }
    
    // Invalidate patterns
    await _invalidateByPatterns(patterns);
    
    // Invalidate dependencies
    for (final dependency in strategy.dependencies) {
      await _invalidateDependency(dependency);
    }
    
    // Update last invalidation time
    final now = DateTime.now();
    for (final pattern in patterns) {
      _lastInvalidation[pattern] = now;
    }
  }

  /// Invalidate by patterns
  Future<void> _invalidateByPatterns(List<String> patterns) async {
    for (final pattern in patterns) {
      // Store pattern for tracking
      _cachePatterns[pattern] = _cachePatterns[pattern] ?? [];
      
      // Invalidate network cache
      await _invalidateNetworkCache(pattern);
      
      // Invalidate local cache
      await _invalidateLocalCache(pattern);
    }
  }

  /// Invalidate network cache
  Future<void> _invalidateNetworkCache(String pattern) async {
    try {
      // This would integrate with NetworkOptimizationService
      // For now, we'll log the invalidation
      AppConfig.logDebug('Invalidating network cache for pattern: $pattern');
    } catch (e) {
      AppConfig.logWarning('Failed to invalidate network cache for pattern: $pattern: $e');
    }
  }

  /// Invalidate local cache
  Future<void> _invalidateLocalCache(String pattern) async {
    try {
      // This would integrate with local storage services
      // For now, we'll log the invalidation
      AppConfig.logDebug('Invalidating local cache for pattern: $pattern');
    } catch (e) {
      AppConfig.logWarning('Failed to invalidate local cache for pattern: $pattern: $e');
    }
  }

  /// Invalidate dependency
  Future<void> _invalidateDependency(String dependency) async {
    try {
      switch (dependency) {
        case 'profile_pictures':
          await _clearProfilePictureCache(null);
          break;
        case 'user_settings':
          await _clearUserSettingsCache(null);
          break;
        case 'chat_metadata':
          await _clearChatMetadataCache();
          break;
        case 'unread_counts':
          await _clearUnreadCountsCache();
          break;
        case 'media_cache':
          await _clearMediaCache();
          break;
        default:
          AppConfig.logWarning('Unknown dependency: $dependency');
      }
    } catch (e) {
      AppConfig.logWarning('Failed to invalidate dependency: $dependency: $e');
    }
  }

  /// Clear profile picture cache
  Future<void> _clearProfilePictureCache(String? userId) async {
    try {
      // This would integrate with ProfilePictureService
      AppConfig.logDebug('Clearing profile picture cache for user: $userId');
    } catch (e) {
      AppConfig.logWarning('Failed to clear profile picture cache: $e');
    }
  }

  /// Clear user settings cache
  Future<void> _clearUserSettingsCache(String? userId) async {
    try {
      // This would integrate with UserSettingsRepository
      AppConfig.logDebug('Clearing user settings cache for user: $userId');
    } catch (e) {
      AppConfig.logWarning('Failed to clear user settings cache: $e');
    }
  }

  /// Clear user messages cache
  Future<void> _clearUserMessagesCache(String? userId) async {
    try {
      // This would integrate with message repositories
      AppConfig.logDebug('Clearing user messages cache for user: $userId');
    } catch (e) {
      AppConfig.logWarning('Failed to clear user messages cache: $e');
    }
  }

  /// Clear user contacts cache
  Future<void> _clearUserContactsCache(String? userId) async {
    try {
      // This would integrate with contact repositories
      AppConfig.logDebug('Clearing user contacts cache for user: $userId');
    } catch (e) {
      AppConfig.logWarning('Failed to clear user contacts cache: $e');
    }
  }

  /// Clear chat metadata cache
  Future<void> _clearChatMetadataCache() async {
    try {
      // This would integrate with chat repositories
      AppConfig.logDebug('Clearing chat metadata cache');
    } catch (e) {
      AppConfig.logWarning('Failed to clear chat metadata cache: $e');
    }
  }

  /// Clear unread counts cache
  Future<void> _clearUnreadCountsCache() async {
    try {
      // This would integrate with unread count repositories
      AppConfig.logDebug('Clearing unread counts cache');
    } catch (e) {
      AppConfig.logWarning('Failed to clear unread counts cache: $e');
    }
  }

  /// Clear media cache
  Future<void> _clearMediaCache() async {
    try {
      // This would integrate with media services
      AppConfig.logDebug('Clearing media cache');
    } catch (e) {
      AppConfig.logWarning('Failed to clear media cache: $e');
    }
  }

  /// Start periodic cleanup
  void _startPeriodicCleanup() {
    Timer.periodic(const Duration(minutes: 30), (_) => _cleanupExpiredData());
  }

  /// Clean up expired data
  void _cleanupExpiredData() {
    final now = DateTime.now();
    final expiredPatterns = <String>[];
    
    for (final entry in _lastInvalidation.entries) {
      final age = now.difference(entry.value);
      if (age > const Duration(hours: 24)) {
        expiredPatterns.add(entry.key);
      }
    }
    
    for (final pattern in expiredPatterns) {
      _cachePatterns.remove(pattern);
      _lastInvalidation.remove(pattern);
    }
    
    if (expiredPatterns.isNotEmpty) {
      AppConfig.logInfo('Cleaned up ${expiredPatterns.length} expired cache patterns');
    }
  }

  /// Get invalidation statistics
  Map<String, dynamic> getInvalidationStats() {
    return {
      'totalStrategies': _strategies.length,
      'totalPatterns': _cachePatterns.length,
      'scheduledInvalidations': _invalidationTimers.length,
      'lastInvalidations': _lastInvalidation.length,
      'strategies': _strategies.keys.toList(),
    };
  }

  /// Dispose of the service
  void dispose() {
    for (final timer in _invalidationTimers.values) {
      timer.cancel();
    }
    _invalidationTimers.clear();
    _cachePatterns.clear();
    _lastInvalidation.clear();
    _strategies.clear();
    _initialized = false;
  }
}

/// Invalidation strategy configuration
class InvalidationStrategy {
  final List<String> patterns;
  final List<String> dependencies;
  final Duration invalidationDelay;

  const InvalidationStrategy({
    required this.patterns,
    required this.dependencies,
    this.invalidationDelay = Duration.zero,
  });
} 