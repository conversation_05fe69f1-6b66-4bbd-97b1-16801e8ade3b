import 'dart:async';
import 'dart:io';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import '../../config/app_config.dart';

/// UI optimization service for performance improvements
/// Handles lazy loading, animation optimization, and rendering performance
class UIOptimizationService {
  factory UIOptimizationService() => _instance;
  UIOptimizationService._internal();
  static final UIOptimizationService _instance = UIOptimizationService._internal();

  // Performance tracking
  final List<FrameTimingInfo> _frameTimings = [];
  final Map<String, AnimationController> _activeAnimations = {};
  final Map<String, Timer> _debounceTimers = {};
  
  // Enhanced image caching
  final Map<String, ImageProvider> _imageCache = {};
  final Map<String, DateTime> _imageCacheTimestamps = {};
  final Map<String, int> _imageCacheHits = {};
  final Map<String, int> _imageCacheMisses = {};
  
  // Widget disposal tracking
  final Set<String> _disposedWidgets = {};
  
  // Memory pressure handling
  bool _isMemoryPressureActive = false;
  int _maxCachedImages = 100; // Dynamic cache size
  
  bool _initialized = false;
  TimingsCallback? _timingsCallback;

  /// Initialize the UI optimization service
  Future<void> initialize() async {
    if (_initialized) return;

    try {
      // Calculate optimal cache size based on device capabilities
      _maxCachedImages = _calculateOptimalCacheSize();
      
      // Start frame timing monitoring
      _startFrameMonitoring();
      
      // Start periodic cleanup
      _startPeriodicCleanup();
      
      // Start memory pressure monitoring
      _startMemoryPressureMonitoring();
      
      AppConfig.logInfo('UI optimization service initialized with max cache size: $_maxCachedImages');
      _initialized = true;
    } catch (error, stackTrace) {
      AppConfig.logError('Failed to initialize UI optimization service', error, stackTrace);
    }
  }

  /// Calculate optimal cache size based on device capabilities
  int _calculateOptimalCacheSize() {
    try {
      // Get device memory info (platform-specific)
      if (Platform.isAndroid || Platform.isIOS) {
        // For mobile devices, use conservative cache sizes
        return 50; // Conservative for mobile
      } else if (Platform.isMacOS || Platform.isWindows) {
        // For desktop, use larger cache sizes
        return 200;
      } else {
        // Default for other platforms
        return 100;
      }
    } catch (e) {
      AppConfig.logWarning('Could not determine device capabilities, using default cache size');
      return 100;
    }
  }

  /// Create an optimized animation controller
  AnimationController createOptimizedAnimationController({
    required Duration duration,
    required TickerProvider vsync,
    String? debugLabel,
    double? value,
    Duration? reverseDuration,
    AnimationBehavior animationBehavior = AnimationBehavior.normal,
  }) {
    final controller = AnimationController(
      duration: duration,
      vsync: vsync,
      debugLabel: debugLabel,
      value: value,
      reverseDuration: reverseDuration,
      animationBehavior: animationBehavior,
    );

    // Track active animations
    final key = debugLabel ?? 'animation_${controller.hashCode}';
    _activeAnimations[key] = controller;

    // Auto-dispose when animation completes
    controller.addStatusListener((status) {
      if (status == AnimationStatus.completed || status == AnimationStatus.dismissed) {
        _cleanupAnimation(key);
      }
    });

    return controller;
  }

  /// Create an optimized image provider with enhanced caching
  ImageProvider getOptimizedImage(
    String imageUrl, {
    Duration? cacheDuration,
    double? scale,
    Map<String, String>? headers,
  }) {
    final cacheKey = _generateImageCacheKey(imageUrl, scale, headers);
    
    // Check cache first
    final cached = _imageCache[cacheKey];
    final cacheTime = _imageCacheTimestamps[cacheKey];
    
    if (cached != null && cacheTime != null) {
      final age = DateTime.now().difference(cacheTime);
      if (age < (cacheDuration ?? AppConfig.cacheExpiration)) {
        _recordImageCacheHit(cacheKey);
        return cached;
      }
    }

    _recordImageCacheMiss(cacheKey);

    // Create new image provider
    ImageProvider provider;
    if (imageUrl.startsWith('http')) {
      provider = NetworkImage(imageUrl, scale: scale ?? 1.0, headers: headers);
    } else if (imageUrl.startsWith('assets/')) {
      provider = AssetImage(imageUrl);
    } else {
      provider = FileImage(File(imageUrl));
    }

    // Cache the provider
    _imageCache[cacheKey] = provider;
    _imageCacheTimestamps[cacheKey] = DateTime.now();

    // Limit cache size with dynamic sizing
    if (_imageCache.length > _maxCachedImages) {
      _cleanupImageCache();
    }

    return provider;
  }

  /// Create an optimized list view with lazy loading
  Widget createOptimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    bool addAutomaticKeepAlives = true,
    bool addRepaintBoundaries = true,
    bool addSemanticIndexes = true,
    double? cacheExtent,
    int? semanticChildCount,
    DragStartBehavior dragStartBehavior = DragStartBehavior.start,
    ScrollViewKeyboardDismissBehavior keyboardDismissBehavior = ScrollViewKeyboardDismissBehavior.manual,
    String? restorationId,
    Clip clipBehavior = Clip.hardEdge,
  }) {
    return ListView.builder(
      itemCount: itemCount,
      itemBuilder: (context, index) {
        // Wrap each item in RepaintBoundary for performance
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
      controller: controller,
      addAutomaticKeepAlives: addAutomaticKeepAlives,
      addRepaintBoundaries: addRepaintBoundaries,
      addSemanticIndexes: addSemanticIndexes,
      cacheExtent: cacheExtent,
      semanticChildCount: semanticChildCount,
      dragStartBehavior: dragStartBehavior,
      keyboardDismissBehavior: keyboardDismissBehavior,
      restorationId: restorationId,
      clipBehavior: clipBehavior,
    );
  }

  /// Create an optimized grid view with lazy loading
  Widget createOptimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required SliverGridDelegate gridDelegate,
    ScrollController? controller,
    bool addAutomaticKeepAlives = true,
    bool addRepaintBoundaries = true,
    bool addSemanticIndexes = true,
    double? cacheExtent,
    int? semanticChildCount,
    DragStartBehavior dragStartBehavior = DragStartBehavior.start,
    ScrollViewKeyboardDismissBehavior keyboardDismissBehavior = ScrollViewKeyboardDismissBehavior.manual,
    String? restorationId,
    Clip clipBehavior = Clip.hardEdge,
  }) {
    return GridView.builder(
      itemCount: itemCount,
      itemBuilder: (context, index) {
        // Wrap each item in RepaintBoundary for performance
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
      gridDelegate: gridDelegate,
      controller: controller,
      addAutomaticKeepAlives: addAutomaticKeepAlives,
      addRepaintBoundaries: addRepaintBoundaries,
      addSemanticIndexes: addSemanticIndexes,
      cacheExtent: cacheExtent,
      semanticChildCount: semanticChildCount,
      dragStartBehavior: dragStartBehavior,
      keyboardDismissBehavior: keyboardDismissBehavior,
      restorationId: restorationId,
      clipBehavior: clipBehavior,
    );
  }

  /// Create a debounced function
  Timer createDebouncedFunction(
    VoidCallback callback, {
    Duration duration = const Duration(milliseconds: 300),
    String? key,
  }) {
    final debounceKey = key ?? 'debounce_${callback.hashCode}';
    
    // Cancel existing timer
    _debounceTimers[debounceKey]?.cancel();
    
    // Create new timer
    final timer = Timer(duration, () {
      callback();
      _debounceTimers.remove(debounceKey);
    });
    
    _debounceTimers[debounceKey] = timer;
    return timer;
  }

  /// Start frame timing monitoring
  void _startFrameMonitoring() {
    _timingsCallback = (List<FrameTiming> timings) {
      final frameTimingInfos = timings.map((t) => FrameTimingInfo(
        0, // frameNumber - we'll use a counter if needed
        t.totalSpan,
        t.buildDuration,
        t.rasterDuration,
      )).toList();
      
      _frameTimings.addAll(frameTimingInfos);
      
      // Keep only last 100 frame timings
      if (_frameTimings.length > 100) {
        _frameTimings.removeRange(0, _frameTimings.length - 100);
      }
      
      // Check for performance issues
      _checkPerformanceIssues(frameTimingInfos);
    };
    
    SchedulerBinding.instance.addTimingsCallback(_timingsCallback!);
  }

  /// Start periodic cleanup
  void _startPeriodicCleanup() {
    Timer.periodic(const Duration(minutes: 5), (_) {
      _cleanupExpiredCache();
      _cleanupUnusedAnimations();
    });
  }

  /// Start memory pressure monitoring
  void _startMemoryPressureMonitoring() {
    // Monitor memory pressure on platforms that support it
    if (Platform.isAndroid || Platform.isIOS) {
      SystemChrome.setSystemUIChangeCallback((systemOverlaysAreVisible) async {
        // This is a simple proxy for memory pressure
        _checkMemoryPressure();
      });
    }
  }

  /// Check for memory pressure and handle it
  void _checkMemoryPressure() {
    if (_isMemoryPressureActive) return;
    
    // Simple memory pressure detection based on cache size and frame performance
    if (_imageCache.length > (_maxCachedImages * 0.8) || _hasPerformanceIssues()) {
      _handleMemoryPressure();
    }
  }

  /// Handle memory pressure by clearing caches
  void _handleMemoryPressure() {
    if (_isMemoryPressureActive) return;
    
    _isMemoryPressureActive = true;
    AppConfig.logInfo('Handling memory pressure - clearing UI caches');
    
    try {
      // Clear image cache
      _imageCache.clear();
      _imageCacheTimestamps.clear();
      _imageCacheHits.clear();
      _imageCacheMisses.clear();
      
      // Clear Flutter's image cache
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();
      
      // Clear animation controllers
      for (final controller in _activeAnimations.values) {
        controller.dispose();
      }
      _activeAnimations.clear();
      
      // Cancel debounce timers
      for (final timer in _debounceTimers.values) {
        timer.cancel();
      }
      _debounceTimers.clear();
      
      // Clear frame timings
      _frameTimings.clear();
      
      AppConfig.logInfo('Memory pressure handled - UI caches cleared');
    } finally {
      _isMemoryPressureActive = false;
    }
  }

  /// Check for performance issues
  void _checkPerformanceIssues(List<FrameTimingInfo> timings) {
    for (final timing in timings) {
      final buildTime = timing.buildDuration.inMilliseconds;
      final rasterTime = timing.rasterDuration.inMilliseconds;
      
      // Log performance warnings
      if (buildTime > 16) { // 60 FPS = 16ms per frame
        AppConfig.logWarning('Slow build time: ${buildTime}ms');
      }
      
      if (rasterTime > 16) {
        AppConfig.logWarning('Slow raster time: ${rasterTime}ms');
      }
    }
  }

  /// Check if there are performance issues
  bool _hasPerformanceIssues() {
    if (_frameTimings.isEmpty) return false;
    
    final recentTimings = _frameTimings.take(10);
    final avgBuildTime = recentTimings
        .map((t) => t.buildDuration.inMilliseconds)
        .reduce((a, b) => a + b) / recentTimings.length;
    
    return avgBuildTime > 20; // 20ms average build time threshold
  }

  /// Clean up expired cache entries
  void _cleanupExpiredCache() {
    final now = DateTime.now();
    final expiredKeys = <String>[];
    
    for (final entry in _imageCacheTimestamps.entries) {
      final age = now.difference(entry.value);
      if (age > AppConfig.cacheExpiration) {
        expiredKeys.add(entry.key);
      }
    }
    
    for (final key in expiredKeys) {
      _imageCache.remove(key);
      _imageCacheTimestamps.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      AppConfig.logInfo('Cleaned up ${expiredKeys.length} expired image cache entries');
    }
  }

  /// Clean up unused animations
  void _cleanupUnusedAnimations() {
    final unusedKeys = <String>[];
    
    for (final entry in _activeAnimations.entries) {
      if (!entry.value.isAnimating && entry.value.status == AnimationStatus.dismissed) {
        unusedKeys.add(entry.key);
      }
    }
    
    for (final key in unusedKeys) {
      _cleanupAnimation(key);
    }
  }

  /// Clean up a specific animation
  void _cleanupAnimation(String key) {
    final controller = _activeAnimations.remove(key);
    controller?.dispose();
  }

  /// Clean up image cache when size limit is exceeded
  void _cleanupImageCache() {
    // Remove oldest entries first (LRU)
    final sortedEntries = _imageCacheTimestamps.entries.toList()
      ..sort((a, b) => a.value.compareTo(b.value));
    
    final entriesToRemove = sortedEntries.take(_imageCache.length - _maxCachedImages + 10);
    
    for (final entry in entriesToRemove) {
      _imageCache.remove(entry.key);
      _imageCacheTimestamps.remove(entry.key);
    }
    
    AppConfig.logInfo('Cleaned up ${entriesToRemove.length} image cache entries');
  }

  /// Generate cache key for image
  String _generateImageCacheKey(String imageUrl, double? scale, Map<String, String>? headers) {
    final headerString = headers?.entries
        .map((e) => '${e.key}:${e.value}')
        .join('|') ?? '';
    return '$imageUrl:${scale ?? 1.0}:$headerString';
  }

  /// Record image cache hit
  void _recordImageCacheHit(String cacheKey) {
    _imageCacheHits[cacheKey] = (_imageCacheHits[cacheKey] ?? 0) + 1;
  }

  /// Record image cache miss
  void _recordImageCacheMiss(String cacheKey) {
    _imageCacheMisses[cacheKey] = (_imageCacheMisses[cacheKey] ?? 0) + 1;
  }

  /// Calculate image cache hit rate
  double _calculateImageCacheHitRate() {
    final totalHits = _imageCacheHits.values.fold(0, (sum, hits) => sum + hits);
    final totalMisses = _imageCacheMisses.values.fold(0, (sum, misses) => sum + misses);
    final total = totalHits + totalMisses;
    
    return total > 0 ? totalHits / total : 0.0;
  }

  /// Get performance statistics
  Map<String, dynamic> getPerformanceStats() {
    final recentTimings = _frameTimings.take(10);
    final avgBuildTime = recentTimings.isNotEmpty
        ? recentTimings
            .map((t) => t.buildDuration.inMilliseconds)
            .reduce((a, b) => a + b) / recentTimings.length
        : 0.0;
    
    final avgRasterTime = recentTimings.isNotEmpty
        ? recentTimings
            .map((t) => t.rasterDuration.inMilliseconds)
            .reduce((a, b) => a + b) / recentTimings.length
        : 0.0;

    return {
      'imageCacheSize': _imageCache.length,
      'imageCacheHitRate': _calculateImageCacheHitRate(),
      'activeAnimations': _activeAnimations.length,
      'debounceTimers': _debounceTimers.length,
      'frameTimings': _frameTimings.length,
      'averageBuildTime': avgBuildTime,
      'averageRasterTime': avgRasterTime,
      'isMemoryPressureActive': _isMemoryPressureActive,
      'maxCachedImages': _maxCachedImages,
    };
  }

  /// Clear all caches
  void clearAllCaches() {
    _imageCache.clear();
    _imageCacheTimestamps.clear();
    _imageCacheHits.clear();
    _imageCacheMisses.clear();
    
    // Clear Flutter's image cache
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
    
    AppConfig.logInfo('All UI caches cleared');
  }

  /// Dispose of the service
  void dispose() {
    _timingsCallback?.let((callback) => 
      SchedulerBinding.instance.removeTimingsCallback(callback));
    
    for (final controller in _activeAnimations.values) {
      controller.dispose();
    }
    
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    
    clearAllCaches();
    _frameTimings.clear();
    _disposedWidgets.clear();
    _initialized = false;
  }
}

/// Extension for nullable value handling
extension NullableExtension<T> on T? {
  void let(Function(T) action) {
    if (this != null) {
      action(this!);
    }
  }
}

/// Helper class for frame timing info
class FrameTimingInfo {
  FrameTimingInfo(this.frameNumber, this.totalSpan, this.buildDuration, this.rasterDuration);
  final int frameNumber;
  final Duration totalSpan;
  final Duration buildDuration;
  final Duration rasterDuration;
} 