import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:mqtt5_client/mqtt5_client.dart';
import 'package:mqtt5_client/mqtt5_server_client.dart';
import 'dart:io';

import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../local_storage/local_storage_service.dart';
import '../../config/ssl_pinning.dart' as pin;

class MqttService {
  MqttServerClient? _client;
  final Map<String, StreamController<String>> _topicControllers = {};
  bool _isConnected = false;
  bool _shouldReconnect = true;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const int _reconnectDelay = 5000; // milliseconds
  String? _host;
  int? _port;
  String? _clientId;
  String? _username;
  String? _password;
  String? _currentUserId;
  final int _keepAliveInterval = 60;
  final Set<String> _processedMessages = {};
  
  // Add message stream controller
  final StreamController<Map<String, dynamic>> _messageController = 
      StreamController<Map<String, dynamic>>.broadcast();

  // Public getter for message stream
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;

  Future<bool> connect({
    String host = 'localhost',
    int port = 1883,
    String clientId = 'test-client',
    String? username,
    String? password,
    Map<String, String>? userProperties,
  }) async {
    _host = host;
    _port = port;
    _clientId = clientId;

    // Prefer explicit username/password if provided, otherwise attempt JWT-based auth
    _username = username ?? _currentUserId;
    _password = password ?? await _getLatestToken();

    return _establishConnection(userProperties: userProperties);
  }

  Future<bool> _establishConnection({Map<String, String>? userProperties}) async {
    _client = MqttServerClient.withPort(_host!, _clientId!, _port!);
    _client!.logging(on: false);
    _client!.keepAlivePeriod = _keepAliveInterval;
    _client!.autoReconnect = false;

    // Enable secure WebSocket with pinned certs
    _client!.useWebSocket = true;
    _client!.secure = true;
    _client!.securityContext = SecurityContext();
    _client!.onBadCertificate = (cert) => pin.isPinnedCert(cert!);

    final connMessage = MqttConnectMessage()
        .withClientIdentifier(_clientId!)
        .authenticateAs(_username, _password);

    if (userProperties != null && userProperties.isNotEmpty) {
      for (final entry in userProperties.entries) {
        connMessage.addUserPropertyPair(entry.key, entry.value);
      }
    }

    _client!.connectionMessage = connMessage;

    try {
      LoggingService.info('Connecting to MQTT broker at $_host:$_port');
      await _client!.connect();

      if (_client!.connectionStatus!.state == MqttConnectionState.connected) {
        LoggingService.info('MQTT connected successfully');
        _isConnected = true;
        _reconnectAttempts = 0;
        _setupConnectionMonitoring();
        return true;
      } else {
        LoggingService.error('MQTT connection failed: ${_client!.connectionStatus!.state}');
        return false;
      }
    } catch (e) {
      LoggingService.error('MQTT connection error: $e');
      return false;
    }
  }

  void _setupConnectionMonitoring() {
    _client!.onConnected = () {
      LoggingService.info('MQTT connected');
      _isConnected = true;
      _reconnectAttempts = 0;
    };

    _client!.onDisconnected = () {
      LoggingService.info('MQTT disconnected');
      _isConnected = false;
      if (_shouldReconnect) {
        _scheduleReconnect();
      }
    };

    _client!.onSubscribed = (subscription) {
      LoggingService.info('Subscribed to topic: ${subscription.topic.rawTopic}');
    };

    _client!.onUnsubscribed = (subscription) {
      LoggingService.info('Unsubscribed from topic: ${subscription.topic.rawTopic}');
    };

    _client!.updates.listen(_handleIncomingMessages);
  }

  void _handleIncomingMessages(List<MqttReceivedMessage<MqttMessage?>>? c) {
    if (c == null) return;

    for (final message in c) {
      try {
        final topic = message.topic;
        final recMess = message.payload! as MqttPublishMessage;
        final payload = utf8.decode(recMess.payload.message!);

        // Extract correlation data from user properties if available
        String? messageId;
        if (recMess.variableHeader != null && recMess.variableHeader!.userProperty.isNotEmpty) {
          for (final prop in recMess.variableHeader!.userProperty) {
            if (prop.pairName == 'correlationData') {
              messageId = prop.pairValue;
              break;
            }
          }
        }

        // Handle message deduplication
        if (messageId != null && _isMessageProcessed(messageId)) {
          LoggingService.info('Duplicate message ignored: $messageId');
          continue;
        }

        if (messageId != null) {
          _processedMessages.add(messageId);
        }

        // Parse JSON payload and add to message stream
        try {
          final data = jsonDecode(payload) as Map<String, dynamic>;
          data['mqtt_metadata'] = {
            'topic': topic,
            'qos': recMess.header?.qos.index,
            'retained': recMess.header?.retain,
            'message_id': messageId,
            'timestamp': DateTime.now().toIso8601String(),
          };
          _messageController.add(data);
        } catch (e) {
          // If not JSON, send as raw string
          _messageController.add({
            'raw_payload': payload,
            'mqtt_metadata': {
              'topic': topic,
              'qos': recMess.header?.qos.index,
              'retained': recMess.header?.retain,
              'message_id': messageId,
              'timestamp': DateTime.now().toIso8601String(),
            },
          });
        }

        // Route message to appropriate topic controller
        final controller = _topicControllers[topic];
        if (controller != null) {
          controller.add(payload);
        }

        LoggingService.info('Received message on topic $topic: $payload');
      } catch (e) {
        LoggingService.error('Error handling incoming message: $e');
      }
    }
  }

  bool _isMessageProcessed(String messageId) => _processedMessages.contains(messageId);

  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      LoggingService.error('Max reconnection attempts reached');
      return;
    }

    _reconnectAttempts++;
    LoggingService.info('Scheduling reconnect attempt $_reconnectAttempts in ${_reconnectDelay}ms');

    Timer(const Duration(milliseconds: _reconnectDelay), () async {
      // Refresh JWT before reconnecting
      _password = await _getLatestToken();
      await _establishConnection();
    });
  }

  Future<void> subscribe(String topic, {MqttQos qos = MqttQos.atMostOnce}) async {
    if (!_isConnected || _client == null) {
      LoggingService.error('Cannot subscribe: MQTT not connected');
      return;
    }

    try {
      _client!.subscribe(topic, qos);
      _topicControllers[topic] = StreamController<String>.broadcast();
      LoggingService.info('Subscribed to topic: $topic');
    } catch (e) {
      LoggingService.error('Failed to subscribe to topic $topic: $e');
    }
  }

  Future<void> unsubscribe(String topic) async {
    if (!_isConnected || _client == null) {
      LoggingService.error('Cannot unsubscribe: MQTT not connected');
      return;
    }

    try {
      _client!.unsubscribeStringTopic(topic);
      _topicControllers[topic]?.close();
      _topicControllers.remove(topic);
      LoggingService.info('Unsubscribed from topic: $topic');
    } catch (e) {
      LoggingService.error('Failed to unsubscribe from topic $topic: $e');
    }
  }

  void publish(String topic, String message, {MqttQos qos = MqttQos.atMostOnce, bool retain = false, String? correlationData}) {
    if (!_isConnected || _client == null) {
      LoggingService.error('Cannot publish: MQTT not connected');
      return;
    }

    try {
      final builder = MqttPayloadBuilder();
      builder.addString(message);

      _client!.publishMessage(topic, qos, builder.payload!, retain: retain);
      LoggingService.info('Published message to topic $topic: $message');
    } catch (e) {
      LoggingService.error('Failed to publish message to topic $topic: $e');
    }
  }

  Stream<String>? getTopicStream(String topic) => _topicControllers[topic]?.stream;

  void disconnect() {
    _shouldReconnect = false;
    _client?.disconnect();
    _isConnected = false;
    
    // Close all topic controllers
    for (final controller in _topicControllers.values) {
      controller.close();
    }
    _topicControllers.clear();
    
    // Close message controller
    _messageController.close();
    
    LoggingService.info('MQTT disconnected');
  }

  bool get isConnected => _isConnected;

  void setCurrentUserId(String userId) {
    _currentUserId = userId;
  }

  void _publishOnlineStatus(bool isOnline) {
    final status = isOnline ? 'online' : 'offline';
    final message = jsonEncode({
      'status': status,
      'timestamp': DateTime.now().toIso8601String(),
    });
    publish('users/$_currentUserId/status', message,
        qos: MqttQos.atLeastOnce, retain: true);
  }

  void sendTypingStatus(String chatId, bool isTyping) {
    if (_currentUserId != null) {
      final topic = 'chats/$chatId/typing';
      final payload = jsonEncode({
        'user_id': _currentUserId,
        'is_typing': isTyping,
        'timestamp': DateTime.now().toIso8601String(),
      });
      publish(topic, payload);
    }
  }

  void publishNotification(String userId, String notification) {
    final topic = 'hopen/requests/$userId';
    publish(topic, notification, qos: MqttQos.atLeastOnce);
  }

  void publishToBubble(String bubbleId, Map<String, dynamic> message) {
    final topic = 'bubbles/$bubbleId/messages';
    final payload = jsonEncode(message);
    publish(topic, payload, qos: MqttQos.atLeastOnce);
  }

  // Call-related MQTT methods
  void sendCandidate(String callId, String targetId, Map<String, dynamic> candidate) {
    final topic = 'calls/$callId/candidate';
    final payload = jsonEncode({
      'type': 'candidate',
      'callId': callId,
      'targetId': targetId,
      'candidate': candidate,
      'timestamp': DateTime.now().toIso8601String(),
    });
    publish(topic, payload, qos: MqttQos.atLeastOnce);
  }

  void sendCallOffer(String callId, String targetId, Map<String, dynamic> offer) {
    final topic = 'calls/$callId/offer';
    final payload = jsonEncode({
      'type': 'offer',
      'callId': callId,
      'targetId': targetId,
      'offer': offer,
      'timestamp': DateTime.now().toIso8601String(),
    });
    publish(topic, payload, qos: MqttQos.atLeastOnce);
  }

  void sendJoinRequest(String bubbleId, String userId) {
    final topic = 'bubbles/$bubbleId/join_request';
    final payload = jsonEncode({
      'type': 'join_request',
      'bubbleId': bubbleId,
      'userId': userId,
      'timestamp': DateTime.now().toIso8601String(),
    });
    publish(topic, payload, qos: MqttQos.atLeastOnce);
  }

  void sendAnswer(String callId, String targetId, Map<String, dynamic> answer) {
    final topic = 'calls/$callId/answer';
    final payload = jsonEncode({
      'type': 'answer',
      'callId': callId,
      'targetId': targetId,
      'answer': answer,
      'timestamp': DateTime.now().toIso8601String(),
    });
    publish(topic, payload, qos: MqttQos.atLeastOnce);
  }

  void sendRejectCall(String callId, String targetId) {
    final topic = 'calls/$callId/reject';
    final payload = jsonEncode({
      'type': 'reject',
      'callId': callId,
      'targetId': targetId,
      'timestamp': DateTime.now().toIso8601String(),
    });
    publish(topic, payload, qos: MqttQos.atLeastOnce);
  }

  void sendEndCall(String callId, String targetId) {
    final topic = 'calls/$callId/end';
    final payload = jsonEncode({
      'type': 'end',
      'callId': callId,
      'targetId': targetId,
      'timestamp': DateTime.now().toIso8601String(),
    });
    publish(topic, payload, qos: MqttQos.atLeastOnce);
  }

  void sendCallAnswer(String callId, String targetUserId, Map<String, dynamic> answer) {
    final topic = 'calls/$callId/answer';
    final payload = jsonEncode({
      'target_user_id': targetUserId,
      'answer': answer,
      'timestamp': DateTime.now().toIso8601String(),
    });
    publish(topic, payload, qos: MqttQos.atLeastOnce);
  }

  void sendIceCandidate(String callId, String targetUserId, Map<String, dynamic> candidate) {
    final topic = 'calls/$callId/ice';
    final payload = jsonEncode({
      'target_user_id': targetUserId,
      'candidate': candidate,
      'timestamp': DateTime.now().toIso8601String(),
    });
    publish(topic, payload);
  }

  String getCallTopic(String roomId) => 'calls/$roomId/events';

  String getFriendCallTopic(String friendId) => 'friends/$friendId/calls';

  String getUserTopic(String userId) => 'users/$userId/events';

  String getChatTopic(String chatId) => 'chats/$chatId/messages';

  String getBubbleTopic(String bubbleId) => 'bubbles/$bubbleId/events';

  Future<String?> _getLatestToken() async {
    try {
      final storage = await LocalStorageService.getInstance();
      return await storage.getString('access_token');
    } catch (_) {
      return null;
    }
  }

  bool isPinnedCert(X509Certificate cert) {
    return pin.isPinnedCert(cert);
  }

  // Dispose helper for tests
  void dispose() => disconnect();
} 