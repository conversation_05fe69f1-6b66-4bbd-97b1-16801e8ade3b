/// MQTT-Only Real-Time Service
///
/// This service replaces the polling-based RealTimeNotificationService
/// with a pure MQTT-driven approach that eliminates HTTP polling.

import 'dart:async';
import 'dart:convert';
import 'package:mqtt5_client/mqtt5_client.dart';
import 'package:mqtt5_client/mqtt5_server_client.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';
import '../services/auth/ory_auth_service.dart';
import 'mqtt_event_handler_impl.dart';
import '../../di/injection_container_refactored.dart' as di;

class MqttOnlyRealTimeService {
  MqttOnlyRealTimeService({
    required this.onNotificationReceived,
    required this.onConnectionStatusChanged,
  });

  // Callbacks
  final Function(Map<String, dynamic>)? onNotificationReceived;
  final Function(bool)? onConnectionStatusChanged;

  // MQTT client
  MqttServerClient? _mqttClient;

  // Network connectivity
  final Connectivity _connectivity = Connectivity();
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;

  // State management
  String? _currentUserId;
  bool _isInitialized = false;
  bool _isConnected = false;

  // Event handler
  late MqttEventHandlerImpl _eventHandler;

  // Monitoring and debugging
  int _connectionAttempts = 0;
  DateTime? _lastConnectionAttempt;
  DateTime? _lastSuccessfulConnection;
  DateTime? _lastMessageReceived;
  int _messagesReceived = 0;
  int _messagesProcessed = 0;
  int _messageErrors = 0;
  final List<String> _recentErrors = [];
  static const int _maxRecentErrors = 10;

  // Reconnection logic
  Timer? _reconnectionTimer;
  int _reconnectionAttempts = 0;
  static const int _maxReconnectionAttempts = 5;
  static const Duration _reconnectionDelay = Duration(seconds: 5);

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) {
      LoggingService.warning(
        'MqttOnlyRealTimeService: Service already initialized',
      );
      return;
    }

    LoggingService.info('MqttOnlyRealTimeService: Initializing service...');

    // Initialize event handler
    _eventHandler = MqttEventHandlerImpl(
      contactRequestRepository: di.sl(),
      friendRequestRepository: di.sl(),
    );

    // Monitor network connectivity
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _onConnectivityChanged,
    );

    _isInitialized = true;
    LoggingService.success(
      'MqttOnlyRealTimeService: Service initialized successfully',
    );
  }

  /// Start the service for a specific user
  Future<void> start(String userId) async {
    if (!_isInitialized) {
      await initialize();
    }

    if (_currentUserId == userId && _isConnected) {
      LoggingService.info(
        'MqttOnlyRealTimeService: Already connected for user $userId',
      );
      return;
    }

    _currentUserId = userId;
    LoggingService.info(
      'MqttOnlyRealTimeService: Starting service for user $userId',
    );

    // Connect to MQTT
    await _connectToMqtt();
  }

  /// Stop the service
  Future<void> stop() async {
    LoggingService.info('MqttOnlyRealTimeService: Stopping service');

    await _connectivitySubscription?.cancel();
    _reconnectionTimer?.cancel();

    if (_mqttClient?.connectionStatus?.state == MqttConnectionState.connected) {
      _mqttClient?.disconnect();
    }

    _isConnected = false;
    _currentUserId = null;
    onConnectionStatusChanged?.call(false);

    LoggingService.info('MqttOnlyRealTimeService: Service stopped');
  }

  /// Connect to MQTT broker with Ory Kratos session token authentication
  Future<void> _connectToMqtt() async {
    if (_currentUserId == null) {
      LoggingService.warning(
        'MqttOnlyRealTimeService: No user ID, skipping MQTT connection',
      );
      return;
    }

    try {
      _connectionAttempts++;
      _lastConnectionAttempt = DateTime.now();

      LoggingService.info(
        'MqttOnlyRealTimeService: Connecting to MQTT broker (attempt $_connectionAttempts)',
      );

      // Get authentication token
      final authService = di.sl<OryAuthService>();
      final token = await authService.getValidToken();

      if (token == null) {
        throw Exception('No valid authentication token available');
      }

      // Create MQTT client
      _mqttClient = MqttServerClient.withPort(
        '*********',
        'flutter_client_${_currentUserId}_${DateTime.now().millisecondsSinceEpoch}',
        1883,
      );
      _mqttClient!.logging(on: false);
      _mqttClient!.keepAlivePeriod = 60;
      _mqttClient!.autoReconnect = true;

      // Set authentication
      _mqttClient!.connectionMessage =
          MqttConnectMessage()
              .withClientIdentifier(_mqttClient!.clientIdentifier)
              .authenticateAs(_currentUserId, token)
              .startClean();

      // Set up event handlers
      _mqttClient!.onConnected = _onMqttConnected;
      _mqttClient!.onDisconnected = _onMqttDisconnected;
      _mqttClient!.onSubscribed = _onMqttSubscribed;
      _mqttClient!.onUnsubscribed = _onMqttUnsubscribed;

      // Connect
      await _mqttClient!.connect();
    } catch (e) {
      LoggingService.error(
        'MqttOnlyRealTimeService: Failed to connect to MQTT: $e',
      );
      _addError('Failed to connect to MQTT: $e');
      _scheduleReconnection();
    }
  }

  /// Handle MQTT connection established
  void _onMqttConnected() {
    LoggingService.success('MqttOnlyRealTimeService: Connected to MQTT broker');

    _isConnected = true;
    _lastSuccessfulConnection = DateTime.now();
    _reconnectionAttempts = 0;

    onConnectionStatusChanged?.call(true);

    // Subscribe to topics
    _subscribeToTopics();

    // Listen for messages
    _mqttClient!.updates.listen(_onMqttMessage);
  }

  /// Handle MQTT disconnection
  void _onMqttDisconnected() {
    LoggingService.warning(
      'MqttOnlyRealTimeService: Disconnected from MQTT broker',
    );

    _isConnected = false;
    onConnectionStatusChanged?.call(false);

    // Schedule reconnection
    _scheduleReconnection();
  }

  /// Subscribe to notification topics
  void _subscribeToTopics() {
    if (_currentUserId == null || _mqttClient == null) return;

    final topics = MqttTopics.getAllTopics(_currentUserId!);

    for (final topic in topics) {
      _mqttClient!.subscribe(topic, MqttQos.atLeastOnce);
      LoggingService.info('MqttOnlyRealTimeService: Subscribed to $topic');
    }
  }

  /// Handle incoming MQTT messages
  void _onMqttMessage(List<MqttReceivedMessage<MqttMessage?>> messages) {
    for (final message in messages) {
      try {
        _messagesReceived++;
        _lastMessageReceived = DateTime.now();

        final topic = message.topic;
        final payload = message.payload;

        // 1. Ensure the payload is the correct type (MqttPublishMessage)
        if (payload is MqttPublishMessage) {
          // 2. Safely access the underlying message bytes, which can be null.
          final messageBytes = payload.payload.message;

          // 3. **THE CRITICAL CHECK**: Ensure the message bytes are not null before decoding.
          if (messageBytes != null) {
            // 4. Now it's safe to use bytesToStringAsString, as messageBytes is guaranteed non-null.
            final payloadString = MqttPublishPayload.bytesToStringAsString(
              messageBytes,
            );

            LoggingService.debug(
              'MqttOnlyRealTimeService: Received message on $topic: $payloadString',
            );

            // This call is now safe. payloadString is a non-nullable String.
            _processMessage(topic!, payloadString);
            _messagesProcessed++;
          } else {
            LoggingService.warning(
              'MqttOnlyRealTimeService: Received message on $topic with null payload content.',
            );
          }
        } else {
          LoggingService.warning(
            'MqttOnlyRealTimeService: Received non-publish message on $topic',
          );
        }
      } catch (e) {
        _messageErrors++;
        LoggingService.error(
          'MqttOnlyRealTimeService: Error processing message: $e',
        );
        _addError('Error processing message: $e');
      }
    }
  }

  /// Process MQTT message and route to appropriate handler
  Future<void> _processMessage(String topic, String? payload) async {
    if (payload == null || payload.isEmpty) return;
    try {
      // Parse JSON payload
      final data = jsonDecode(payload) as Map<String, dynamic>;

      // Route to event handler
      await _eventHandler.handleMqttMessage(topic, payload);

      // Also call legacy callback for backward compatibility
      onNotificationReceived?.call(data);
    } catch (e) {
      LoggingService.error(
        'MqttOnlyRealTimeService: Failed to process message: $e',
      );
      _addError('Failed to process message: $e');
    }
  }

  /// Handle network connectivity changes
  void _onConnectivityChanged(ConnectivityResult result) {
    LoggingService.info(
      'MqttOnlyRealTimeService: Network connectivity changed to $result',
    );

    if (result != ConnectivityResult.none &&
        !_isConnected &&
        _currentUserId != null) {
      // Network is back, try to reconnect
      _connectToMqtt();
    }
  }

  /// Schedule reconnection attempt
  void _scheduleReconnection() {
    if (_reconnectionTimer?.isActive == true) return;

    if (_reconnectionAttempts < _maxReconnectionAttempts) {
      _reconnectionTimer = Timer(_reconnectionDelay, () {
        _reconnectionAttempts++;
        _connectToMqtt();
      });

      LoggingService.info(
        'MqttOnlyRealTimeService: Scheduled reconnection attempt ${_reconnectionAttempts + 1}',
      );
    } else {
      LoggingService.warning(
        'MqttOnlyRealTimeService: Max reconnection attempts reached',
      );
    }
  }

  /// Add error to recent errors list
  void _addError(String error) {
    _recentErrors.add('${DateTime.now().toIso8601String()}: $error');
    if (_recentErrors.length > _maxRecentErrors) {
      _recentErrors.removeAt(0);
    }
  }

  /// MQTT event handlers
  void _onMqttSubscribed(MqttSubscription subscription) {
    LoggingService.debug(
      'MqttOnlyRealTimeService: Subscribed to ${subscription.topic}',
    );
  }

  void _onMqttUnsubscribed(MqttSubscription subscription) {
    LoggingService.debug(
      'MqttOnlyRealTimeService: Unsubscribed from ${subscription.topic}',
    );
  }

  /// Get service status for debugging
  String getStatus() {
    final buffer = StringBuffer();
    buffer.writeln('MQTT-Only Real-Time Service Status:');
    buffer.writeln('  Initialized: $_isInitialized');
    buffer.writeln('  Connected: $_isConnected');
    buffer.writeln('  Current User: $_currentUserId');
    buffer.writeln('  Connection Attempts: $_connectionAttempts');
    buffer.writeln('  Last Connection Attempt: $_lastConnectionAttempt');
    buffer.writeln('  Last Successful Connection: $_lastSuccessfulConnection');
    buffer.writeln('  Messages Received: $_messagesReceived');
    buffer.writeln('  Messages Processed: $_messagesProcessed');
    buffer.writeln('  Message Errors: $_messageErrors');
    buffer.writeln('  Last Message Received: $_lastMessageReceived');

    if (_recentErrors.isNotEmpty) {
      buffer.writeln('\nRecent Errors:');
      for (final error in _recentErrors) {
        buffer.writeln('  $error');
      }
    }

    return buffer.toString();
  }
}
