import '../../../repositories/local_storage/abstract_profile_picture_repository.dart';
import '../../../statefulbusinesslogic/core/services/profile_picture_service.dart';

/// Implementation of the profile picture repository in the Provider layer
/// 
/// This class implements the AbstractProfilePictureRepository interface
/// and delegates to the EnhancedProfilePictureService for actual operations.
/// 
/// This follows the Clean Architecture pattern where:
/// - Repository interface is in the Repository layer
/// - Repository implementation is in the Provider layer
/// - Service implementation is in the Statefulbusinesslogic layer
class ProfilePictureRepositoryImpl implements AbstractProfilePictureRepository {
  final ProfilePictureService _profilePictureService;
  
  ProfilePictureRepositoryImpl({
    required ProfilePictureService profilePictureService,
  }) : _profilePictureService = profilePictureService;

  @override
  Future<String?> getProfilePicturePath(String userId, String imageUrl) async {
    return await _profilePictureService.getProfilePicturePath(userId, imageUrl);
  }

  @override
  Future<void> clearOldCache(Duration maxAge) async {
    await _profilePictureService.clearOldCache(maxAge);
  }

  @override
  Future<Map<String, dynamic>> getCacheStats() async {
    return await _profilePictureService.getCacheStats();
  }

  @override
  Future<void> clearAllCache() async {
    await _profilePictureService.clearAllCache();
  }

  @override
  Future<void> updateLastAccessed(String userId) async {
    await _profilePictureService.updateLastAccessed(userId);
  }

  @override
  Future<bool> isProfilePictureCached(String userId) async {
    return await _profilePictureService.isProfilePictureCached(userId);
  }
} 