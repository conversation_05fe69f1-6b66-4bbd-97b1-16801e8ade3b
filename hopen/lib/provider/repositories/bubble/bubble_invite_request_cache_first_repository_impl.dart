/// Cache-first implementation of BubbleInviteRequestRepository
///
/// This implementation prioritizes local cache and only fetches from remote
/// when triggered by MQTT events or initial load.

import 'dart:async';

import '../../../repositories/bubble/bubble_invite_request_repository.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../../statefulbusinesslogic/core/error/exceptions.dart';
import '../../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';
import '../../../statefulbusinesslogic/core/db/app_database.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../datasources/http_remote_datasource.dart';
import '../../services/api/http_api_service.dart';
import '../base/cache_first_repository_base.dart';

class BubbleInviteRequestCacheFirstRepositoryImpl
    extends CacheFirstRepositoryBase
    implements BubbleInviteRequestRepository {
  BubbleInviteRequestCacheFirstRepositoryImpl({
    required HttpApiService apiService,
    required HttpRemoteDataSource remoteDataSource,
    required BubbleInviteRequestDao bubbleInviteRequestDao,
    super.debugMode = false,
  }) : _apiService = apiService,
       _remoteDataSource = remoteDataSource,
       _bubbleInviteRequestDao = bubbleInviteRequestDao;

  final HttpApiService _apiService;
  final HttpRemoteDataSource _remoteDataSource;
  final BubbleInviteRequestDao _bubbleInviteRequestDao;

  String? _currentUserId;

  /// Convert Drift BubbleInviteRequest to domain BubbleInviteRequest
  BubbleInviteRequest _convertFromDrift(DriftBubbleInviteRequest driftRequest) {
    return BubbleInviteRequest(
      id: driftRequest.id,
      bubbleId: driftRequest.bubbleId,
      bubbleName: driftRequest.bubbleName,
      inviterId: driftRequest.inviterId,
      inviterName: driftRequest.inviterName,
      inviteeId: driftRequest.inviteeId,
      inviteeName: driftRequest.inviteeName,
      invitedAt: driftRequest.invitedAt,
      status: InviteRequestStatus.values.firstWhere(
        (e) => e.name == driftRequest.status,
        orElse: () => InviteRequestStatus.pending,
      ),
      message: driftRequest.message,
      respondedAt: driftRequest.respondedAt,
      expiresAt: driftRequest.expiresAt,
    );
  }

  /// Convert domain BubbleInviteRequest to Drift BubbleInviteRequest
  DriftBubbleInviteRequest _convertToDrift(BubbleInviteRequest request) {
    return DriftBubbleInviteRequest(
      id: request.id,
      bubbleId: request.bubbleId,
      bubbleName: request.bubbleName,
      inviterId: request.inviterId,
      inviterName: request.inviterName,
      inviteeId: request.inviteeId,
      inviteeName: request.inviteeName,
      invitedAt: request.invitedAt,
      status: request.status.name,
      message: request.message,
      respondedAt: request.respondedAt,
      expiresAt: request.expiresAt,
      cachedAt: DateTime.now(),
      isStale: false,
    );
  }

  /// Set current user ID for cache operations
  void setCurrentUserId(String userId) {
    _currentUserId = userId;
  }

  @override
  Stream<List<BubbleInviteRequest>> get pendingInviteRequestsStream {
    if (_currentUserId == null) return Stream.value([]);
    return _bubbleInviteRequestDao
        .watchPendingInviteRequests(_currentUserId!)
        .map((driftRequests) => driftRequests.map(_convertFromDrift).toList());
  }

  @override
  Future<bool> get hasCachedData async {
    if (_currentUserId == null) return false;
    final requests = await _bubbleInviteRequestDao.getInviteRequestsForUser(
      _currentUserId!,
    );
    return requests.isNotEmpty;
  }

  @override
  Future<void> initializeCache() async {
    if (_currentUserId == null) {
      LoggingService.warning(
        'BubbleInviteRequestRepository: Cannot initialize cache without user ID',
      );
      return;
    }
    LoggingService.info(
      'BubbleInviteRequestRepository: Cache initialized for user $_currentUserId',
    );
  }

  @override
  Future<void> fetchFromRemote() async {
    if (_currentUserId == null) {
      LoggingService.warning(
        'BubbleInviteRequestRepository: Cannot fetch from remote without user ID',
      );
      return;
    }

    try {
      LoggingService.info(
        'BubbleInviteRequestRepository: Fetching from remote for user $_currentUserId',
      );

      // Fetch pending invite requests
      final response = await _remoteDataSource.get(
        '/api/invite-requests/user/$_currentUserId',
      );
      final requests = <BubbleInviteRequest>[];
      for (final requestData in response['requests'] ?? []) {
        requests.add(
          BubbleInviteRequest(
            id: requestData['id'] as String,
            bubbleId: requestData['bubbleId'] as String,
            bubbleName: requestData['bubbleName'] as String,
            inviterId: requestData['inviterId'] as String,
            inviterName: requestData['inviterName'] as String,
            inviteeId: requestData['inviteeId'],
            inviteeName: requestData['inviteeName'],
            invitedAt: DateTime.parse(requestData['invitedAt'] as String),
            status: InviteRequestStatus.values.firstWhere(
              (e) => e.name == requestData['status'],
              orElse: () => InviteRequestStatus.pending,
            ),
            message: requestData['message'] as String?,
            respondedAt:
                requestData['respondedAt'] != null
                    ? DateTime.parse(requestData['respondedAt'] as String)
                    : null,
            expiresAt:
                requestData['expiresAt'] != null
                    ? DateTime.parse(requestData['expiresAt'] as String)
                    : null,
          ),
        );
      }

      // Convert and store in Drift database
      for (final request in requests) {
        final driftRequest = _convertToDrift(request);
        await _bubbleInviteRequestDao.insertOrUpdateInviteRequest(driftRequest);
      }

      LoggingService.info(
        'BubbleInviteRequestRepository: Cached ${requests.length} pending invite requests',
      );
    } catch (e) {
      LoggingService.error(
        'BubbleInviteRequestRepository: Failed to fetch from remote: $e',
      );
      rethrow;
    }
  }

  @override
  Future<void> clearCache() async {
    if (_currentUserId == null) return;

    await _bubbleInviteRequestDao.clearInviteRequestsForUser(_currentUserId!);
    LoggingService.info(
      'BubbleInviteRequestRepository: Cache cleared for user $_currentUserId',
    );
  }

  @override
  Future<void> handleMqttEvent(MqttEvent event) async {
    if (event is BubbleEvent) {
      await processBubbleEvent(event);
    }
  }

  /// Process bubble invite request MQTT events
  Future<void> processBubbleEvent(BubbleEvent event) async {
    LoggingService.info(
      'BubbleInviteRequestRepository: Processing event: ${event.action} for bubble ${event.bubbleId}',
    );

    switch (event.action) {
      case BubbleAction.invite_received:
        // New invite received - refresh from remote to get full details
        await fetchFromRemote();
        break;
      case BubbleAction.joined:
      case BubbleAction.left:
        // Update request status in database
        await _bubbleInviteRequestDao.updateInviteRequestStatus(
          event.bubbleId,
          event.action.name,
        );
        break;
      case BubbleAction.expired:
        // Remove expired request from database
        await _bubbleInviteRequestDao.deleteInviteRequestForBubble(
          event.bubbleId,
        );
        break;
      default:
        // Handle other bubble actions if needed
        break;
    }
  }

  @override
  Future<Result<void>> sendInviteRequest(
    String bubbleId,
    String inviteeId,
    String? message,
  ) async {
    try {
      await _remoteDataSource.post('/api/v1/bubbles/$bubbleId/invite', {
        'recipient_id': inviteeId,
        'message': message ?? '',
      });
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to send invite request: $e'),
      );
    }
  }

  @override
  Future<Result<List<BubbleInviteRequest>>> getUserInviteRequests(
    String userId,
  ) async {
    try {
      final driftRequests = await _bubbleInviteRequestDao
          .getInviteRequestsForUser(userId);
      final requests = driftRequests.map(_convertFromDrift).toList();
      return Result.success(requests);
    } catch (e) {
      LoggingService.error(
        'BubbleInviteRequestRepository: Failed to get user invite requests: $e',
      );
      return Result.failure(
        NetworkError(message: 'Failed to get user invite requests: $e'),
      );
    }
  }

  @override
  Future<Result<void>> respondToInviteRequest(
    String requestId,
    InviteRequestStatus response,
  ) async {
    try {
      await _remoteDataSource.put('/api/invite-requests/$requestId/respond', {
        'response': response.name,
      });

      // Update request status in Drift database
      await _bubbleInviteRequestDao.updateInviteRequestStatusById(
        requestId,
        response.name,
      );

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to respond to invite request: $e'),
      );
    }
  }

  @override
  Future<Result<BubbleInviteRequest>> getBubbleDetails(String bubbleId) async {
    try {
      // Check Drift database first
      final driftRequest = await _bubbleInviteRequestDao
          .getInviteRequestByBubbleId(bubbleId);
      if (driftRequest != null) {
        return Result.success(_convertFromDrift(driftRequest));
      }

      // Fetch from remote if not in cache
      final response = await _remoteDataSource.get(
        '/api/bubbles/$bubbleId/details',
      );
      final request = BubbleInviteRequest(
        id: response['id'] as String,
        bubbleId: response['bubbleId'] as String,
        bubbleName: response['bubbleName'] as String,
        inviterId: response['inviterId'] as String,
        inviterName: response['inviterName'] as String,
        inviteeId: response['inviteeId'],
        inviteeName: response['inviteeName'],
        invitedAt: DateTime.parse(response['invitedAt'] as String),
        status: InviteRequestStatus.values.firstWhere(
          (e) => e.name == response['status'],
          orElse: () => InviteRequestStatus.pending,
        ),
        message: response['message'] as String?,
        respondedAt:
            response['respondedAt'] != null
                ? DateTime.parse(response['respondedAt'] as String)
                : null,
        expiresAt:
            response['expiresAt'] != null
                ? DateTime.parse(response['expiresAt'] as String)
                : null,
      );

      // Store in Drift database
      final driftRequest2 = _convertToDrift(request);
      await _bubbleInviteRequestDao.insertOrUpdateInviteRequest(driftRequest2);

      return Result.success(request);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get bubble details: $e'),
      );
    }
  }

  @override
  Future<Result<BubbleInviteRequest>> sendInvite(
    String bubbleId,
    String userId,
    String message,
  ) async {
    try {
      final response = await _remoteDataSource.post(
        '/api/bubbles/$bubbleId/invite',
        {'userId': userId, 'message': message},
      );

      final request = BubbleInviteRequest(
        id: response['id'] as String,
        bubbleId: bubbleId,
        bubbleName: response['bubbleName'] as String,
        inviterId: response['inviterId'] as String,
        inviterName: response['inviterName'] as String,
        inviteeId: userId,
        inviteeName: response['inviteeName'] as String,
        invitedAt: DateTime.now(),
        status: InviteRequestStatus.pending,
        message: message,
        respondedAt: null,
        expiresAt:
            response['expiresAt'] != null
                ? DateTime.parse(response['expiresAt'] as String)
                : null,
      );

      // Store in Drift database
      final driftRequest = _convertToDrift(request);
      await _bubbleInviteRequestDao.insertOrUpdateInviteRequest(driftRequest);

      return Result.success(request);
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to send invite: $e'));
    }
  }

  // Missing interface methods - implementing with basic functionality
  @override
  Future<Result<List<BubbleInviteRequest>>> getPendingInvites(
    String userId,
  ) async {
    try {
      final driftRequests = await _bubbleInviteRequestDao
          .getPendingInviteRequests(userId);
      final requests = driftRequests.map(_convertFromDrift).toList();
      return Result.success(requests);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get pending invites: $e'),
      );
    }
  }

  @override
  Future<Result<List<BubbleInviteRequest>>> getSentInvites(
    String userId,
  ) async {
    try {
      final driftRequests = await _bubbleInviteRequestDao
          .getInviteRequestsForUser(userId);
      final sentRequests =
          driftRequests.where((r) => r.inviterId == userId).toList();
      final requests = sentRequests.map(_convertFromDrift).toList();
      return Result.success(requests);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get sent invites: $e'),
      );
    }
  }

  @override
  Future<Result<List<BubbleInviteRequest>>> getBubbleInvites(
    String bubbleId,
  ) async {
    try {
      final driftRequests = await _bubbleInviteRequestDao
          .getInviteRequestsForUser(''); // This needs proper implementation
      final bubbleRequests =
          driftRequests.where((r) => r.bubbleId == bubbleId).toList();
      final requests = bubbleRequests.map(_convertFromDrift).toList();
      return Result.success(requests);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get bubble invites: $e'),
      );
    }
  }

  @override
  Future<Result<void>> acceptInvite(String inviteId) async {
    try {
      await _remoteDataSource.post('/api/invite-requests/$inviteId/accept', {});
      await _bubbleInviteRequestDao.updateInviteRequestStatusById(
        inviteId,
        'accepted',
      );
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to accept invite: $e'),
      );
    }
  }

  @override
  Future<Result<void>> declineInvite(String inviteId) async {
    try {
      await _remoteDataSource.post(
        '/api/invite-requests/$inviteId/decline',
        {},
      );
      await _bubbleInviteRequestDao.updateInviteRequestStatusById(
        inviteId,
        'declined',
      );
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to decline invite: $e'),
      );
    }
  }

  @override
  Future<Result<void>> cancelInvite(String inviteId) async {
    try {
      await _remoteDataSource.delete('/api/invite-requests/$inviteId');
      await _bubbleInviteRequestDao.updateInviteRequestStatusById(
        inviteId,
        'cancelled',
      );
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to cancel invite: $e'),
      );
    }
  }

  @override
  Future<Result<BubbleInviteRequest>> getInvite(String inviteId) async {
    try {
      final driftRequest = await _bubbleInviteRequestDao
          .getInviteRequestByBubbleId(inviteId);
      if (driftRequest != null) {
        return Result.success(_convertFromDrift(driftRequest));
      }
      return Result.failure(NetworkError(message: 'Invite not found'));
    } catch (e) {
      return Result.failure(NetworkError(message: 'Failed to get invite: $e'));
    }
  }

  @override
  Future<Result<bool>> hasPendingInvite(String bubbleId, String userId) async {
    try {
      final driftRequests = await _bubbleInviteRequestDao
          .getPendingInviteRequests(userId);
      final hasInvite = driftRequests.any((r) => r.bubbleId == bubbleId);
      return Result.success(hasInvite);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to check pending invite: $e'),
      );
    }
  }

  @override
  Future<Result<BubbleInviteRequest>> resendInvite(String inviteId) async {
    // For now, just return the existing invite
    return getInvite(inviteId);
  }

  @override
  Future<Result<List<BubbleInviteRequest>>> getInviteHistory(
    String bubbleId,
  ) async {
    return getBubbleInvites(bubbleId);
  }

  @override
  Future<Result<void>> expireOldInvites() async {
    // This would need proper implementation based on business logic
    return Result.success(null);
  }

  @override
  Future<Result<Map<String, int>>> getInviteStatistics(String bubbleId) async {
    try {
      final invites = await getBubbleInvites(bubbleId);
      if (invites.isFailure) {
        return Result.failure(invites.error);
      }

      final stats = <String, int>{
        'total': invites.data.length,
        'pending':
            invites.data
                .where((i) => i.status == InviteRequestStatus.pending)
                .length,
        'accepted':
            invites.data
                .where((i) => i.status == InviteRequestStatus.accepted)
                .length,
        'declined':
            invites.data
                .where((i) => i.status == InviteRequestStatus.declined)
                .length,
      };

      return Result.success(stats);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get invite statistics: $e'),
      );
    }
  }

  @override
  Future<Result<void>> acceptInviteRequest(String requestId) async {
    return acceptInvite(requestId);
  }

  @override
  Future<Result<void>> declineInviteRequest(String requestId) async {
    return declineInvite(requestId);
  }
}
