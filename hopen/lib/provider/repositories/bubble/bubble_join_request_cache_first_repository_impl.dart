/// Cache-first implementation of BubbleJoinRequestRepository
///
/// This implementation prioritizes local cache and only fetches from remote
/// when triggered by MQTT events or initial load.

import 'dart:async';

import '../../../repositories/bubble/bubble_join_request_repository.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../../statefulbusinesslogic/core/error/exceptions.dart';
import '../../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';
import '../../../statefulbusinesslogic/core/db/app_database.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../datasources/http_remote_datasource.dart';
import '../../services/api/http_api_service.dart';
import '../base/cache_first_repository_base.dart';

class BubbleJoinRequestCacheFirstRepositoryImpl extends CacheFirstRepositoryBase
    implements BubbleJoinRequestRepository {
  BubbleJoinRequestCacheFirstRepositoryImpl({
    required HttpApiService apiService,
    required HttpRemoteDataSource remoteDataSource,
    required BubbleJoinRequestDao bubbleJoinRequestDao,
    super.debugMode = false,
  }) : _apiService = apiService,
       _remoteDataSource = remoteDataSource,
       _bubbleJoinRequestDao = bubbleJoinRequestDao;

  final HttpApiService _apiService;
  final HttpRemoteDataSource _remoteDataSource;
  final BubbleJoinRequestDao _bubbleJoinRequestDao;

  String? _currentUserId;

  /// Convert Drift BubbleJoinRequest to domain BubbleJoinRequest
  BubbleJoinRequest _convertFromDrift(DriftBubbleJoinRequest driftRequest) {
    return BubbleJoinRequest(
      id: driftRequest.id,
      bubbleId: driftRequest.bubbleId,
      userId: driftRequest.userId,
      userName: driftRequest.userName,
      userAvatarUrl: driftRequest.userAvatarUrl,
      requestedAt: driftRequest.requestedAt,
      status: JoinRequestStatus.values.firstWhere(
        (e) => e.name == driftRequest.status,
        orElse: () => JoinRequestStatus.pending,
      ),
      message: driftRequest.message,
      respondedAt: driftRequest.respondedAt,
      respondedBy: driftRequest.respondedBy,
    );
  }

  /// Convert domain BubbleJoinRequest to Drift BubbleJoinRequest
  DriftBubbleJoinRequest _convertToDrift(BubbleJoinRequest request) {
    return DriftBubbleJoinRequest(
      id: request.id,
      bubbleId: request.bubbleId,
      userId: request.userId,
      userName: request.userName,
      userAvatarUrl: request.userAvatarUrl,
      requestedAt: request.requestedAt,
      status: request.status.name,
      message: request.message,
      respondedAt: request.respondedAt,
      respondedBy: request.respondedBy,
      cachedAt: DateTime.now(),
      isStale: false,
    );
  }

  /// Set current user ID for cache operations
  void setCurrentUserId(String userId) {
    _currentUserId = userId;
  }

  @override
  Stream<List<BubbleJoinRequest>> get pendingJoinRequestsStream {
    if (_currentUserId == null) return Stream.value([]);
    return _bubbleJoinRequestDao
        .watchPendingJoinRequests(_currentUserId!)
        .map((driftRequests) => driftRequests.map(_convertFromDrift).toList());
  }

  @override
  Future<bool> get hasCachedData async {
    if (_currentUserId == null) return false;
    final requests = await _bubbleJoinRequestDao.getJoinRequestsForUser(
      _currentUserId!,
    );
    return requests.isNotEmpty;
  }

  @override
  Future<void> initializeCache() async {
    if (_currentUserId == null) {
      LoggingService.warning(
        'BubbleJoinRequestRepository: Cannot initialize cache without user ID',
      );
      return;
    }
    LoggingService.info(
      'BubbleJoinRequestRepository: Cache initialized for user $_currentUserId',
    );
  }

  @override
  Future<void> fetchFromRemote() async {
    if (_currentUserId == null) {
      LoggingService.warning(
        'BubbleJoinRequestRepository: Cannot fetch from remote without user ID',
      );
      return;
    }

    try {
      LoggingService.info(
        'BubbleJoinRequestRepository: Fetching from remote for user $_currentUserId',
      );

      // Fetch pending join requests for user's bubbles
      final response = await _remoteDataSource.get(
        '/api/join-requests/user/$_currentUserId',
      );
      final requests = <BubbleJoinRequest>[];
      for (final requestData in response['requests'] ?? []) {
        requests.add(
          BubbleJoinRequest(
            id: requestData['id'] as String,
            bubbleId: requestData['bubbleId'] as String,
            userId: requestData['userId'],
            userName: requestData['userName'],
            userAvatarUrl: requestData['userAvatarUrl'],
            requestedAt: DateTime.parse(requestData['requestedAt'] as String),
            status: JoinRequestStatus.values.firstWhere(
              (e) => e.name == requestData['status'],
              orElse: () => JoinRequestStatus.pending,
            ),
            message: requestData['message'] as String?,
            respondedAt:
                requestData['respondedAt'] != null
                    ? DateTime.parse(requestData['respondedAt'] as String)
                    : null,
            respondedBy: requestData['respondedBy'],
          ),
        );
      }

      // Convert and store in Drift database
      for (final request in requests) {
        final driftRequest = _convertToDrift(request);
        await _bubbleJoinRequestDao.insertOrUpdateJoinRequest(driftRequest);
      }

      LoggingService.info(
        'BubbleJoinRequestRepository: Cached ${requests.length} pending join requests',
      );
    } catch (e) {
      LoggingService.error(
        'BubbleJoinRequestRepository: Failed to fetch from remote: $e',
      );
      rethrow;
    }
  }

  @override
  Future<void> clearCache() async {
    if (_currentUserId == null) return;

    await _bubbleJoinRequestDao.clearJoinRequestsForUser(_currentUserId!);
    LoggingService.info(
      'BubbleJoinRequestRepository: Cache cleared for user $_currentUserId',
    );
  }

  @override
  Future<void> handleMqttEvent(MqttEvent event) async {
    if (event is BubbleEvent) {
      await processBubbleEvent(event);
    }
  }

  /// Process bubble join request MQTT events
  Future<void> processBubbleEvent(BubbleEvent event) async {
    LoggingService.info(
      'BubbleJoinRequestRepository: Processing event: ${event.action} for bubble ${event.bubbleId}',
    );

    switch (event.action) {
      case BubbleAction.created:
        // New join request created - refresh from remote to get full details
        await fetchFromRemote();
        break;
      case BubbleAction.joined:
      case BubbleAction.left:
        // Update request status in database
        await _bubbleJoinRequestDao.updateJoinRequestStatus(
          event.bubbleId,
          event.action.name,
        );
        break;
      case BubbleAction.expired:
        // Remove expired request from database
        await _bubbleJoinRequestDao.deleteJoinRequestForBubble(event.bubbleId);
        break;
      default:
        // Handle other bubble actions if needed
        break;
    }
  }

  @override
  Future<Result<BubbleJoinRequest>> sendJoinRequest(
    String bubbleId,
    String message,
  ) async {
    try {
      final response = await _remoteDataSource.post(
        '/api/v1/bubbles/$bubbleId/join',
        {'message': message},
      );

      // Create a minimal request object since backend doesn't return full details
      final request = BubbleJoinRequest(
        id: response['request_id'] as String,
        bubbleId: bubbleId,
        userId: _currentUserId ?? '',
        userName: '',
        userAvatarUrl: null,
        requestedAt: DateTime.now(),
        status: JoinRequestStatus.pending,
        message: message,
        respondedAt: null,
        respondedBy: null,
      );

      // Store in Drift database
      final driftRequest = _convertToDrift(request);
      await _bubbleJoinRequestDao.insertOrUpdateJoinRequest(driftRequest);

      return Result.success(request);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to send join request: $e'),
      );
    }
  }

  @override
  Future<Result<List<BubbleJoinRequest>>> getPendingJoinRequests(
    String bubbleId,
  ) async {
    try {
      final driftRequests = await _bubbleJoinRequestDao
          .getPendingJoinRequestsForBubble(bubbleId);
      final requests = driftRequests.map(_convertFromDrift).toList();
      return Result.success(requests);
    } catch (e) {
      LoggingService.error(
        'BubbleJoinRequestRepository: Failed to get pending join requests: $e',
      );
      return Result.failure(
        NetworkError(message: 'Failed to get pending join requests: $e'),
      );
    }
  }

  @override
  Future<Result<void>> respondToJoinRequest(
    String requestId,
    JoinRequestStatus response,
  ) async {
    try {
      await _remoteDataSource.put('/api/join-requests/$requestId/respond', {
        'response': response.name,
      });

      // Update request status in Drift database
      await _bubbleJoinRequestDao.updateJoinRequestStatusById(
        requestId,
        response.name,
      );

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to respond to join request: $e'),
      );
    }
  }

  @override
  Future<Result<BubbleJoinRequest>> getBubbleDetails(String bubbleId) async {
    try {
      // Check Drift database first
      final driftRequest = await _bubbleJoinRequestDao.getJoinRequestByBubbleId(
        bubbleId,
      );
      if (driftRequest != null) {
        return Result.success(_convertFromDrift(driftRequest));
      }

      // Fetch from remote if not in cache
      final response = await _remoteDataSource.get(
        '/bubbles/$bubbleId/details',
      );
      final request = BubbleJoinRequest(
        id: response['id'] as String,
        bubbleId: response['bubbleId'] as String,
        userId: response['userId'],
        userName: response['userName'],
        userAvatarUrl: response['userAvatarUrl'],
        requestedAt: DateTime.parse(response['requestedAt'] as String),
        status: JoinRequestStatus.values.firstWhere(
          (e) => e.name == response['status'],
          orElse: () => JoinRequestStatus.pending,
        ),
        message: response['message'] as String?,
        respondedAt:
            response['respondedAt'] != null
                ? DateTime.parse(response['respondedAt'] as String)
                : null,
        respondedBy: response['respondedBy'],
      );

      // Store in Drift database
      final driftRequest2 = _convertToDrift(request);
      await _bubbleJoinRequestDao.insertOrUpdateJoinRequest(driftRequest2);

      return Result.success(request);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get bubble details: $e'),
      );
    }
  }

  // Missing interface methods - implementing with basic functionality
  @override
  Future<Result<List<BubbleJoinRequest>>> getUserJoinRequests(
    String userId,
  ) async {
    try {
      final driftRequests = await _bubbleJoinRequestDao.getJoinRequestsForUser(
        userId,
      );
      final requests = driftRequests.map(_convertFromDrift).toList();
      return Result.success(requests);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get user join requests: $e'),
      );
    }
  }

  @override
  Future<Result<void>> approveJoinRequest(String requestId) async {
    try {
      await _remoteDataSource.post('/api/join-requests/$requestId/approve', {});
      await _bubbleJoinRequestDao.updateJoinRequestStatusById(
        requestId,
        'approved',
      );
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to approve join request: $e'),
      );
    }
  }

  @override
  Future<Result<void>> rejectJoinRequest(
    String requestId,
    String reason,
  ) async {
    try {
      await _remoteDataSource.post('/api/join-requests/$requestId/reject', {
        'reason': reason,
      });
      await _bubbleJoinRequestDao.updateJoinRequestStatusById(
        requestId,
        'rejected',
      );
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to reject join request: $e'),
      );
    }
  }

  @override
  Future<Result<void>> cancelJoinRequest(String requestId) async {
    try {
      await _remoteDataSource.delete('/api/join-requests/$requestId');
      await _bubbleJoinRequestDao.updateJoinRequestStatusById(
        requestId,
        'cancelled',
      );
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to cancel join request: $e'),
      );
    }
  }

  @override
  Future<Result<BubbleJoinRequest>> getJoinRequest(String requestId) async {
    try {
      final driftRequest = await _bubbleJoinRequestDao.getJoinRequestByBubbleId(
        requestId,
      );
      if (driftRequest != null) {
        return Result.success(_convertFromDrift(driftRequest));
      }
      return Result.failure(NetworkError(message: 'Join request not found'));
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get join request: $e'),
      );
    }
  }

  @override
  Future<Result<bool>> hasPendingJoinRequest(
    String bubbleId,
    String userId,
  ) async {
    try {
      final driftRequests = await _bubbleJoinRequestDao.getJoinRequestsForUser(
        userId,
      );
      final hasRequest = driftRequests.any(
        (r) => r.bubbleId == bubbleId && r.status == 'pending',
      );
      return Result.success(hasRequest);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to check pending join request: $e'),
      );
    }
  }

  @override
  Future<Result<List<BubbleJoinRequest>>> getJoinRequestHistory(
    String bubbleId,
  ) async {
    try {
      final driftRequests = await _bubbleJoinRequestDao.getJoinRequestsForUser(
        '',
      ); // This needs proper implementation
      final bubbleRequests =
          driftRequests.where((r) => r.bubbleId == bubbleId).toList();
      final requests = bubbleRequests.map(_convertFromDrift).toList();
      return Result.success(requests);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get join request history: $e'),
      );
    }
  }

  @override
  Future<Result<void>> expireOldJoinRequests() async {
    // This would need proper implementation based on business logic
    return Result.success(null);
  }

  @override
  Future<Result<void>> acceptJoinRequest(
    String bubbleId,
    String requestId,
  ) async {
    return approveJoinRequest(requestId);
  }

  @override
  Future<Result<void>> declineJoinRequest(
    String bubbleId,
    String requestId,
  ) async {
    return rejectJoinRequest(requestId, 'Declined');
  }
}
