import '../../../repositories/bubble/bubble_kickout_repository.dart';
import '../../../statefulbusinesslogic/core/error/exceptions.dart';
import '../../../statefulbusinesslogic/core/models/user_model.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../../statefulbusinesslogic/core/error/app_error.dart';
import '../../datasources/http_remote_datasource.dart';
import '../../services/mqtt/mqtt_service.dart';

class BubbleKickoutRepositoryImpl implements BubbleKickoutRepository {
  BubbleKickoutRepositoryImpl({
    required HttpRemoteDataSource remoteDataSource,
    required MqttService mqttService,
  }) : _remoteDataSource = remoteDataSource,
       _mqttService = mqttService;
  final HttpRemoteDataSource _remoteDataSource;
  final MqttService _mqttService;

  @override
  Future<Result<void>> kickoutMember({
    required String bubbleId,
    required String memberId,
    String? reason,
  }) async {
    try {
      // Create kickout request using the backend endpoint
      await _remoteDataSource.post('/api/v1/bubbles/$bubbleId/kickout', {
        'target_user_id': memberId,
        if (reason != null) 'message': reason,
      });

      // Also publish MQTT event for real-time notification
      _mqttService.publish('bubble/$bubbleId/kickout', {
        'type': 'member_kickout',
        'bubbleId': bubbleId,
        'memberId': memberId,
        'reason': reason,
        'timestamp': DateTime.now().toIso8601String(),
      });

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to kickout member: $e'),
      );
    }
  }

  @override
  Future<Result<List<BubbleKickoutRecord>>> getKickoutHistory(
    String bubbleId,
  ) async {
    try {
      final response = await _remoteDataSource.get(
        '/api/v1/bubbles/$bubbleId/kickouts',
      );
      final List<dynamic> kickouts = response['kickouts'] ?? [];
      final records =
          kickouts
              .map(
                (k) => BubbleKickoutRecord.fromJson(k as Map<String, dynamic>),
              )
              .toList();
      return Result.success(records);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get kickout history: $e'),
      );
    }
  }

  @override
  Future<Result<List<BubbleKickoutRecord>>> getUserKickoutHistory(
    String userId,
  ) async {
    try {
      final response = await _remoteDataSource.get(
        '/api/v1/users/$userId/kickouts',
      );
      final List<dynamic> kickouts = response['kickouts'] ?? [];
      final records =
          kickouts
              .map(
                (k) => BubbleKickoutRecord.fromJson(k as Map<String, dynamic>),
              )
              .toList();
      return Result.success(records);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get user kickout history: $e'),
      );
    }
  }

  @override
  Future<Result<bool>> isUserKickedOut({
    required String bubbleId,
    required String userId,
  }) async {
    try {
      final response = await _remoteDataSource.get(
        '/api/v1/bubbles/$bubbleId/members/$userId/status',
      );
      final isKickedOut = response['is_kicked_out'] as bool? ?? false;
      return Result.success(isKickedOut);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to check if user is kicked out: $e'),
      );
    }
  }

  @override
  Future<Result<List<UserModel>>> getKickedOutUsers(String bubbleId) async {
    try {
      final response = await _remoteDataSource.get(
        '/api/v1/bubbles/$bubbleId/kicked-out-users',
      );
      final List<dynamic> users = response['users'] ?? [];
      final userModels =
          users
              .map((user) => UserModel.fromJson(user as Map<String, dynamic>))
              .toList();
      return Result.success(userModels);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get kicked out users: $e'),
      );
    }
  }

  @override
  Future<Result<void>> allowUserToRejoin({
    required String bubbleId,
    required String userId,
  }) async {
    try {
      await _remoteDataSource.post('/api/v1/bubbles/$bubbleId/allow-rejoin', {
        'user_id': userId,
      });

      // Also publish MQTT event for real-time notification
      _mqttService.publish('bubble/$bubbleId/kickout', {
        'type': 'rejoin_allowed',
        'bubbleId': bubbleId,
        'userId': userId,
        'timestamp': DateTime.now().toIso8601String(),
      });

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to allow user to rejoin: $e'),
      );
    }
  }
}
