import 'dart:async';
import 'dart:io';

import 'package:uuid/uuid.dart';

import '../../../repositories/chat/chat_repository.dart';
import '../../../repositories/auth/auth_repository.dart';
import '../../../statefulbusinesslogic/core/error/exceptions.dart';
import '../../../statefulbusinesslogic/core/error/result.dart';
import '../../../statefulbusinesslogic/core/models/chat_info_model.dart';
import '../../../statefulbusinesslogic/core/models/chat_message.dart';
import '../../../statefulbusinesslogic/core/models/send_message_model.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../datasources/http_remote_datasource.dart';
import '../../models/api_models.dart';
import '../../services/media/media_upload_service.dart';
import '../../services/mqtt/mqtt_service.dart';
import '../../../repositories/audio_recording/audio_recording_repository.dart';
import '../../../repositories/local_storage/abstract_chat_message_repository.dart';

class ChatRepositoryImpl implements ChatRepository {
  // _globalTypingSubscription = _webSocketService.typingStatusStream.listen((typingData) {
  //   _handleTypingStatus(typingData);
  // });

  // _globalStatusSubscription = _webSocketService.connectionStatusStream.listen((status) {
  //   _handleConnectionStatus(status);
  // });

  ChatRepositoryImpl({
    required MqttService mqttService,
    required HttpRemoteDataSource httpDataSource,
    required MediaUploadService mediaUploadService,
    required AuthRepository authRepository,
    required AudioRecordingRepository audioRecordingRepository,
    required AbstractChatMessageRepository localMessageRepository,
  }) : _mqttService = mqttService,
       _httpDataSource = httpDataSource,
       _mediaUploadService = mediaUploadService,
       _authRepository = authRepository,
       _audioRecordingRepository = audioRecordingRepository,
       _localMessageRepository = localMessageRepository {
    // It's crucial to handle the lifecycle of these global subscriptions carefully.
    // They should ideally be initialized when the first chat connection occurs
    // and closed when no more chats are active, or managed by WebSocketService itself.
    _initializeRealTimeListeners();
  }
  final MqttService _mqttService;
  final HttpRemoteDataSource _httpDataSource;
  final MediaUploadService _mediaUploadService;
  final AuthRepository _authRepository;
  final AudioRecordingRepository _audioRecordingRepository;
  final AbstractChatMessageRepository _localMessageRepository;

  // Local cache for better performance
  final Map<String, ChatInfoModel> _chatInfoCache = {};
  final Map<String, List<ChatMessage>> _messagesCache = {};

  // Stream controllers for real-time updates
  final StreamController<ChatMessage> _newMessageController =
      StreamController.broadcast();
  final StreamController<String> _messageUpdatedController =
      StreamController.broadcast();

  // Stream controllers for broadcasting filtered streams per chat ID
  // This is a simple in-memory way to manage multiple chat stream subscriptions.
  // A more robust solution might involve a map of StreamSubscriptions.
  final Map<String, StreamController<Map<String, dynamic>>>
  _messageStreamControllers = {};
  final Map<String, StreamController<Map<String, dynamic>>>
  _typingStreamControllers = {};
  final Map<String, StreamController<Map<String, dynamic>>>
  _chatEventsStreamControllers = {};

  // Subscriptions to the single WebSocketService streams
  StreamSubscription? _globalMessagesSubscription;

  void _initializeRealTimeListeners() {
    // Initialize global listeners for real-time updates
    _globalMessagesSubscription = _mqttService.messageStream.listen(
      _handleNewMessage,
    );

    // _globalTypingSubscription = _webSocketService.typingStatusStream.listen((typingData) {
    //   _handleTypingStatus(typingData);
    // });

    // _globalStatusSubscription = _webSocketService.connectionStatusStream.listen((status) {
    //   _handleConnectionStatus(status);
    // });
  }

  void _handleNewMessage(Map<String, dynamic> message) {
    final chatId = message['chat_id'] as String?;
    if (chatId != null && _messageStreamControllers.containsKey(chatId)) {
      _messageStreamControllers[chatId]!.add(message);
    }
  }

  void _handleTypingStatus(Map<String, dynamic> typingData) {
    final chatId = typingData['chat_id'] as String?;
    if (chatId != null && _typingStreamControllers.containsKey(chatId)) {
      _typingStreamControllers[chatId]!.add(typingData);
    }
  }

  void _handleConnectionStatus(Map<String, dynamic> status) {
    // Broadcast connection status to all active chat streams
    for (final controller in _chatEventsStreamControllers.values) {
      controller.add({'type': 'connection_status', 'data': status});
    }
  }

  @override
  Future<void> connectToChat(String chatId) async {
    try {
      // Create stream controllers for this chat if they don't exist
      if (!_messageStreamControllers.containsKey(chatId)) {
        _messageStreamControllers[chatId] = StreamController.broadcast();
      }
      if (!_typingStreamControllers.containsKey(chatId)) {
        _typingStreamControllers[chatId] = StreamController.broadcast();
      }
      if (!_chatEventsStreamControllers.containsKey(chatId)) {
        _chatEventsStreamControllers[chatId] = StreamController.broadcast();
      }

      // Connect to WebSocket for this chat
      final clientId = const Uuid().v4();

      // Retrieve current user ID for identification
      String? userId = _authRepository.authenticatedUser?.id;
      if (userId == null) {
        final result = await _authRepository.getCurrentUser();
        if (result.isSuccess) {
          userId = result.data.id;
        }
      }

      // Associate userId with MQTT service for publishing helpers
      if (userId != null && userId.isNotEmpty) {
        _mqttService.setCurrentUserId(userId);
      }

      await _mqttService.connect(
        host: 'localhost',
        port: 1883,
        clientId: clientId,
        username: userId ?? '',
        password: '', // Let MQTT service fetch latest JWT
      );
      // await _webSocketService.joinChat(chatId);
    } catch (e) {
      LoggingService.error('Error connecting to chat: $e');
    }
  }

  @override
  Future<void> disconnectFromChat(String chatId) async {
    try {
      // Close and remove stream controllers for this chat
      _messageStreamControllers[chatId]?.close();
      _messageStreamControllers.remove(chatId);

      _typingStreamControllers[chatId]?.close();
      _typingStreamControllers.remove(chatId);

      _chatEventsStreamControllers[chatId]?.close();
      _chatEventsStreamControllers.remove(chatId);

      // Leave chat on WebSocket
      // await _webSocketService.leaveChat(chatId);
    } catch (e) {
      LoggingService.error('Error disconnecting from chat: $e');
    }
  }

  @override
  Stream<ChatMessage> messageStream(String chatId) {
    if (!_messageStreamControllers.containsKey(chatId)) {
      _messageStreamControllers[chatId] = StreamController.broadcast();
    }
    return _messageStreamControllers[chatId]!.stream.map(
      (messageData) => ChatMessage(
        id: messageData['id'] ?? '',
        content: messageData['content'] ?? '',
        senderId: messageData['sender_id'] ?? '',
        timestamp:
            DateTime.tryParse(messageData['timestamp'] ?? '') ?? DateTime.now(),
        mediaType: _parseMediaType(messageData['type'] ?? 'text'),
      ),
    );
  }

  @override
  Stream<Map<String, dynamic>> typingStatusStream(String chatId) {
    if (!_typingStreamControllers.containsKey(chatId)) {
      _typingStreamControllers[chatId] = StreamController.broadcast();
    }
    return _typingStreamControllers[chatId]!.stream;
  }

  @override
  Stream<Map<String, dynamic>> chatEventsStream(String chatId) {
    if (!_chatEventsStreamControllers.containsKey(chatId)) {
      _chatEventsStreamControllers[chatId] = StreamController.broadcast();
    }
    return _chatEventsStreamControllers[chatId]!.stream;
  }

  @override
  Future<void> sendTypingStatus(
    String chatId,
    String userId, {
    required bool isTyping,
  }) async {
    try {
      _mqttService.sendTypingStatus(chatId, isTyping);
    } catch (e) {
      LoggingService.error('Error sending typing status: $e');
    }
  }

  // --- Stubbed methods for existing ChatRepository interface ---
  // These would need to be implemented using an HTTP client (ApiClient)

  @override
  Future<Map<String, dynamic>> getChatInfo(
    String chatId, {
    required bool isGroupChat,
  }) async {
    try {
      // Check cache first
      if (_chatInfoCache.containsKey(chatId)) {
        final cachedInfo = _chatInfoCache[chatId]!;
        // Check if cache is still fresh (less than 5 minutes old)
        if (DateTime.now()
                .difference(cachedInfo.lastUpdated ?? DateTime.now())
                .inMinutes <
            5) {
          return cachedInfo.toJson();
        }
      }

      // Determine if it's a bubble chat or direct chat
      ChatInfoModel chatInfo;

      if (isGroupChat || chatId.startsWith('bubble_')) {
        final bubbleId =
            chatId.startsWith('bubble_')
                ? chatId.replaceFirst('bubble_', '')
                : chatId;
        final apiBubble = await _httpDataSource.getBubble(bubbleId);
        final members = await _httpDataSource.getBubbleMembers(bubbleId);

        chatInfo = ChatInfoModel(
          id: chatId,
          isGroupChat: true,
          name: apiBubble.name,
          participantIds: members.map((m) => m.id).toList(),
          participantNames:
              members
                  .map((m) => '${m.firstName} ${m.lastName}'.trim())
                  .toList(),
          isOnline: true, // Group chats are always "online"
          lastUpdated: DateTime.now(),
        );
      } else {
        // Direct chat with user
        final user = await _httpDataSource.getUserProfile(chatId);
        chatInfo = ChatInfoModel(
          id: chatId,
          isGroupChat: false,
          name: '${user.firstName ?? ''} ${user.lastName ?? ''}'.trim(),
          participantIds: [chatId],
          participantNames: [
            '${user.firstName ?? ''} ${user.lastName ?? ''}'.trim(),
          ],
          isOnline: user.isOnline ?? false,
          lastUpdated: DateTime.now(),
        );
      }

      // Cache the result
      _chatInfoCache[chatId] = chatInfo;
      return chatInfo.toJson();
    } on ServerException {
      rethrow;
    } catch (e) {
      throw ServerException(message: e.toString());
    }
  }

  @override
  Future<Result<List<ChatMessage>>> getMessages(
    String chatId, {
    int limit = 50,
    String? before,
  }) async {
    try {
      // Check cache first
      if (_messagesCache.containsKey(chatId)) {
        // A more sophisticated cache would have an expiry policy
        return Result.success(_messagesCache[chatId]!);
      }

      // If not in cache or cache is old, fetch from API
      final messages = await _fetchAndCacheMessages(chatId);
      return Result.success(messages);
    } catch (e) {
      LoggingService.error('Failed to get messages for chat $chatId', error: e);
      return Result.failure(
        NetworkError(message: 'Failed to get messages: ${e.toString()}'),
      );
    }
  }

  Future<List<ChatMessage>> _fetchAndCacheMessages(String chatId) async {
    try {
      // This is a simplified example; a real implementation would
      // involve a remote data source (API call)
      final apiMessages = await _httpDataSource.getChatMessages(chatId);

      final messages =
          apiMessages.data
              .map(
                (msg) => ChatMessage(
                  id: msg.id,
                  content: msg.content,
                  senderId: msg.senderId,
                  timestamp: msg.createdAt,
                  mediaType: _parseMediaType(msg.messageType),
                ),
              )
              .toList();

      _messagesCache[chatId] = messages;

      // Persist locally
      for (final m in messages) {
        await _localMessageRepository.saveMessage(m, chatId);
      }

      return messages;
    } on ServerException catch (_) {
      // Network issue – fall back to local storage
      final local = await _localMessageRepository.getMessages(chatId);
      return local;
    } catch (_) {
      final local = await _localMessageRepository.getMessages(chatId);
      return local;
    }
  }

  ChatMessage _mapApiMessageToChatMessage(ApiChatMessage apiMessage) =>
      ChatMessage(
        id: apiMessage.id,
        content: apiMessage.content,
        senderId: apiMessage.senderId,
        timestamp: apiMessage.createdAt,
        mediaType: _parseMediaType(apiMessage.messageType),
      );

  MediaType _parseMediaType(String type) {
    switch (type.toLowerCase()) {
      case 'image':
        return MediaType.image;
      case 'video':
        return MediaType.video;
      case 'audio':
        return MediaType.audio;
      case 'document':
        return MediaType.file;
      default:
        return MediaType.text;
    }
  }

  @override
  Future<Result<ChatMessage>> sendMessage(
    String chatId,
    String content,
    String senderId,
  ) async {
    try {
      final request = SendMessageRequest(bubbleId: chatId, content: content);

      final sentMessage = await _httpDataSource.sendMessage(request);

      final messageModel = ChatMessage(
        id: sentMessage.id,
        content: sentMessage.content,
        senderId: senderId,
        timestamp: sentMessage.createdAt,
        mediaType: _parseMediaType(sentMessage.messageType),
      );

      await _localMessageRepository.saveMessage(messageModel, chatId);

      return Result.success(messageModel);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to send message: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<ChatMessage>> sendMediaMessage({
    required String chatId,
    required String senderId,
    required File mediaFile,
    required MediaType mediaType,
    String? caption,
    String? replyToMessageId,
  }) async {
    try {
      // Step 1: Upload the media file
      final uploadResult = await _mediaUploadService.uploadFile(
        file: mediaFile,
        category: 'chat',
      );

      if (!uploadResult.success) {
        return Result.failure(
          NetworkError(
            message: 'Failed to upload media: ${uploadResult.error}',
          ),
        );
      }

      // Step 2: Send message with media URL via realtime service
      final messageResponse = await _httpDataSource.post(
        '/realtime/message/send',
        {
          'chat_id': chatId,
          'content': caption ?? '',
          'message_type': _mediaTypeToString(mediaType),
          'media_url': uploadResult.url,
          'media_type': uploadResult.contentType,
          'media_title': uploadResult.originalName,
          'metadata': {
            'file_id': uploadResult.fileId,
            'file_size': uploadResult.size?.toString(),
            if (replyToMessageId != null) 'reply_to': replyToMessageId,
          },
        },
      );

      // Step 3: Create ChatMessage from response
      final messageModel = ChatMessage(
        id: messageResponse['message_id'],
        content: caption ?? uploadResult.originalName ?? 'Media file',
        senderId: senderId,
        timestamp: DateTime.now(),
        mediaType: mediaType,
        mediaUrl: uploadResult.url,
        caption: uploadResult.originalName,
      );

      await _localMessageRepository.saveMessage(messageModel, chatId);

      return Result.success(messageModel);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to send media message: ${e.toString()}'),
      );
    }
  }

  String _mediaTypeToString(MediaType mediaType) {
    switch (mediaType) {
      case MediaType.image:
        return 'image';
      case MediaType.video:
        return 'video';
      case MediaType.audio:
        return 'audio';
      case MediaType.file:
        return 'file';
      case MediaType.text:
      default:
        return 'text';
    }
  }

  @override
  Future<void> updateMessageStatus(
    String chatId,
    String messageId,
    String status,
  ) async {
    try {
      if (status == 'read') {
        await _httpDataSource.markMessageAsRead(messageId);
      }
      // For other statuses, we might need additional API endpoints
    } catch (e) {
      LoggingService.error('Error updating message status: $e');
    }
  }

  @override
  Future<Result<void>> deleteMessage(String messageId, String userId) async {
    try {
      await _httpDataSource.deleteMessage(messageId);
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to delete message: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<List<ChatMessage>>> searchMessages(
    String chatId,
    String query,
  ) async {
    try {
      final apiMessages = await _httpDataSource.searchChatMessages(
        chatId,
        query,
        limit: 50,
      );
      final results =
          apiMessages.data.map(_mapApiMessageToChatMessage).toList();
      // Persist to local cache for offline availability
      await _localMessageRepository.saveMessages(chatId, results);
      return Result.success(results);
    } catch (e) {
      // Fallback: local search (offline)
      final local = await _localMessageRepository.searchMessages(chatId, query);
      return Result.success(local);
    }
  }

  @override
  Future<Result<void>> markMessageAsRead(
    String messageId,
    String userId,
  ) async {
    try {
      await _httpDataSource.markMessageAsRead(messageId);
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(
          message: 'Failed to mark message as read: ${e.toString()}',
        ),
      );
    }
  }

  @override
  Future<Result<void>> markAllMessagesAsRead(
    String chatId,
    String userId,
  ) async {
    try {
      await _httpDataSource.put('/api/chats/$chatId/mark-all-read', {
        'userId': userId,
      });
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(
          message: 'Failed to mark all messages as read: ${e.toString()}',
        ),
      );
    }
  }

  @override
  Future<Result<ChatMessage>> editMessage(
    String messageId,
    String newContent,
    String userId,
  ) async {
    try {
      final response = await _httpDataSource.put('/api/messages/$messageId', {
        'content': newContent,
        'userId': userId,
      });

      final editedMessage = ChatMessage.fromJson(
        response['message'] as Map<String, dynamic>,
      );
      return Result.success(editedMessage);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to edit message: ${e.toString()}'),
      );
    }
  }

  @override
  Future<Result<int>> getUnreadCount(String chatId, String userId) async {
    try {
      final response = await _httpDataSource.get(
        '/api/chats/$chatId/unread-count?userId=$userId',
      );
      final count = response['unreadCount'] as int? ?? 0;
      return Result.success(count);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to get unread count: ${e.toString()}'),
      );
    }
  }

  @override
  Stream<int> unreadCountStream(String chatId, String userId) {
    // Create a stream controller for unread count updates
    final controller = StreamController<int>.broadcast();

    // Subscribe to MQTT for real-time unread count updates
    final subscription = _mqttService.messageStream.listen((messageData) {
      // Handle MQTT 5.0 message format
      try {
        final topic = messageData['topic'] as String? ?? '';
        if (topic.contains('/unread-count') &&
            (messageData['chatId'] as String?) == chatId) {
          final count = messageData['unreadCount'] as int? ?? 0;
          controller.add(count);
        }
      } catch (e) {
        print('Error processing unread count message: $e');
      }
    });

    // Get initial unread count
    getUnreadCount(chatId, userId).then((result) {
      switch (result) {
        case Success<int>(:final data):
          controller.add(data);
        case PartialSuccess<int>(:final data, :final message):
          controller.add(data);
        case Failure<int>():
          controller.add(0);
      }
    });

    // Clean up subscription when stream is cancelled
    controller.onCancel = subscription.cancel;

    return controller.stream;
  }

  Future<Result<void>> connectToRealtime() async {
    try {
      // Connect to MQTT with required parameters
      await _mqttService.connect(
        host: 'localhost',
        port: 1883,
        clientId: 'chat_${DateTime.now().millisecondsSinceEpoch}',
        username: '',
        password: '',
      );

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to connect to realtime: ${e.toString()}'),
      );
    }
  }

  void dispose() {
    _globalMessagesSubscription?.cancel();
    // _globalTypingSubscription.cancel();
    // _globalStatusSubscription.cancel();
    _newMessageController.close();
    _messageUpdatedController.close();

    // Also close all chat-specific stream controllers
    for (final controller in _messageStreamControllers.values) {
      controller.close();
    }
    for (final controller in _typingStreamControllers.values) {
      controller.close();
    }
    for (final controller in _chatEventsStreamControllers.values) {
      controller.close();
    }
  }

  @override
  Future<Result<String>> uploadMedia(File mediaFile, String mediaType) async {
    try {
      // Simulate media upload
      await Future<void>.delayed(const Duration(seconds: 2));

      // Return a mock URL
      final fileName = mediaFile.path.split('/').last;
      final mockUrl = 'https://mock-storage.com/media/$fileName';

      return Result.success(mockUrl);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to upload media: $e'),
      );
    }
  }

  @override
  Future<Result<bool>> startVoiceRecording() async {
    try {
      await _audioRecordingRepository.initialize();
      final permission = await _audioRecordingRepository.checkPermission();
      if (!permission) {
        return Result.failure(
          NetworkError(message: 'Microphone permission denied'),
        );
      }
      await _audioRecordingRepository.startRecording();
      return Result.success(true);
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to start recording: $e'),
      );
    }
  }

  @override
  Future<Result<File>> stopVoiceRecording() async {
    try {
      final path = await _audioRecordingRepository.stopRecording();
      if (path == null) {
        return Result.failure(NetworkError(message: 'Recording failed'));
      }
      return Result.success(File(path));
    } catch (e) {
      return Result.failure(
        NetworkError(message: 'Failed to stop recording: $e'),
      );
    }
  }
}
