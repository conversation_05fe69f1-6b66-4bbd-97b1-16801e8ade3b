/// Cache-first implementation of ContactRequestRepository
/// 
/// This implementation prioritizes local cache and only fetches from remote
/// when triggered by MQTT events or initial load.

import 'dart:async';
import '../../repositories/contact/contact_request_repository.dart';
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';
import '../datasources/http_remote_datasource.dart';
import '../services/logging/logging_service.dart';
import '../services/api/http_api_service.dart';
import 'base/cache_first_repository_base.dart';

class ContactRequestCacheFirstRepositoryImpl extends CacheFirstRepositoryBase implements ContactRequestRepository {
  ContactRequestCacheFirstRepositoryImpl({
    required HttpApiService apiService,
    required HttpRemoteDataSource remoteDataSource,
    super.debugMode = false,
  }) : _apiService = apiService,
       _remoteDataSource = remoteDataSource;

  final HttpApiService _apiService;
  final HttpRemoteDataSource _remoteDataSource;

  // In-memory cache (in production, this would use Drift database)
  List<ContactRequest> _pendingReceivedRequests = [];
  List<ContactRequest> _pendingSentRequests = [];
  List<ContactRequest> _requestHistory = [];
  final Map<String, ContactRequest> _requestsById = {};

  // Stream controllers for real-time updates
  final StreamController<List<ContactRequest>> _pendingReceivedController = 
      StreamController<List<ContactRequest>>.broadcast();
  final StreamController<List<ContactRequest>> _pendingSentController = 
      StreamController<List<ContactRequest>>.broadcast();

  @override
  Stream<List<ContactRequest>> get pendingReceivedRequestsStream => 
      _pendingReceivedController.stream;

  @override
  Stream<List<ContactRequest>> get pendingSentRequestsStream => 
      _pendingSentController.stream;

  @override
  Future<bool> get hasCachedData async {
    return _pendingReceivedRequests.isNotEmpty || 
           _pendingSentRequests.isNotEmpty || 
           _requestHistory.isNotEmpty;
  }

  @override
  Future<void> initializeCache() async {
    // In production, this would initialize Drift database connection
    LoggingService.info('ContactRequestRepository: Cache initialized');
  }

  @override
  Future<void> fetchFromRemote() async {
    try {
      LoggingService.info('ContactRequestRepository: Fetching from remote');
      
      // Fetch pending received requests
      final receivedRequests = await _apiService.getPendingContactRequests();
      _pendingReceivedRequests = receivedRequests.map((apiContact) => 
          ContactRequest.fromJson(apiContact.toJson())).toList();
      
      // Update cache by ID
      for (final request in _pendingReceivedRequests) {
        _requestsById[request.id] = request;
      }
      
      // Notify streams
      _pendingReceivedController.add(_pendingReceivedRequests);
      
      LoggingService.info('ContactRequestRepository: Fetched ${_pendingReceivedRequests.length} pending received requests');
    } catch (e) {
      LoggingService.error('ContactRequestRepository: Failed to fetch from remote: $e');
      rethrow;
    }
  }

  @override
  Future<void> clearCache() async {
    _pendingReceivedRequests.clear();
    _pendingSentRequests.clear();
    _requestHistory.clear();
    _requestsById.clear();
    
    _pendingReceivedController.add([]);
    _pendingSentController.add([]);
    
    LoggingService.info('ContactRequestRepository: Cache cleared');
  }

  @override
  Future<void> processContactRequestEvent(ContactRequestEvent event) async {
    LoggingService.info('ContactRequestRepository: Processing event: ${event.action} for request ${event.requestId}');
    
    switch (event.action) {
      case ContactRequestAction.received:
        // New contact request received - refresh from remote to get full details
        await refreshFromRemote();
        break;
        
      case ContactRequestAction.accepted:
      case ContactRequestAction.declined:
      case ContactRequestAction.cancelled:
      case ContactRequestAction.expired:
        // Request status changed - remove from pending lists
        _pendingReceivedRequests.removeWhere((r) => r.id == event.requestId);
        _pendingSentRequests.removeWhere((r) => r.id == event.requestId);
        _requestsById.remove(event.requestId);
        
        // Notify streams
        _pendingReceivedController.add(_pendingReceivedRequests);
        _pendingSentController.add(_pendingSentRequests);
        break;
    }
  }

  @override
  Future<Result<ContactRequest>> sendContactRequest(String receiverId, String message) async {
    try {
      final response = await _remoteDataSource.post('/contact/requests', {
        'recipient_id': receiverId,
        'message': message,
      });
      
      final request = ContactRequest.fromJson(response['request']);
      
      // Add to sent requests cache
      _pendingSentRequests.add(request);
      _requestsById[request.id] = request;
      _pendingSentController.add(_pendingSentRequests);
      
      return Result.success(request);
    } catch (e) {
      return Result.failure(const NetworkError(message: 'Failed to send contact request'));
    }
  }

  @override
  Future<Result<List<ContactRequest>>> getPendingReceivedRequests() async {
    return executeCacheFirst(
      cacheOperation: () async => _pendingReceivedRequests.isNotEmpty ? _pendingReceivedRequests : null,
      remoteOperation: () async {
        await fetchFromRemote();
        return _pendingReceivedRequests;
      },
    ).then((requests) => Result.success(requests));
  }

  @override
  Future<Result<List<ContactRequest>>> getPendingSentRequests() async {
    return Result.success(_pendingSentRequests);
  }

  @override
  Future<Result<void>> acceptContactRequest(String requestId) async {
    try {
      await _remoteDataSource.post('/contact/requests/$requestId/accept', {});
      
      // Remove from pending received requests
      _pendingReceivedRequests.removeWhere((r) => r.id == requestId);
      _requestsById.remove(requestId);
      _pendingReceivedController.add(_pendingReceivedRequests);
      
      return Result.success(null);
    } catch (e) {
      return Result.failure(const NetworkError(message: 'Failed to accept contact request'));
    }
  }

  @override
  Future<Result<void>> rejectContactRequest(String requestId) async {
    try {
      await _remoteDataSource.post('/contact/requests/$requestId/decline', {});
      
      // Remove from pending received requests
      _pendingReceivedRequests.removeWhere((r) => r.id == requestId);
      _requestsById.remove(requestId);
      _pendingReceivedController.add(_pendingReceivedRequests);
      
      return Result.success(null);
    } catch (e) {
      return Result.failure(const NetworkError(message: 'Failed to reject contact request'));
    }
  }

  @override
  Future<Result<void>> cancelContactRequest(String requestId) async {
    try {
      await _remoteDataSource.delete('/contact/requests/$requestId');
      
      // Remove from pending sent requests
      _pendingSentRequests.removeWhere((r) => r.id == requestId);
      _requestsById.remove(requestId);
      _pendingSentController.add(_pendingSentRequests);
      
      return Result.success(null);
    } catch (e) {
      return Result.failure(const NetworkError(message: 'Failed to cancel contact request'));
    }
  }

  @override
  Future<Result<ContactRequest>> getContactRequest(String requestId) async {
    // Check cache first
    final cachedRequest = _requestsById[requestId];
    if (cachedRequest != null) {
      return Result.success(cachedRequest);
    }
    
    // Fetch from remote if not in cache
    try {
      final response = await _remoteDataSource.get('/contact/requests/$requestId');
      final request = ContactRequest.fromJson(response['request']);
      _requestsById[requestId] = request;
      return Result.success(request);
    } catch (e) {
      return Result.failure(const NetworkError(message: 'Failed to get contact request'));
    }
  }

  @override
  Future<Result<bool>> hasPendingRequest(String userId1, String userId2) async {
    // Check cache first
    final hasPending = _pendingReceivedRequests.any((r) => 
        (r.senderId == userId1 && r.receiverId == userId2) ||
        (r.senderId == userId2 && r.receiverId == userId1)) ||
      _pendingSentRequests.any((r) => 
        (r.senderId == userId1 && r.receiverId == userId2) ||
        (r.senderId == userId2 && r.receiverId == userId1));
    
    return Result.success(hasPending);
  }

  @override
  Future<Result<List<ContactRequest>>> getContactRequestHistory() async {
    return Result.success(_requestHistory);
  }

  @override
  Future<Result<List<UserModel>>> getMutualContacts(String userId) async {
    // This would typically fetch from a contacts repository
    return Result.success([]);
  }

  @override
  Future<Result<List<UserModel>>> searchUsers(String query) async {
    // Always fetch fresh for search
    try {
      final response = await _remoteDataSource.get('/users/search?q=$query');
      final users = <UserModel>[];
      for (final userData in response['users'] ?? []) {
        users.add(UserModel.fromJson(userData));
      }
      return Result.success(users);
    } catch (e) {
      return Result.failure(const NetworkError(message: 'Failed to search users'));
    }
  }

  @override
  Future<Result<List<UserModel>>> getSuggestedContacts() async {
    // This would typically use a recommendation algorithm
    return Result.success([]);
  }

  @override
  Future<Result<void>> expireOldRequests() async {
    try {
      await _remoteDataSource.post('/contact/requests/expire-old', {});
      // Refresh cache after expiring old requests
      await refreshFromRemote();
      return Result.success(null);
    } catch (e) {
      return Result.failure(const NetworkError(message: 'Failed to expire old requests'));
    }
  }

  void dispose() {
    super.dispose();
    _pendingReceivedController.close();
    _pendingSentController.close();
  }
}

/// Network error class
class NetworkError {
  const NetworkError({required this.message});
  final String message;
}
