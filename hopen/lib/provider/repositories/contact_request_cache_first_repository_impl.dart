/// Cache-first implementation of ContactRequestRepository
///
/// This implementation prioritizes local cache and only fetches from remote
/// when triggered by MQTT events or initial load.

import 'dart:async';
import '../../repositories/contact/contact_request_repository.dart';
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/error/failures.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';
import '../datasources/http_remote_datasource.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';
import '../services/api/http_api_service.dart';
import 'base/cache_first_repository_base.dart';
import '../../statefulbusinesslogic/core/db/app_database.dart';

class ContactRequestCacheFirstRepositoryImpl extends CacheFirstRepositoryBase
    implements ContactRequestRepository {
  ContactRequestCacheFirstRepositoryImpl({
    required HttpApiService apiService,
    required HttpRemoteDataSource remoteDataSource,
    required ContactRequestDao contactRequestDao,
    super.debugMode = false,
  }) : _apiService = apiService,
       _remoteDataSource = remoteDataSource,
       _contactRequestDao = contactRequestDao;

  final HttpApiService _apiService;
  final HttpRemoteDataSource _remoteDataSource;
  final ContactRequestDao _contactRequestDao;

  String? _currentUserId;

  /// Convert Drift ContactRequest to domain ContactRequest
  ContactRequest _convertFromDrift(DriftContactRequest driftRequest) {
    return ContactRequest(
      id: driftRequest.id,
      senderId: driftRequest.requesterId,
      senderName: 'Unknown', // Will be populated from user data
      receiverId: driftRequest.recipientId,
      receiverName: 'Unknown', // Will be populated from user data
      sentAt: driftRequest.createdAt,
      status: ContactRequestStatus.values.firstWhere(
        (e) => e.name == driftRequest.status,
        orElse: () => ContactRequestStatus.pending,
      ),
      message: driftRequest.message,
      respondedAt:
          driftRequest.updatedAt != driftRequest.createdAt
              ? driftRequest.updatedAt
              : null,
    );
  }

  /// Convert domain ContactRequest to Drift ContactRequest
  DriftContactRequest _convertToDrift(ContactRequest request) {
    return DriftContactRequest(
      id: request.id,
      requesterId: request.senderId,
      recipientId: request.receiverId,
      status: request.status.name,
      message: request.message,
      createdAt: request.sentAt,
      updatedAt: request.respondedAt ?? request.sentAt,
      cachedAt: DateTime.now(),
      isStale: false,
    );
  }

  /// Set current user ID for cache operations
  void setCurrentUserId(String userId) {
    _currentUserId = userId;
  }

  @override
  Stream<List<ContactRequest>> get pendingReceivedRequestsStream {
    if (_currentUserId == null) return Stream.value([]);
    return _contactRequestDao
        .watchPendingReceivedRequests(_currentUserId!)
        .map((driftRequests) => driftRequests.map(_convertFromDrift).toList());
  }

  @override
  Stream<List<ContactRequest>> get pendingSentRequestsStream {
    if (_currentUserId == null) return Stream.value([]);
    return _contactRequestDao
        .watchPendingSentRequests(_currentUserId!)
        .map((driftRequests) => driftRequests.map(_convertFromDrift).toList());
  }

  @override
  Future<bool> get hasCachedData async {
    if (_currentUserId == null) return false;
    final requests = await _contactRequestDao.getContactRequestsForUser(
      _currentUserId!,
    );
    return requests.isNotEmpty;
  }

  @override
  Future<void> initializeCache() async {
    if (_currentUserId == null) {
      LoggingService.warning(
        'ContactRequestRepository: Cannot initialize cache without user ID',
      );
      return;
    }
    LoggingService.info(
      'ContactRequestRepository: Cache initialized for user $_currentUserId',
    );
  }

  @override
  Future<void> fetchFromRemote() async {
    if (_currentUserId == null) {
      LoggingService.warning(
        'ContactRequestRepository: Cannot fetch from remote without user ID',
      );
      return;
    }

    try {
      LoggingService.info(
        'ContactRequestRepository: Fetching from remote for user $_currentUserId',
      );

      // Fetch pending received requests
      final receivedRequests = await _apiService.getPendingContactRequests();

      // Convert and store in Drift database
      for (final apiContact in receivedRequests) {
        final contactRequest = ContactRequest.fromJson(apiContact.toJson());
        final driftRequest = _convertToDrift(contactRequest);
        await _contactRequestDao.insertOrUpdateContactRequest(driftRequest);
      }

      LoggingService.info(
        'ContactRequestRepository: Cached ${receivedRequests.length} pending received requests',
      );
    } catch (e) {
      LoggingService.error(
        'ContactRequestRepository: Failed to fetch from remote: $e',
      );
      rethrow;
    }
  }

  @override
  Future<void> clearCache() async {
    if (_currentUserId == null) return;

    await _contactRequestDao.clearContactRequestsForUser(_currentUserId!);
    LoggingService.info(
      'ContactRequestRepository: Cache cleared for user $_currentUserId',
    );
  }

  @override
  Future<void> processContactRequestEvent(ContactRequestEvent event) async {
    LoggingService.info(
      'ContactRequestRepository: Processing event: ${event.action} for request ${event.requestId}',
    );

    switch (event.action) {
      case ContactRequestAction.received:
        // New contact request received - refresh from remote to get full details
        await fetchFromRemote();
        break;

      case ContactRequestAction.accepted:
      case ContactRequestAction.declined:
      case ContactRequestAction.cancelled:
        // Update request status in database
        await _contactRequestDao.updateContactRequestStatus(
          event.requestId,
          event.action.name,
        );
        break;
      case ContactRequestAction.expired:
        // Remove expired request from database
        await _contactRequestDao.deleteContactRequest(event.requestId);
        break;
    }
  }

  @override
  Future<Result<ContactRequest>> sendContactRequest(
    String receiverId,
    String message,
  ) async {
    try {
      final response = await _remoteDataSource.post('/contact/requests', {
        'recipient_id': receiverId,
        'message': message,
      });

      final request = ContactRequest.fromJson(
        response['request'] as Map<String, dynamic>,
      );

      // Store in Drift database
      final driftRequest = _convertToDrift(request);
      await _contactRequestDao.insertOrUpdateContactRequest(driftRequest);

      return Result.success(request);
    } catch (e) {
      return Result.failure(
        NetworkFailure(message: 'Failed to send contact request'),
      );
    }
  }

  @override
  Future<Result<List<ContactRequest>>> getPendingReceivedRequests() async {
    if (_currentUserId == null) {
      return Result.failure(
        const NetworkFailure(message: 'User not authenticated'),
      );
    }

    try {
      final driftRequests = await _contactRequestDao.getPendingReceivedRequests(
        _currentUserId!,
      );
      final requests = driftRequests.map(_convertFromDrift).toList();
      return Result.success(requests);
    } catch (e) {
      LoggingService.error(
        'ContactRequestRepository: Failed to get pending received requests: $e',
      );
      return Result.failure(
        const NetworkFailure(
          message: 'Failed to get pending received requests',
        ),
      );
    }
  }

  @override
  Future<Result<List<ContactRequest>>> getPendingSentRequests() async {
    if (_currentUserId == null) {
      return Result.failure(
        const NetworkFailure(message: 'User not authenticated'),
      );
    }

    try {
      final driftRequests = await _contactRequestDao.getPendingSentRequests(
        _currentUserId!,
      );
      final requests = driftRequests.map(_convertFromDrift).toList();
      return Result.success(requests);
    } catch (e) {
      LoggingService.error(
        'ContactRequestRepository: Failed to get pending sent requests: $e',
      );
      return Result.failure(
        const NetworkFailure(message: 'Failed to get pending sent requests'),
      );
    }
  }

  @override
  Future<Result<void>> acceptContactRequest(String requestId) async {
    try {
      await _remoteDataSource.post('/contact/requests/$requestId/accept', {});

      // Update request status in Drift database
      await _contactRequestDao.updateContactRequestStatus(
        requestId,
        'accepted',
      );

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        const NetworkFailure(message: 'Failed to accept contact request'),
      );
    }
  }

  @override
  Future<Result<void>> rejectContactRequest(String requestId) async {
    try {
      await _remoteDataSource.post('/contact/requests/$requestId/decline', {});

      // Update request status in Drift database
      await _contactRequestDao.updateContactRequestStatus(
        requestId,
        'rejected',
      );

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        const NetworkFailure(message: 'Failed to reject contact request'),
      );
    }
  }

  @override
  Future<Result<void>> cancelContactRequest(String requestId) async {
    try {
      await _remoteDataSource.delete('/contact/requests/$requestId');

      // Update request status in Drift database
      await _contactRequestDao.updateContactRequestStatus(
        requestId,
        'cancelled',
      );

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        const NetworkFailure(message: 'Failed to cancel contact request'),
      );
    }
  }

  @override
  Future<Result<ContactRequest>> getContactRequest(String requestId) async {
    try {
      // Check Drift database first
      final driftRequest = await _contactRequestDao.getContactRequestById(
        requestId,
      );
      if (driftRequest != null) {
        return Result.success(_convertFromDrift(driftRequest));
      }

      // Fetch from remote if not in cache
      final response = await _remoteDataSource.get(
        '/contact/requests/$requestId',
      );
      final request = ContactRequest.fromJson(
        response['request'] as Map<String, dynamic>,
      );

      // Store in Drift database
      final driftRequest2 = _convertToDrift(request);
      await _contactRequestDao.insertOrUpdateContactRequest(driftRequest2);

      return Result.success(request);
    } catch (e) {
      return Result.failure(
        const NetworkFailure(message: 'Failed to get contact request'),
      );
    }
  }

  @override
  Future<Result<bool>> hasPendingRequest(String userId1, String userId2) async {
    if (_currentUserId == null) {
      return Result.failure(
        const NetworkFailure(message: 'User not authenticated'),
      );
    }

    try {
      // For now, return false - this would need proper implementation
      // based on the actual ContactRequestDao interface
      return Result.success(false);
    } catch (e) {
      LoggingService.error(
        'ContactRequestRepository: Failed to check pending request: $e',
      );
      return Result.failure(
        const NetworkFailure(message: 'Failed to check pending request'),
      );
    }
  }

  @override
  Future<Result<List<ContactRequest>>> getContactRequestHistory() async {
    if (_currentUserId == null) {
      return Result.failure(
        const NetworkFailure(message: 'User not authenticated'),
      );
    }

    try {
      // For now, return empty list - this would need proper implementation
      // based on the actual ContactRequestDao interface
      return Result.success(<ContactRequest>[]);
    } catch (e) {
      LoggingService.error(
        'ContactRequestRepository: Failed to get contact request history: $e',
      );
      return Result.failure(
        const NetworkFailure(message: 'Failed to get contact request history'),
      );
    }
  }

  @override
  Future<Result<List<UserModel>>> getMutualContacts(String userId) async {
    // This would typically fetch from a contacts repository
    return Result.success([]);
  }

  @override
  Future<Result<List<UserModel>>> searchUsers(String query) async {
    // Always fetch fresh for search
    try {
      final response = await _remoteDataSource.get('/users/search?q=$query');
      final users = <UserModel>[];
      for (final userData in (response['users'] as List<dynamic>? ?? [])) {
        users.add(UserModel.fromJson(userData as Map<String, dynamic>));
      }
      return Result.success(users);
    } catch (e) {
      return Result.failure(
        const NetworkFailure(message: 'Failed to search users'),
      );
    }
  }

  @override
  Future<Result<List<UserModel>>> getSuggestedContacts() async {
    // This would typically use a recommendation algorithm
    return Result.success([]);
  }

  @override
  Future<Result<void>> expireOldRequests() async {
    try {
      await _remoteDataSource.post('/contact/requests/expire-old', {});
      // Refresh cache after expiring old requests
      await refreshFromRemote();
      return Result.success(null);
    } catch (e) {
      return Result.failure(
        const NetworkFailure(message: 'Failed to expire old requests'),
      );
    }
  }

  @override
  Future<void> handleMqttEvent(MqttEvent event) async {
    LoggingService.info(
      'ContactRequestRepository: Processing MQTT event: ${event.runtimeType}',
    );

    // Handle contact request events specifically
    if (event is ContactRequestEvent) {
      await processContactRequestEvent(event);
    } else {
      LoggingService.warning(
        'ContactRequestRepository: Received non-contact-request event: ${event.runtimeType}',
      );
    }
  }

  // Drift streams are automatically disposed when the DAO is disposed
}
