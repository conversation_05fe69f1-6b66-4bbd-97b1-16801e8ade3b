/// Cache-first implementation of FriendRequestRepository
///
/// This implementation prioritizes local cache and only fetches from remote
/// when triggered by MQTT events or initial load.

import 'dart:async';
import '../../repositories/friendship/friend_request_repository.dart';
import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/friend_request.dart';
import '../../statefulbusinesslogic/core/error/exceptions.dart';
import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';
import '../../statefulbusinesslogic/core/db/app_database.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';
import '../datasources/http_remote_datasource.dart';
import '../services/api/http_api_service.dart';
import 'base/cache_first_repository_base.dart';

class FriendRequestCacheFirstRepositoryImpl extends CacheFirstRepositoryBase
    implements FriendRequestRepository {
  FriendRequestCacheFirstRepositoryImpl({
    required HttpApiService apiService,
    required HttpRemoteDataSource remoteDataSource,
    required FriendRequestDao friendRequestDao,
    super.debugMode = false,
  }) : _apiService = apiService,
       _remoteDataSource = remoteDataSource,
       _friendRequestDao = friendRequestDao;

  final HttpApiService _apiService;
  final HttpRemoteDataSource _remoteDataSource;
  final FriendRequestDao _friendRequestDao;

  String? _currentUserId;

  /// Convert Drift FriendRequest to domain FriendRequest
  FriendRequest _convertFromDrift(DriftFriendRequest driftRequest) {
    return FriendRequest(
      id: driftRequest.id,
      requesterId: driftRequest.requesterId,
      recipientId: driftRequest.recipientId,
      status: driftRequest.status,
      sourceBubbleId: driftRequest.sourceBubbleId,
      createdAt: driftRequest.createdAt,
      autoGenerated: driftRequest.autoGenerated,
      // Optional fields not stored in Drift - would need to be fetched separately
      requesterName: null,
      requesterUsername: null,
      requesterProfilePicUrl: null,
      bubbleName: null,
    );
  }

  /// Convert domain FriendRequest to Drift FriendRequest
  DriftFriendRequest _convertToDrift(FriendRequest request) {
    return DriftFriendRequest(
      id: request.id,
      requesterId: request.requesterId,
      recipientId: request.recipientId,
      status: request.status,
      sourceBubbleId: request.sourceBubbleId,
      autoGenerated: request.autoGenerated,
      createdAt: request.createdAt,
      updatedAt: DateTime.now(),
      cachedAt: DateTime.now(),
      isStale: false,
    );
  }

  /// Set current user ID for cache operations
  void setCurrentUserId(String userId) {
    _currentUserId = userId;
  }

  @override
  Stream<List<FriendRequest>> get pendingFriendRequestsStream {
    if (_currentUserId == null) return Stream.value([]);
    return _friendRequestDao
        .watchPendingReceivedFriendRequests(_currentUserId!)
        .map((driftRequests) => driftRequests.map(_convertFromDrift).toList());
  }

  @override
  Future<bool> get hasCachedData async {
    if (_currentUserId == null) return false;
    final requests = await _friendRequestDao.getFriendRequestsForUser(
      _currentUserId!,
    );
    return requests.isNotEmpty;
  }

  @override
  Future<void> initializeCache() async {
    if (_currentUserId == null) {
      LoggingService.warning(
        'FriendRequestRepository: Cannot initialize cache without user ID',
      );
      return;
    }
    LoggingService.info(
      'FriendRequestRepository: Cache initialized for user $_currentUserId',
    );
  }

  @override
  Future<void> fetchFromRemote() async {
    if (_currentUserId == null) {
      LoggingService.warning(
        'FriendRequestRepository: Cannot fetch from remote without user ID',
      );
      return;
    }

    try {
      LoggingService.info(
        'FriendRequestRepository: Fetching from remote for user $_currentUserId',
      );

      // Fetch pending friend requests
      final response = await _remoteDataSource.get('/friendship/requests');
      final requests = <FriendRequest>[];
      for (final requestData
          in (response['requests'] as List<dynamic>? ?? [])) {
        requests.add(
          FriendRequest.fromJson(requestData as Map<String, dynamic>),
        );
      }

      // Convert and store in Drift database
      for (final request in requests) {
        final driftRequest = _convertToDrift(request);
        await _friendRequestDao.insertOrUpdateFriendRequest(driftRequest);
      }

      LoggingService.info(
        'FriendRequestRepository: Cached ${requests.length} pending friend requests',
      );
    } catch (e) {
      LoggingService.error(
        'FriendRequestRepository: Failed to fetch from remote: $e',
      );
      rethrow;
    }
  }

  @override
  Future<void> clearCache() async {
    if (_currentUserId == null) return;

    await _friendRequestDao.clearFriendRequestsForUser(_currentUserId!);
    LoggingService.info(
      'FriendRequestRepository: Cache cleared for user $_currentUserId',
    );
  }

  @override
  Future<void> handleMqttEvent(MqttEvent event) async {
    if (event is FriendRequestEvent) {
      await processFriendRequestEvent(event);
    }
  }

  /// Process friend request MQTT events
  Future<void> processFriendRequestEvent(FriendRequestEvent event) async {
    LoggingService.info(
      'FriendRequestRepository: Processing event: ${event.action} for request ${event.requestId}',
    );

    switch (event.action) {
      case FriendRequestAction.received:
      case FriendRequestAction.auto_generated:
        // New friend request received - refresh from remote to get full details
        await fetchFromRemote();
        break;
      case FriendRequestAction.accepted:
      case FriendRequestAction.declined:
        // Update request status in database
        await _friendRequestDao.updateFriendRequestStatus(
          event.requestId,
          event.action.name,
        );
        break;
    }
  }

  @override
  Future<Result<List<FriendRequest>>> getPendingFriendRequests() async {
    if (_currentUserId == null) {
      return Result.failure(
        const NetworkException(message: 'User not authenticated'),
      );
    }

    try {
      final driftRequests = await _friendRequestDao
          .getPendingReceivedFriendRequests(_currentUserId!);
      final requests = driftRequests.map(_convertFromDrift).toList();
      return Result.success(requests);
    } catch (e) {
      LoggingService.error(
        'FriendRequestRepository: Failed to get pending friend requests: $e',
      );
      return Result.failure(
        const NetworkException(
          message: 'Failed to get pending friend requests',
        ),
      );
    }
  }

  @override
  Future<Result<void>> acceptFriendRequest(String requestId) async {
    try {
      await _remoteDataSource.post(
        '/friendship/requests/$requestId/accept',
        {},
      );

      // Update request status in Drift database
      await _friendRequestDao.updateFriendRequestStatus(requestId, 'accepted');

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        const NetworkException(message: 'Failed to accept friend request'),
      );
    }
  }

  @override
  Future<Result<void>> declineFriendRequest(String requestId) async {
    try {
      await _remoteDataSource.post(
        '/friendship/requests/$requestId/decline',
        {},
      );

      // Update request status in Drift database
      await _friendRequestDao.updateFriendRequestStatus(requestId, 'declined');

      return Result.success(null);
    } catch (e) {
      return Result.failure(
        const NetworkException(message: 'Failed to decline friend request'),
      );
    }
  }

  @override
  Future<Result<FriendRequest>> getFriendRequest(String requestId) async {
    try {
      // Check Drift database first
      final driftRequest = await _friendRequestDao.getFriendRequestById(
        requestId,
      );
      if (driftRequest != null) {
        return Result.success(_convertFromDrift(driftRequest));
      }

      // Fetch from remote if not in cache
      final response = await _remoteDataSource.get(
        '/friendship/requests/$requestId',
      );
      final request = FriendRequest.fromJson(
        response['request'] as Map<String, dynamic>,
      );

      // Store in Drift database
      final driftRequest2 = _convertToDrift(request);
      await _friendRequestDao.insertOrUpdateFriendRequest(driftRequest2);

      return Result.success(request);
    } catch (e) {
      return Result.failure(
        const NetworkException(message: 'Failed to get friend request'),
      );
    }
  }

  @override
  Future<Result<List<String>>> getFriends() async {
    // This would typically fetch from a friends repository or user profile
    // For now, return empty list as friends are managed separately
    return Result.success([]);
  }
}
