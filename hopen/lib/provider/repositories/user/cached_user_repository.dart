import 'dart:async';
import 'dart:convert';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:drift/drift.dart';

import '../../../statefulbusinesslogic/core/db/app_database.dart';
import '../../../statefulbusinesslogic/core/models/user_model.dart';
import '../../../repositories/user/user_repository.dart';

/// Repository implementing "stale-while-revalidate" caching strategy for user profiles
///
/// This repository provides:
/// 1. Immediate data from cache (Drift database)
/// 2. Background network refresh
/// 3. Automatic cache updates
/// 4. Offline support
class CachedUserRepository {
  final UserRepository _networkRepository;
  final UserProfileDao _dao;
  final Connectivity _connectivity;

  // Cache configuration
  static const Duration _stalePeriod = Duration(
    minutes: 15,
  ); // Data is stale after 15 minutes
  static const Duration _maxAge = Duration(
    days: 7,
  ); // Data expires after 7 days

  CachedUserRepository({
    required UserRepository networkRepository,
    required UserProfileDao dao,
    Connectivity? connectivity,
  }) : _networkRepository = networkRepository,
       _dao = dao,
       _connectivity = connectivity ?? Connectivity();

  /// Watch user profile with stale-while-revalidate strategy
  ///
  /// Returns a stream that:
  /// 1. Immediately emits cached data (if available)
  /// 2. Triggers background refresh
  /// 3. Emits fresh data when available
  Stream<UserModel?> watchUserProfile(String userId) async* {
    debugPrint('🔄 CachedUserRepository: Starting watch for user $userId');

    // 1. Immediately emit cached data
    final cached = await _dao.getUserProfile(userId);
    if (cached != null) {
      debugPrint('🔄 CachedUserRepository: Found cached data for user $userId');
      yield _driftUserToUserModel(cached);

      // Update last accessed time
      await _dao.updateLastAccessed(userId);
    }

    // 2. Check if we need to refresh
    final needsRefresh =
        cached == null || cached.isStale || _isDataStale(cached.lastRefreshed);

    if (needsRefresh) {
      debugPrint(
        '🔄 CachedUserRepository: Data needs refresh for user $userId',
      );

      // 3. Trigger background refresh
      _refreshUserProfile(userId)
          .then((freshUser) {
            if (freshUser != null) {
              debugPrint(
                '🔄 CachedUserRepository: Background refresh completed for user $userId',
              );
            }
          })
          .catchError((error) {
            debugPrint(
              '🔄 CachedUserRepository: Background refresh failed for user $userId: $error',
            );
          });
    }

    // 4. Watch for database changes (will emit when background refresh completes)
    await for (final updated in _dao.watchUserProfile(userId)) {
      if (updated != null) {
        yield _driftUserToUserModel(updated);
      }
    }
  }

  /// Get user profile with immediate cache return
  Future<UserModel?> getUserProfile(String userId) async {
    debugPrint('🔄 CachedUserRepository: Getting user profile for $userId');

    // 1. Try cache first
    final cached = await _dao.getUserProfile(userId);
    if (cached != null) {
      debugPrint(
        '🔄 CachedUserRepository: Found cached profile for user $userId',
      );
      await _dao.updateLastAccessed(userId);

      // 2. Trigger background refresh if needed
      if (cached.isStale || _isDataStale(cached.lastRefreshed)) {
        debugPrint(
          '🔄 CachedUserRepository: Triggering background refresh for user $userId',
        );
        _refreshUserProfile(userId);
      }

      return _driftUserToUserModel(cached);
    }

    // 3. No cache, fetch from network
    debugPrint(
      '🔄 CachedUserRepository: No cache found, fetching from network for user $userId',
    );
    return await _refreshUserProfile(userId);
  }

  /// Cache user profile immediately after authentication
  Future<void> cacheUserProfile(UserModel user) async {
    debugPrint('🔄 CachedUserRepository: Caching user profile for ${user.id}');

    try {
      // Cache the user profile immediately
      await _cacheUserProfile(user);
      debugPrint('🔄 CachedUserRepository: User profile cached for ${user.id}');
    } catch (e) {
      debugPrint(
        '🔄 CachedUserRepository: Error caching user profile for ${user.id}: $e',
      );
      rethrow;
    }
  }

  /// Force refresh user profile from network
  Future<UserModel?> _refreshUserProfile(String userId) async {
    try {
      // Check connectivity
      final connectivityResult = await _connectivity.checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        debugPrint(
          '🔄 CachedUserRepository: No internet connection, skipping refresh for user $userId',
        );
        return null;
      }

      debugPrint(
        '🔄 CachedUserRepository: Fetching fresh data from network for user $userId',
      );

      // Fetch from network
      final freshUser = await _networkRepository.getUser(userId);
      if (freshUser != null) {
        // Cache the fresh data
        await _cacheUserProfile(freshUser);
        debugPrint(
          '🔄 CachedUserRepository: Fresh data cached for user $userId',
        );
        return freshUser;
      }
    } catch (e) {
      debugPrint(
        '🔄 CachedUserRepository: Network refresh failed for user $userId: $e',
      );
      // Mark as stale so we'll try again later
      await _dao.markAsStale(userId);
    }

    return null;
  }

  /// Cache user profile in Drift database
  Future<void> _cacheUserProfile(UserModel user) async {
    final now = DateTime.now();

    final driftUser = UserProfilesCompanion(
      id: Value(user.id),
      email: Value(user.email),
      username: Value(user.username),
      firstName: Value(user.firstName),
      lastName: Value(user.lastName),
      profilePictureUrl: Value(user.profilePictureUrl),
      birthday: Value(user.birthday),
      friendIds: Value(user.friendIds.isNotEmpty ? jsonEncode(user.friendIds) : null),
      bubbleId: Value(user.bubbleId),
      contactIds: Value(
          user.contactIds.isNotEmpty ? jsonEncode(user.contactIds) : null),
      onlineStatus: Value(user.onlineStatus.toString()),
      bubbleStatus: Value(user.bubbleStatus.toString()),
      blockedUserIds: Value(
          user.blockedUserIds.isNotEmpty
              ? jsonEncode(user.blockedUserIds)
              : null),
      pendingSentContactRequestIds: Value(
          user.pendingSentContactRequestIds.isNotEmpty
              ? jsonEncode(user.pendingSentContactRequestIds)
              : null),
      pendingReceivedContactRequestIds: Value(
          user.pendingReceivedContactRequestIds.isNotEmpty
              ? jsonEncode(user.pendingReceivedContactRequestIds)
              : null),
      hasCompletedOnboarding: Value(user.hasCompletedOnboarding ?? false),
      cachedAt: Value(now),
      lastRefreshed: Value(now),
      lastAccessed: Value(now),
      isStale: const Value(false),
    );

    await _dao.into(_dao.userProfiles).insertOnConflictUpdate(driftUser);
  }

  /// Convert DriftUserProfile to UserModel
  UserModel _driftUserToUserModel(DriftUserProfile drift) {
    return UserModel(
      id: drift.id,
      email: drift.email,
      username: drift.username,
      firstName: drift.firstName,
      lastName: drift.lastName,
      profilePictureUrl: drift.profilePictureUrl,
      birthday: drift.birthday,
      friendIds:
          drift.friendIds != null
              ? List<String>.from(jsonDecode(drift.friendIds!))
              : [],
      bubbleId: drift.bubbleId,
      contactIds:
          drift.contactIds != null
              ? List<String>.from(jsonDecode(drift.contactIds!))
              : [],
      onlineStatus: OnlineStatus.values.firstWhere(
        (e) => e.toString() == drift.onlineStatus,
        orElse: () => OnlineStatus.offline,
      ),
      bubbleStatus: BubbleMembershipStatus.values.firstWhere(
        (e) => e.toString() == drift.bubbleStatus,
        orElse: () => BubbleMembershipStatus.noBubble,
      ),
      blockedUserIds:
          drift.blockedUserIds != null
              ? List<String>.from(jsonDecode(drift.blockedUserIds!))
              : [],
      pendingSentContactRequestIds:
          drift.pendingSentContactRequestIds != null
              ? List<String>.from(
                jsonDecode(drift.pendingSentContactRequestIds!),
              )
              : [],
      pendingReceivedContactRequestIds:
          drift.pendingReceivedContactRequestIds != null
              ? List<String>.from(
                jsonDecode(drift.pendingReceivedContactRequestIds!),
              )
              : [],
      hasCompletedOnboarding: drift.hasCompletedOnboarding,
    );
  }

  /// Check if data is stale based on last refresh time
  bool _isDataStale(DateTime lastRefreshed) {
    return DateTime.now().difference(lastRefreshed) > _stalePeriod;
  }

  /// Clear all cached user profiles
  Future<void> clearCache() async {
    await _dao.clearAllProfiles();
    debugPrint('🔄 CachedUserRepository: Cache cleared');
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    return await _dao.getCacheStats();
  }

  /// Background cleanup of old cache entries
  Future<void> cleanupOldCache() async {
    final cutoffTime = DateTime.now().subtract(_maxAge);
    final oldProfiles = await _dao.getProfilesNeedingRefresh(_maxAge);

    for (final profile in oldProfiles) {
      await _dao.deleteUserProfile(profile.id);
    }

    debugPrint(
      '🔄 CachedUserRepository: Cleaned up ${oldProfiles.length} old cache entries',
    );
  }
}
