import 'dart:async';

import 'package:flutter/foundation.dart'; // For kDebugMode
import 'package:flutter_webrtc/flutter_webrtc.dart'; // WebRTC package
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart'; // For generating unique call IDs

import '../../../repositories/auth/auth_repository.dart';
import '../../../repositories/bubble/bubble_repository.dart'; // Added import
import '../../../repositories/call/call_repository.dart';
import '../../../repositories/user/user_repository.dart'; // To get current user details
import '../../../statefulbusinesslogic/core/models/bubble_entity.dart'; // Updated import
import '../../../statefulbusinesslogic/core/models/user_model.dart'; // For UserModel
import '../../../statefulbusinesslogic/core/models/value_objects.dart'; // For BubbleId
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../services/call/platform_call_handler.dart';
import '../../services/mqtt/mqtt_service.dart';
import '../../services/webrtc/webrtc_service.dart';

// Use events from repository layer

class _CallContext {
  // Actual group ID if isGroupCall is true

  _CallContext({
    required this.callId,
    required this.targetUserId,
    required this.isGroupCall,
    this.groupId,
  });
  final String callId;
  final String
  targetUserId; // For 1:1, the peer. For group, could be group ID or a server routing ID.
  final bool isGroupCall;
  final String? groupId;
}

class CallRepositoryImpl implements CallRepositoryExtended {
  // Public constructor
  CallRepositoryImpl({
    // required MqttService mqttService, // TODO: Fix MQTT integration for calls
    required UserRepository userRepository,
    required BubbleRepository bubbleRepository, // Added to constructor
    required WebRTCService webrtcService,
    required AuthRepository authRepository,
  }) : // _mqttService = mqttService,
       _userRepository = userRepository,
       _bubbleRepository = bubbleRepository,
       _webrtcService = webrtcService,
       _authRepository = authRepository {
    if (kDebugMode) {
      LoggingService.error('CallRepositoryImpl: Initialized.');
    }

    // Initialize platform call handler if available
    try {
      _platformCallHandler = GetIt.instance<PlatformCallHandler>();
    } catch (e) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: PlatformCallHandler not available: $e',
        );
      }
      // Continue without platform call handler
    }

    _listenToWebSocketMessages();
  }
  // Changed to implement extended interface
  // final MqttService _mqttService; // TODO: Fix MQTT integration for calls
  final UserRepository _userRepository;
  final BubbleRepository _bubbleRepository; // Added BubbleRepository
  PlatformCallHandler? _platformCallHandler;
  final WebRTCService _webrtcService;
  final AuthRepository _authRepository;

  // ICE Server Configuration
  static const List<Map<String, String>> _iceServers = [
    {'urls': 'stun:stun.l.google.com:19302'},
    {'urls': 'stun:stun1.l.google.com:19302'},
    {'urls': 'stun:stun2.l.google.com:19302'},
    // Add TURN servers for production
    // {
    //   'urls': 'turn:your-turn-server:3478',
    //   'username': 'your-username',
    //   'credential': 'your-password',
    // },
  ];

  Map<String, dynamic> get _rtcConfiguration => {
    'iceServers': _iceServers,
    'iceCandidatePoolSize': 10,
    'bundlePolicy': 'max-bundle',
    'rtcpMuxPolicy': 'require',
  };

  // Refactored for group calls: callId maps to participantId, which maps to PC/Stream
  final Map<String, Map<String, RTCPeerConnection>> _groupPeerConnections = {};
  final Map<String, MediaStream> _localStreams =
      {}; // Still one local stream per call session
  final Map<String, Map<String, MediaStream>> _groupRemoteStreams = {};

  // _callContexts stores overall session info (e.g., if it's a group call, the group ID)
  final Map<String, _CallContext> _callContexts =
      {}; // To store context for each call

  StreamSubscription? _webSocketSubscription;
  final Uuid _uuid = const Uuid();

  // Stream Controllers for notifying CallBloc
  final _remoteStreamAddedController =
      StreamController<RemoteStreamAdded>.broadcast();
  final _iceConnectionStateChangedController =
      StreamController<IceConnectionStateChanged>.broadcast();
  final _callErrorOccurredController =
      StreamController<CallErrorOccurred>.broadcast();
  final _callOutcomeOccurredController =
      StreamController<CallOutcomeOccurred>.broadcast();
  final _joinCallAcknowledgedController =
      StreamController<JoinCallAcknowledged>.broadcast();
  final _joinCallFailedController =
      StreamController<JoinCallFailed>.broadcast();
  final _incomingOfferOccurredController =
      StreamController<IncomingOfferOccurred>.broadcast();

  // Public streams for CallBloc to listen to
  @override
  Stream<RemoteStreamAdded> get onRemoteStreamAdded =>
      _remoteStreamAddedController.stream;
  @override
  Stream<IceConnectionStateChanged> get onIceConnectionStateChanged =>
      _iceConnectionStateChangedController.stream;
  @override
  Stream<CallErrorOccurred> get onCallErrorOccurred =>
      _callErrorOccurredController.stream;
  @override
  Stream<CallOutcomeOccurred> get onCallOutcomeOccurred =>
      _callOutcomeOccurredController.stream;
  @override
  Stream<JoinCallAcknowledged> get onJoinCallAcknowledged =>
      _joinCallAcknowledgedController.stream;
  @override
  Stream<JoinCallFailed> get onJoinCallFailed =>
      _joinCallFailedController.stream;
  @override
  Stream<IncomingOfferOccurred> get onIncomingOfferOccurred =>
      _incomingOfferOccurredController.stream;

  // Missing fields that are referenced in the code
  final Map<String, Set<String>> _activeGroupParticipants = {};

  // Missing methods that are referenced in the code
  Future<void> _sendCallMessage(
    String targetUserId,
    Map<String, dynamic> message,
  ) async {
    try {
      // await _webSocketService.sendMessage('user_$targetUserId', jsonEncode(message));
      LoggingService.error(
        'Would send call message to $targetUserId: $message',
      );
    } catch (e) {
      LoggingService.error('Error sending call message: $e');
    }
  }

  void _listenToWebSocketMessages() {
    // TODO: Fix MQTT integration for calls
    /*
    _webSocketSubscription?.cancel();
    _webSocketSubscription = _mqttService.messageStream.listen(
      (message) {
        if (kDebugMode) {
          LoggingService.error(
            'CallRepositoryImpl (WS Listener): Received message: $message',
          );
        }
        final type = message['type'] as String?;
        final callId = message['call_id'] as String?;
        final callContext = _callContexts[callId];

        if (callId == null ||
            _groupPeerConnections[callId] == null ||
            callContext == null) {
          if (kDebugMode) {
            LoggingService.error(
              'CallRepositoryImpl (WS Listener): Ignoring message for unknown, inactive, or context-less callId: $callId',
            );
          }
          return;
        }

        switch (type) {
          case 'call_answer':
            _handleAnswer(callId, message);
            break;
          case 'ice_candidate':
            _handleRemoteCandidate(callId, message);
            break;
          case 'call_rejected':
            _handleCallRejected(callId, message);
            break;
          case 'call_ended':
            _handleCallEnded(callId, message);
            break;
          case 'join_call_ack':
            _handleJoinCallAck(callId, message);
            break;
          case 'join_call_failed':
            _handleJoinCallFailed(callId, message);
            break;
          case 'call_offer':
            _handleOffer(callId, message);
            break;
          default:
            if (kDebugMode) {
              LoggingService.error(
                'CallRepositoryImpl (WS Listener): Received unhandled message type: $type',
              );
            }
        }
      },
      onError: (error) {
        if (kDebugMode) {
          LoggingService.error(
            'CallRepositoryImpl (WS Listener): Error on WebSocket stream: $error',
          );
        }
      },
      onDone: () {
        if (kDebugMode) {
          LoggingService.error(
            'CallRepositoryImpl (WS Listener): WebSocket stream closed.',
          );
        }
      },
    );
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: Subscribed to WebSocket messages stream.',
      );
    }
    */
  }

  Future<RTCPeerConnection> _createPeerConnection(
    String callId,
    String participantId, // The ID of the peer this connection is for
    String
    signalingTargetId, // The ID to send signaling messages (candidates, etc.) to for this PC
  ) async {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: Creating PeerConnection for call $callId, participant $participantId, signalingTarget: $signalingTargetId',
      );
    }
    final pc = await createPeerConnection(_rtcConfiguration, {});
    _groupPeerConnections.putIfAbsent(callId, () => {})[participantId] = pc;

    pc.onIceCandidate = (candidate) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: onIceCandidate for call $callId, participant $participantId: ${candidate.toMap()}',
        );
      }
      // Use signalingTargetId from the context captured when connection was initiated/accepted
      // final callContext = _callContexts[callId]; // callContext might not have specific participant signaling target
      // if (callContext != null) { // This check might be too broad for participant-specific signaling
      _mqttService.sendCandidate(
        callId,
        signalingTargetId, // Send to the correct target for this PC
        candidate.toMap(),
      );
      // } else {
      //   if (kDebugMode) {
      //     LoggingService.error(
      //       "CallRepositoryImpl: Could not find call context for $callId to send ICE candidate for participant $participantId.",
      //     );
      //   }
      // }
    };

    pc.onIceConnectionState = (state) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: onIceConnectionState for call $callId, participant $participantId: $state',
        );
      }
      _iceConnectionStateChangedController.add(
        IceConnectionStateChanged(
          callId: callId,
          participantId: participantId,
          state: state,
        ),
      );
    };

    pc.onTrack = (event) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: onTrack for call $callId, participant $participantId: ${event.streams}',
        );
      }
      if (event.streams.isNotEmpty) {
        final stream = event.streams[0];
        _groupRemoteStreams.putIfAbsent(callId, () => {})[participantId] =
            stream;
        _remoteStreamAddedController.add(
          RemoteStreamAdded(
            callId: callId,
            participantId: participantId,
            stream: stream,
          ),
        );
      }
    };
    pc.onConnectionState = (state) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: onConnectionState for call $callId, participant $participantId: $state',
        );
      }
      if (state == RTCPeerConnectionState.RTCPeerConnectionStateFailed) {
        _callOutcomeOccurredController.add(
          CallOutcomeOccurred(callId: callId, outcome: 'failed'),
        );
        // _cleanupCall(callId); // Cleanup for entire call might be too much if one PC fails
        // Consider cleaning up only this participant's connection: _cleanupParticipantConnection(callId, participantId);
      } else if (state ==
              RTCPeerConnectionState.RTCPeerConnectionStateDisconnected ||
          state == RTCPeerConnectionState.RTCPeerConnectionStateClosed) {
        // Consider cleaning up only this participant's connection
        // _cleanupParticipantConnection(callId, participantId);
      }
    };
    return pc;
  }

  Future<MediaStream> _getLocalMedia(bool enableVideo, bool enableAudio) async {
    final mediaConstraints = <String, dynamic>{
      'audio': enableAudio,
      'video': enableVideo ? {'facingMode': 'user'} : false,
    };
    final stream = await navigator.mediaDevices.getUserMedia(mediaConstraints);
    return stream;
  }

  void _cleanupCall(String callId) {
    if (kDebugMode) {
      LoggingService.error('CallRepositoryImpl: Cleaning up call $callId');
    }
    // Clean up local stream for this call session
    _localStreams[callId]?.getTracks().forEach((track) => track.stop());
    _localStreams[callId]?.dispose();
    _localStreams.remove(callId);

    // Clean up all peer connections and remote streams associated with this callId
    final participantConnections = _groupPeerConnections.remove(callId);
    participantConnections?.forEach((participantId, pc) {
      pc.close();
      final remoteStream = _groupRemoteStreams[callId]?.remove(participantId);
      remoteStream?.getTracks().forEach((track) => track.stop());
      remoteStream?.dispose();
    });
    _groupRemoteStreams.remove(
      callId,
    ); // Clean up the entry for the callId itself

    _callContexts.remove(callId);
    // Note: _peerConnections map was removed/renamed to _groupPeerConnections
  }

  @override
  Future<Map<String, dynamic>> initiateCall({
    required String targetId,
    required bool isGroup,
    required bool withVideo,
    required bool withScreenShare,
  }) async {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: initiateCall to target: $targetId, isGroup: $isGroup, video: $withVideo, screenShare: $withScreenShare',
      );
    }
    final callId = _uuid.v4();
    _callContexts[callId] = _CallContext(
      callId: callId,
      targetUserId: targetId,
      isGroupCall: isGroup,
      groupId: isGroup ? targetId : null,
    );

    final pc = await _createPeerConnection(callId, targetId, targetId);
    _groupPeerConnections[callId]![targetId] = pc;

    MediaStream? localStream;
    if (withVideo || !withScreenShare) {
      try {
        localStream = await navigator.mediaDevices.getUserMedia({
          'audio': true,
          'video': withVideo,
        });
        _localStreams[callId] = localStream;
        localStream.getTracks().forEach((track) {
          pc.addTrack(track, localStream!);
        });
      } catch (e) {
        if (kDebugMode) {
          LoggingService.error(
            'CallRepositoryImpl: Error getting user media for call: $e',
          );
        }
        _callErrorOccurredController.add(
          CallErrorOccurred(
            callId: callId,
            error: e,
            stackTrace: StackTrace.current,
          ),
        );
        throw Exception('Could not access camera/microphone: $e');
      }
    }

    if (withScreenShare) {
      try {
        final screenStream = await navigator.mediaDevices.getDisplayMedia({
          'video': true,
          'audio': false,
        });
        if (localStream == null) {
          localStream = screenStream;
          _localStreams[callId] = localStream;
          localStream.getTracks().forEach((track) {
            pc.addTrack(track, localStream!);
          });
        } else {
          screenStream.getTracks().forEach((track) {
            pc.addTrack(track, screenStream);
          });
        }
        if (kDebugMode) {
          LoggingService.error(
            'CallRepositoryImpl: Screen sharing stream obtained and tracks added.',
          );
        }
      } catch (e) {
        if (kDebugMode) {
          LoggingService.error(
            'CallRepositoryImpl: Error getting screen sharing media: $e',
          );
        }
        _callErrorOccurredController.add(
          CallErrorOccurred(
            callId: callId,
            error: e,
            stackTrace: StackTrace.current,
          ),
        );
        throw Exception('Could not access screen sharing media: $e');
      }
    }

    if (localStream == null && !withScreenShare) {
      _cleanupCall(callId);
      throw Exception(
        'Cannot initiate call without video/audio or screen share.',
      );
    }

    try {
      final offer = await pc.createOffer();
      await pc.setLocalDescription(offer);

      // final currentUser = await _userRepository.getUser(
      //   _userRepository.currentUserId!,
      // );
      BubbleEntity? bubble;
      if (isGroup) {
        final bubbleResult = await _bubbleRepository.getBubbleDetailsById(
          BubbleId(targetId),
        );
        bubbleResult.fold(
          onSuccess: (bubbleEntity) {
            // Use the BubbleEntity directly
            bubble = bubbleEntity;
          },
          onFailure: (error) {
            LoggingService.error('Error getting bubble details: $error');
            bubble = null;
          },
        );
      }

      _mqttService.sendCallOffer(callId, targetId, offer.toMap());
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Offer sent for call $callId to target $targetId',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error creating/sending offer: $e',
        );
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(
          callId: callId,
          error: e,
          stackTrace: StackTrace.current,
        ),
      );
      _cleanupCall(callId);
      throw Exception('Failed to create or send call offer: $e');
    }

    return {'callId': callId, 'localStream': localStream};
  }

  @override
  Future<Map<String, dynamic>> joinCall({
    required String bubbleId,
    required String callId,
    required bool withVideo,
    required bool withScreenShare,
  }) async {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: joinCall for bubble: $bubbleId, callId: $callId, video: $withVideo, screenShare: $withScreenShare',
      );
    }

    // Check if already in this call or another active call
    final isInThisCall = _groupPeerConnections.containsKey(callId);
    final isInAnotherCall = _callContexts.values.any(
      (ctx) =>
          ctx.callId != callId && _groupPeerConnections.containsKey(ctx.callId),
    );

    if (isInThisCall || isInAnotherCall) {
      final activeCallId =
          isInThisCall
              ? callId
              : _callContexts.values
                  .firstWhere(
                    (ctx) =>
                        ctx.callId != callId &&
                        _groupPeerConnections.containsKey(ctx.callId),
                  )
                  .callId;
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Already in call $activeCallId. Cannot join another call ($callId).',
        );
      }
      throw Exception(
        'Already in call $activeCallId. Cannot join another call.',
      );
    }

    // It's important to note that when joining, we don't create a new PeerConnection immediately.
    // The server will likely send us offers from existing participants, or we might need to send an offer-like "join_request".
    // This placeholder assumes the client will receive offers after signaling its intent to join.
    // For a real implementation, this would involve:
    // 1. Sending a "join_request" message to the server via WebSocket:
    //    _webSocketService.sendJoinRequest(
    //        callId: callId,
    //        bubbleId: bubbleId,
    //        userId: _userRepository.currentUserId!, // Ensure current user ID is available
    //        withVideo: withVideo,
    //        withScreenShare: withScreenShare,
    //    );
    // 2. The server would then orchestrate connecting this user to existing participants,
    //    likely by sending offers from them to this client, which would be handled by
    //    the existing `_handleOffer` (if we adapt it from `ReceiveIncomingCallEvent`) or a new message handler.
    // 3. This client would then create PeerConnections for each existing participant.

    // For this placeholder, we will simulate getting local media as if preparing for a call.
    // The actual WebRTC setup for joining will be more complex.

    _callContexts[callId] = _CallContext(
      callId: callId,
      targetUserId: bubbleId, // For group calls, target is the bubble/group
      isGroupCall: true,
      groupId: bubbleId,
    );

    MediaStream? localStream;
    try {
      localStream = await navigator.mediaDevices.getUserMedia({
        'audio': true, // Always join with audio capability
        'video': withVideo,
      });
      _localStreams[callId] = localStream;
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl (joinCall): Local media stream obtained.',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl (joinCall): Error getting user media: $e',
        );
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(
          callId: callId,
          error: e,
          stackTrace: StackTrace.current,
        ),
      );
      _cleanupCall(callId); // Clean up context if media fails
      throw Exception('Could not access camera/microphone for joining: $e');
    }

    // Screen sharing for joining (simplified)
    if (withScreenShare) {
      try {
        final screenStream = await navigator.mediaDevices.getDisplayMedia({
          'video': true,
          'audio':
              false, // Typically screen share doesn't capture audio this way
        });

        // If also sharing video, decide how to handle two video tracks.
        // For simplicity, if screen sharing is enabled, its video track might replace the camera track
        // or be added as a separate stream/track if the signaling and UI support it.
        // Here, we'll add its tracks to the existing localStream or create a new one.
        // This part needs careful consideration based on how multi-stream is handled.
        // For this placeholder, let's assume screen share stream might become the primary video.

        // Add screen tracks to the local stream or replace video tracks
        // This is a simplified approach. A robust solution might involve multiple transceivers.
        localStream.getVideoTracks().forEach(
          (track) => track.stop(),
        ); // Stop existing video tracks
        screenStream.getVideoTracks().forEach((track) {
          localStream?.addTrack(track);
        });

        _localStreams[callId] = localStream; // Update the stream reference

        if (kDebugMode) {
          LoggingService.error(
            'CallRepositoryImpl (joinCall): Screen sharing stream obtained and tracks added/replaced.',
          );
        }
      } catch (e) {
        if (kDebugMode) {
          LoggingService.error(
            'CallRepositoryImpl (joinCall): Error getting screen sharing media: $e',
          );
        }
        // Non-fatal for joining if screen share fails but camera works.
        // Or, could throw if screen share is mandatory. For now, log and continue.
        _callErrorOccurredController.add(
          CallErrorOccurred(
            callId: callId,
            error: 'Screen share failed: $e',
            stackTrace: StackTrace.current,
          ),
        );
      }
    }

    // At this point, the client has local media and has registered its intent (conceptually).
    // It now waits for the server to send offers from other participants or further instructions.
    // The actual _createPeerConnection and offer/answer exchange for *each* other participant
    // would happen in response to WebSocket messages from the server.

    final currentUserId = _userRepository.currentUserId;
    if (currentUserId == null) {
      _cleanupCall(callId);
      throw Exception('Current user ID not found. Cannot send join request.');
    }

    try {
      _mqttService.sendJoinRequest(bubbleId, currentUserId);
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl (joinCall): Sent join_request for callId: $callId, BubbleID: $bubbleId, UserId: $currentUserId',
        );
      }
    } catch (e, s) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl (joinCall): Error sending join_request: $e\n$s',
        );
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(
          callId: callId,
          error: 'Failed to send join request: $e',
          stackTrace: s,
        ),
      );
      _cleanupCall(callId);
      throw Exception('Failed to send join request: $e');
    }

    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl (joinCall): Placeholder logic complete. Waiting for server messages to establish connections with participants in callId: $callId, BubbleID: $bubbleId',
      );
    }

    return {'callId': callId, 'localStream': localStream};
  }

  @override
  Future<void> acceptCall({
    required String callId,
    required bool withVideo,
    required Map<String, dynamic> remoteOfferSdp,
    required String callerId,
    required bool isGroupCall,
    String? groupId,
  }) async {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: acceptCall for callId: $callId, withVideo: $withVideo, from caller: $callerId, isGroup: $isGroupCall, groupId: $groupId',
      );
    }

    if (_groupPeerConnections.containsKey(callId)) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: PeerConnection already exists for call $callId. Ignoring acceptCall.',
        );
      }
      // Potentially an error or specific handling if trying to accept an already handled call.
      return;
    }

    // Store context for the call we are accepting
    _callContexts[callId] = _CallContext(
      callId: callId,
      targetUserId: callerId,
      isGroupCall: isGroupCall,
      groupId: groupId,
    );

    final pc = await _createPeerConnection(callId, callerId, callerId);
    _groupPeerConnections[callId]![callerId] = pc;

    MediaStream? localStream;
    try {
      localStream = await navigator.mediaDevices.getUserMedia({
        'audio': true, // Always accept with audio enabled
        'video': withVideo,
      });
      _localStreams[callId] = localStream;
      localStream.getTracks().forEach((track) {
        pc.addTrack(track, localStream!);
      });
    } catch (e) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error getting user media for acceptCall: $e',
        );
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(
          callId: callId,
          error: e,
          stackTrace: StackTrace.current,
        ),
      );
      _cleanupCall(callId);
      throw Exception('Could not access camera/microphone to accept call: $e');
    }

    try {
      await pc.setRemoteDescription(
        RTCSessionDescription(
          remoteOfferSdp['sdp'] as String,
          remoteOfferSdp['type'] as String,
        ),
      );

      final answer = await pc.createAnswer();
      await pc.setLocalDescription(answer);

      // Send answer via WebSocket
      _mqttService.sendAnswer(
        callId,
        callerId, // Send answer back to the original caller
        answer.toMap(),
        // isVideoEnabled: withVideo, // User's current video status when answering
      );
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Answer sent for call $callId to caller $callerId',
        );
      }

      // Notify BLoC that local and remote streams might be ready (or will be soon)
      // The BLoC will transition to active state upon receiving remote stream via onTrack
      // For now, we can return the local stream. The remote stream will come via onTrack.
      // The _remoteStreamAddedController.add is triggered by pc.onTrack.
    } catch (e) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error processing offer/creating answer: $e',
        );
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(
          callId: callId,
          error: e,
          stackTrace: StackTrace.current,
        ),
      );
      _cleanupCall(callId);
      throw Exception('Failed to process offer or send answer: $e');
    }
    // The method is void, but BLoC will listen for stream events.
    // No explicit return value needed here to signal success to BLoC for accept,
    // as streams setup and state changes are event-driven via repository streams.
  }

  Future<void> _handleAnswer(
    String callId,
    Map<String, dynamic> message,
  ) async {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: Handling call_answer for $callId: $message',
      );
    }

    try {
      final mediaInfo = message['media_info'] as Map<String, dynamic>?;
      if (mediaInfo == null) {
        throw const FormatException(
          'Missing media_info in call_answer message',
        );
      }

      final sdp = mediaInfo['sdp'] as String?;
      if (sdp == null) {
        throw const FormatException(
          'Missing or invalid SDP in call_answer message',
        );
      }
      const type = 'answer'; // SDP type for an answer is always 'answer'

      final responderId = message['responder_id'] as String?;
      if (responderId == null) {
        throw FormatException(
          'Missing responder_id in call_answer message for call $callId',
        );
      }

      final pc = _groupPeerConnections[callId]?[responderId];
      if (pc == null) {
        throw StateError(
          'No PeerConnection found for call $callId, participant $responderId',
        );
      }

      await pc.setRemoteDescription(RTCSessionDescription(sdp, type));
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Remote description (answer) set for $callId, participant $responderId',
        );
      }
    } catch (e, s) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error handling call_answer for $callId: $e',
        );
        LoggingService.error(s.toString());
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(callId: callId, error: e, stackTrace: s),
      );
    }
  }

  Future<void> _handleRemoteCandidate(
    String callId,
    Map<String, dynamic> message,
  ) async {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: Handling ice_candidate for $callId: $message',
      );
    }

    try {
      final candidateInfo = message['candidate_info'] as Map<String, dynamic>?;
      if (candidateInfo == null) {
        throw const FormatException(
          'Missing candidate_info in ice_candidate message',
        );
      }

      final senderId = message['sender_id'] as String?;
      if (senderId == null) {
        throw FormatException(
          'Missing sender_id in ice_candidate message for call $callId',
        );
      }

      final pc = _groupPeerConnections[callId]?[senderId];
      if (pc == null) {
        throw StateError(
          'No PeerConnection found for call $callId, participant $senderId',
        );
      }

      // Validate all required fields
      final candidateSdp = candidateInfo['candidate'] as String?;
      final sdpMid = candidateInfo['sdpMid'] as String?;
      final sdpMLineIndex = candidateInfo['sdpMLineIndex'] as int?;

      if (candidateSdp == null || sdpMid == null || sdpMLineIndex == null) {
        throw const FormatException(
          'Incomplete ICE candidate data. Missing one or more required fields: candidate, sdpMid, sdpMLineIndex',
        );
      }

      final candidate = RTCIceCandidate(candidateSdp, sdpMid, sdpMLineIndex);
      await pc.addCandidate(candidate);

      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Remote ICE candidate added for $callId',
        );
      }
    } catch (e, s) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error handling ice_candidate for $callId: $e',
        );
        LoggingService.error(s.toString());
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(callId: callId, error: e, stackTrace: s),
      );
    }
  }

  void _handleCallRejected(String callId, Map<String, dynamic> message) {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: Call $callId was rejected. Message: $message',
      );
    }

    if (_groupPeerConnections.containsKey(callId)) {
      try {
        _callOutcomeOccurredController.add(
          CallOutcomeOccurred(callId: callId, outcome: 'rejected_by_remote'),
        );
        _cleanupCall(callId);
      } catch (e, s) {
        if (kDebugMode) {
          LoggingService.error(
            'CallRepositoryImpl: Error handling call rejection: $e',
          );
          LoggingService.error(s.toString());
        }
        _callErrorOccurredController.add(
          CallErrorOccurred(callId: callId, error: e, stackTrace: s),
        );
        _cleanupCall(callId); // Still cleanup even if handling failed
      }
    } else {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Ignoring call_rejected for unknown callId: $callId',
        );
      }
    }
  }

  void _handleCallEnded(String callId, Map<String, dynamic> message) {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: Handling call_ended for call $callId',
      );
    }

    if (_groupPeerConnections.containsKey(callId)) {
      try {
        _callOutcomeOccurredController.add(
          CallOutcomeOccurred(callId: callId, outcome: 'ended_by_remote'),
        );
        _cleanupCall(callId);
      } catch (e, s) {
        if (kDebugMode) {
          LoggingService.error(
            'CallRepositoryImpl: Error handling call_ended for $callId: $e',
          );
          LoggingService.error(s.toString());
        }
        _callErrorOccurredController.add(
          CallErrorOccurred(callId: callId, error: e, stackTrace: s),
        );
        _cleanupCall(callId); // Still cleanup even if handling failed
      }
    } else {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Ignoring call_ended for unknown callId: $callId',
        );
      }
    }
  }

  void _handleJoinCallAck(String callId, Map<String, dynamic> message) {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: Handling join_call_ack for $callId: $message',
      );
    }
    try {
      final participantsData = message['participants'] as List<dynamic>? ?? [];
      final participantMaps =
          participantsData.map((p) => p as Map<String, dynamic>).toList();

      // Convert maps to UserModel objects
      final participants =
          participantMaps
              .map(
                (map) => UserModel(
                  id: map['id'] as String? ?? '',
                  username: map['username'] as String?,
                  firstName: map['firstName'] as String?,
                  lastName: map['lastName'] as String?,
                  email: map['email'] as String?,
                  profilePictureUrl: map['profilePictureUrl'] as String?,
                ),
              )
              .toList();

      _joinCallAcknowledgedController.add(
        JoinCallAcknowledged(callId: callId, participants: participants),
      );
    } catch (e, s) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error handling join_call_ack for $callId: $e\n$s',
        );
      }
      // Notify of error or use a generic failure for this specific message
      _callErrorOccurredController.add(
        CallErrorOccurred(
          callId: callId,
          error: 'Failed to parse join_call_ack: $e',
          stackTrace: s,
        ),
      );
    }
  }

  void _handleJoinCallFailed(String callId, Map<String, dynamic> message) {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: Handling join_call_failed for $callId: $message',
      );
    }
    try {
      final reason = message['reason'] as String? ?? 'Unknown reason';
      _joinCallFailedController.add(
        JoinCallFailed(callId: callId, reason: reason),
      );
      _cleanupCall(
        callId,
      ); // If join failed, cleanup any context set up for this callId
    } catch (e, s) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error handling join_call_failed for $callId: $e\n$s',
        );
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(
          callId: callId,
          error: 'Failed to parse join_call_failed: $e',
          stackTrace: s,
        ),
      );
      _cleanupCall(callId); // Ensure cleanup
    }
  }

  @override
  Future<void> rejectCall({
    required String callId,
    String? targetUserId,
  }) async {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: rejectCall $callId. Target: $targetUserId',
      );
    }

    try {
      // targetUserId is the original caller. If not available (e.g. from CallContext),
      // we might not be able to send WS message, but should still clean up.
      final callContext = _callContexts[callId];
      final actualTargetUserId = targetUserId ?? callContext?.targetUserId;

      if (actualTargetUserId == null) {
        throw StateError(
          'No target user ID available for call $callId. Cannot send reject message.',
        );
      }

      _mqttService.sendRejectCall(callId, actualTargetUserId);
    } catch (e, s) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error rejecting call $callId: $e',
        );
        LoggingService.error(s.toString());
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(callId: callId, error: e, stackTrace: s),
      );
    } finally {
      _cleanupCall(
        callId,
      ); // Always cleanup, even if sending reject message failed
    }
  }

  @override
  Future<void> endCall({required String callId, String? targetUserId}) async {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: endCall $callId. Target: $targetUserId',
      );
    }

    try {
      final callContext = _callContexts[callId];
      // For endCall, targetUserId is the *other* party.
      // If initiating end, context.targetUserId is the one.
      // If ending due to remote, context is already cleared by _handleCallEnded.
      final actualTargetUserId = targetUserId ?? callContext?.targetUserId;

      if (actualTargetUserId == null) {
        if (_groupPeerConnections.containsKey(callId)) {
          throw StateError(
            'No target user ID available for active call $callId. Cannot send end message.',
          );
        }
        // If no peer connection exists, this might be a cleanup call for an already ended call
        return;
      }

      _mqttService.sendEndCall(callId, actualTargetUserId);
    } catch (e, s) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error ending call $callId: $e',
        );
        LoggingService.error(s.toString());
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(callId: callId, error: e, stackTrace: s),
      );
    } finally {
      _cleanupCall(
        callId,
      ); // Always cleanup, even if sending end message failed
    }
  }

  @override
  Future<void> setMuted(String callId, {required bool muted}) async {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: setMuted $callId, muted: $muted',
      );
    }
    try {
      final localStream = _localStreams[callId];
      if (localStream == null) {
        throw StateError('No local stream found for call $callId');
      }
      final audioTracks = localStream.getAudioTracks();
      if (audioTracks.isEmpty) {
        throw StateError(
          'No audio tracks found in local stream for call $callId',
        );
      }
      for (final track in audioTracks) {
        track.enabled = !muted;
      }
    } catch (e, s) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error setting mute state for call $callId: $e',
        );
        LoggingService.error(s.toString());
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(callId: callId, error: e, stackTrace: s),
      );
      rethrow;
    }
  }

  @override
  Future<void> setVideoEnabled(String callId, {required bool enabled}) async {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: setVideoEnabled $callId, enabled: $enabled',
      );
    }
    try {
      final localStream = _localStreams[callId];
      if (localStream == null) {
        throw StateError('No local stream found for call $callId');
      }
      final videoTracks = localStream.getVideoTracks();
      if (videoTracks.isEmpty) {
        throw StateError(
          'No video tracks found in local stream for call $callId',
        );
      }
      for (final track in videoTracks) {
        track.enabled = enabled;
      }
    } catch (e, s) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error setting video state for call $callId: $e',
        );
        LoggingService.error(s.toString());
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(callId: callId, error: e, stackTrace: s),
      );
      rethrow;
    }
  }

  @override
  Future<void> switchCamera(String callId) async {
    if (kDebugMode) {
      LoggingService.error('CallRepositoryImpl: switchCamera $callId');
    }

    try {
      final localStream = _localStreams[callId];
      if (localStream == null) {
        throw StateError('No local stream found for call $callId');
      }

      final videoTrack = localStream.getVideoTracks().firstOrNull;
      if (videoTrack == null) {
        throw StateError(
          'No video track found in local stream for call $callId',
        );
      }

      await Helper.switchCamera(videoTrack);
    } catch (e, s) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error switching camera for call $callId: $e',
        );
        LoggingService.error(s.toString());
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(callId: callId, error: e, stackTrace: s),
      );
      rethrow;
    }
  }

  @override
  void dispose() {
    if (kDebugMode) {
      LoggingService.error('CallRepositoryImpl: Disposing...');
    }
    _webSocketSubscription?.cancel();
    _groupPeerConnections.keys.toList().forEach(
      _cleanupCall,
    ); // .toList() to avoid concurrent modification
    _groupPeerConnections.clear();
    _localStreams.clear();
    _groupRemoteStreams.clear();
    _callContexts.clear();

    // Close stream controllers
    _remoteStreamAddedController.close();
    _iceConnectionStateChangedController.close();
    _callErrorOccurredController.close();
    _callOutcomeOccurredController.close();
    _joinCallAcknowledgedController.close();
    _joinCallFailedController.close();
    _incomingOfferOccurredController.close();
  }

  Future<void> _handleOffer(String callId, Map<String, dynamic> message) async {
    if (kDebugMode) {
      LoggingService.error(
        'CallRepositoryImpl: Handling call_offer for $callId: $message',
      );
    }

    try {
      final remoteOfferSdpMap =
          message['media_info']?['sdp'] as Map<String, dynamic>?;
      final offeringParticipantId = message['caller_info']?['id'] as String?;
      final offeringParticipantName =
          message['caller_info']?['name'] as String?;
      final offeringParticipantAvatar =
          message['caller_info']?['avatar_url'] as String?;
      final isVideoOfferedByParticipant =
          message['media_info']?['video_offered'] as bool? ?? false;

      if (remoteOfferSdpMap == null || offeringParticipantId == null) {
        throw const FormatException(
          'Incomplete call_offer message: missing media_info.sdp or caller_info.id',
        );
      }
      // Ensure sdp and type are strings within remoteOfferSdpMap
      final sdpString = remoteOfferSdpMap['sdp'] as String?;
      final sdpType = remoteOfferSdpMap['type'] as String?;
      if (sdpString == null || sdpType == null) {
        throw const FormatException(
          'Incomplete SDP information in call_offer message.',
        );
      }
      final remoteOfferSdp = RTCSessionDescription(sdpString, sdpType);

      final callContext = _callContexts[callId];

      // Scenario 1: Offer from an existing participant in a group call we are currently joining or are part of.
      if (callContext != null &&
          callContext.isGroupCall &&
          !(_groupPeerConnections[callId]?.containsKey(offeringParticipantId) ??
              false)) {
        if (kDebugMode) {
          LoggingService.error(
            'CallRepositoryImpl: Offer from existing participant $offeringParticipantId for group call $callId. Current PCs for call: ${_groupPeerConnections[callId]?.keys.toList()}',
          );
        }

        final pc = await _createPeerConnection(
          callId,
          offeringParticipantId,
          offeringParticipantId,
        );

        var localStream = _localStreams[callId];
        if (localStream == null) {
          if (kDebugMode) {
            LoggingService.error(
              'CallRepositoryImpl: Warning - localStream is null when handling group offer. Re-acquiring default.',
            );
          }
          localStream = await _getLocalMedia(true, true);
          _localStreams[callId] = localStream;
        }

        localStream.getTracks().forEach((track) {
          pc.addTrack(track, localStream!);
        });

        await pc.setRemoteDescription(remoteOfferSdp);

        final answer = await pc.createAnswer();
        await pc.setLocalDescription(answer);

        // final acceptingWithVideo =
        _localStreams[callId]?.getVideoTracks().any((t) => t.enabled) ?? false;

        _mqttService.sendAnswer(
          callId,
          offeringParticipantId,
          answer.toMap(),
          // isVideoEnabled: acceptingWithVideo,
        );
      } else if (!_groupPeerConnections.containsKey(callId) &&
          callContext == null) {
        // Scenario 2: Offer for a brand NEW call to us (no existing PC or context for this callId).
        if (kDebugMode) {
          LoggingService.error(
            'CallRepositoryImpl: Offer for a new call $callId from $offeringParticipantId.',
          );
        }

        final targetInfo = message['target_info'] as Map<String, dynamic>?;
        final isActuallyGroupCall = targetInfo?['type'] == 'bubble';

        String? groupId;
        String? groupName;
        String? groupAvatarUrl;

        if (isActuallyGroupCall && targetInfo != null) {
          groupId = targetInfo['id'] as String?;
          groupName = targetInfo['name'] as String?;
          groupAvatarUrl = targetInfo['avatar_url'] as String?;
        }
        // If not isActuallyGroupCall or targetInfo is null, they remain null.

        _incomingOfferOccurredController.add(
          IncomingOfferOccurred(
            callId: callId,
            callerId: offeringParticipantId,
            callerName: offeringParticipantName ?? 'Unknown Caller',
            callerAvatar: offeringParticipantAvatar,
            isGroup: isActuallyGroupCall,
            isVideoOffered: isVideoOfferedByParticipant,
            isAudioOffered:
                message['media_info']?['audio_offered'] as bool? ?? true,
            isScreenShareOffered:
                message['media_info']?['screen_share_offered'] as bool? ??
                false,
            groupId: groupId,
            groupName: groupName,
            groupAvatarUrl: groupAvatarUrl,
            remoteOfferSdp: sdpString, // Pass the SDP string
          ),
        );
      } else {
        if (kDebugMode) {
          final reasons = <String>[];
          if (callContext == null &&
              _groupPeerConnections.containsKey(callId)) {
            reasons.add('Context null but PCs exist.');
          }
          if (callContext != null &&
              !_groupPeerConnections.containsKey(callId)) {
            reasons.add('Context exists but no PCs.');
          }
          if (callContext != null &&
              _groupPeerConnections.containsKey(callId) &&
              _groupPeerConnections[callId]!.containsKey(
                offeringParticipantId,
              )) {
            reasons.add('Already connected to this participant.');
          }

          if (reasons.isNotEmpty) {
            LoggingService.error(
              "Ignoring call_offer for $callId from $offeringParticipantId: ${reasons.join(' ')}",
            );
          } else {
            LoggingService.error(
              'Ignoring call_offer for $callId from $offeringParticipantId: Undetermined reason or fallthrough.',
            );
          }
        }
      }
    } catch (e, s) {
      if (kDebugMode) {
        LoggingService.error(
          'CallRepositoryImpl: Error handling call_offer for $callId: $e\n$s',
        );
      }
      _callErrorOccurredController.add(
        CallErrorOccurred(
          callId: callId,
          error: 'Error processing offer: $e',
          stackTrace: s,
        ),
      );
    }
  }

  Future<void> _handleParticipantJoin(
    String callId,
    String participantId,
    String participantName,
  ) async {
    try {
      // Clean up peer connection
      final peerConnections = _groupPeerConnections[callId];
      if (peerConnections != null) {
        final pc = peerConnections[participantId];
        if (pc != null) {
          await pc.close();
          peerConnections.remove(participantId);
        }
      }

      // Remove from active participants
      final participants = _activeGroupParticipants[callId];
      if (participants != null) {
        participants.add(participantId);
      }

      // Emit participant joined event
      LoggingService.info('Participant $participantId joined call $callId');
    } catch (e) {
      LoggingService.error('Error adding group participant: $e');
      _callErrorOccurredController.add(
        CallErrorOccurred(
          callId: callId,
          error: 'Failed to add participant: $e',
          stackTrace: StackTrace.current,
        ),
      );
    }
  }

  Future<void> _handleParticipantLeave(
    String callId,
    String participantId,
  ) async {
    try {
      // Clean up peer connection
      final peerConnections = _groupPeerConnections[callId];
      if (peerConnections != null) {
        final pc = peerConnections[participantId];
        if (pc != null) {
          await pc.close();
          peerConnections.remove(participantId);
        }
      }

      // Remove from active participants
      final participants = _activeGroupParticipants[callId];
      if (participants != null) {
        participants.remove(participantId);
      }

      // Emit participant left event
      LoggingService.info('Participant $participantId left call $callId');

      LoggingService.error('Participant $participantId left call $callId');
    } catch (e) {
      LoggingService.error('Error handling participant leave: $e');
    }
  }

  Future<void> _sendLeaveMessage(String callId, String? targetUserId) async {
    try {
      // Get current user ID from auth service
      final currentUserId = _userRepository.currentUserId ?? 'unknown_user';

      final message = {
        'type': 'leave',
        'callId': callId,
        'senderId': currentUserId,
        'timestamp': DateTime.now().toIso8601String(),
      };

      if (targetUserId != null) {
        // Direct call - send to specific user
        LoggingService.error(
          'Would send leave message to $targetUserId: $message',
        );
      } else {
        // Group call - send to all participants
        final participants = _activeGroupParticipants[callId];
        if (participants != null) {
          for (final participantId in participants) {
            LoggingService.error(
              'Would send leave message to $participantId: $message',
            );
          }
        }
      }
    } catch (e) {
      LoggingService.error('Error sending leave message: $e');
    }
  }

  Future<void> _sendBusySignal(String callId, String targetUserId) async {
    try {
      await _sendCallMessage(targetUserId, {
        'type': 'busy',
        'callId': callId,
        'message': 'User is busy',
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      LoggingService.error('Error sending busy signal: $e');
    }
  }

  Future<void> _handleBusySignal(String callId) async {
    try {
      // Clean up call resources
      _cleanupCall(callId);

      _callOutcomeOccurredController.add(
        CallOutcomeOccurred(callId: callId, outcome: 'rejected_busy'),
      );
    } catch (e) {
      LoggingService.error('Error handling busy signal: $e');
    }
  }

  @override
  Stream<MediaStream?> getLocalStream(String callId) {
    final localStream = _localStreams[callId];
    return Stream.value(localStream);
  }

  @override
  Stream<Map<String, MediaStream>> getRemoteStreams(String callId) {
    final remoteStreams = _groupRemoteStreams[callId] ?? {};
    return Stream.value(remoteStreams);
  }

  @override
  Future<bool> isCallActive(String callId) async =>
      _groupPeerConnections.containsKey(callId) &&
      _groupPeerConnections[callId]!.isNotEmpty;

  @override
  Future<List<UserModel>> getCallParticipants(String callId) async {
    final callContext = _callContexts[callId];
    if (callContext == null) return [];

    // For now, return empty list since _CallContext doesn't store participants
    // In a real implementation, we'd need to track participants separately
    // or extend _CallContext to include participants
    return [];
  }
}
