import '../../../repositories/cache/cache_invalidation_repository.dart';
import '../../services/cache_invalidation_service.dart';

/// Implementation of CacheInvalidationRepository in the Provider layer
/// 
/// This class delegates to the existing CacheInvalidationService while
/// following Clean Architecture principles.
class CacheInvalidationRepositoryImpl implements CacheInvalidationRepository {
  final CacheInvalidationService _service;
  
  CacheInvalidationRepositoryImpl({
    CacheInvalidationService? service,
  }) : _service = service ?? CacheInvalidationService();

  @override
  Future<void> initialize() => _service.initialize();

  @override
  Future<void> invalidateUserData(String userId) => 
      _service.invalidateUserData(userId);

  @override
  Future<void> invalidateByPattern(String pattern) => 
      _service.invalidateByPattern(pattern);

  @override
  Future<void> invalidateByUrl(String url) => 
      _service.invalidateByUrl(url);

  @override
  Future<void> scheduleInvalidation(String pattern, {required Duration delay}) => 
      _service.scheduleInvalidation(pattern, delay: delay);

  @override
  Future<void> invalidateAll() => _service.invalidateAll();

  @override
  Map<String, dynamic> getInvalidationStats() => 
      _service.getInvalidationStats();

  @override
  void dispose() => _service.dispose();
}
