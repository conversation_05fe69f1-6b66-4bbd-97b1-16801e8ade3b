import '../../../repositories/cache/cache_monitoring_repository.dart';
import '../../services/cache_monitoring_service.dart';

/// Implementation of CacheMonitoringRepository in the Provider layer
/// 
/// This class delegates to the existing CacheMonitoringService while
/// following Clean Architecture principles.
class CacheMonitoringRepositoryImpl implements CacheMonitoringRepository {
  final CacheMonitoringService _service;
  
  CacheMonitoringRepositoryImpl({
    CacheMonitoringService? service,
  }) : _service = service ?? CacheMonitoringService();

  @override
  Future<void> initialize() => _service.initialize();

  @override
  void registerCache(String cacheName, {String? description}) => 
      _service.registerCache(cacheName, description: description);

  @override
  void recordCacheHit(String cacheName, String key, {Duration? responseTime}) => 
      _service.recordCacheHit(cacheName, key, responseTime: responseTime);

  @override
  void recordCacheMiss(String cacheName, String key, {Duration? responseTime}) => 
      _service.recordCacheMiss(cacheName, key, responseTime: responseTime);

  @override
  void recordCacheEviction(String cacheName, String key, {String? reason}) => 
      _service.recordCacheEviction(cacheName, key, reason: reason);

  @override
  void recordCacheError(String cacheName, String key, String error) => 
      _service.recordCacheError(cacheName, key, error);

  @override
  void recordMemoryUsage(String cacheName, int memoryBytes) => 
      _service.recordMemoryUsage(cacheName, memoryBytes);

  @override
  Map<String, dynamic> getCacheStatistics() => 
      _service.getCacheStatistics();

  @override
  Map<String, dynamic> getCacheHealthReport() => 
      _service.getCacheHealthReport();

  @override
  void dispose() => _service.dispose();
}
