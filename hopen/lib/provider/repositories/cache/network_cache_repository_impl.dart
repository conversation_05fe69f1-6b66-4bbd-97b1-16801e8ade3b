import 'package:http/http.dart' as http;

import '../../../repositories/cache/network_cache_repository.dart';
import '../../services/network_optimization_service.dart';

/// Implementation of NetworkCacheRepository in the Provider layer
/// 
/// This class delegates to the existing NetworkOptimizationService while
/// following Clean Architecture principles.
class NetworkCacheRepositoryImpl implements NetworkCacheRepository {
  final NetworkOptimizationService _service;
  
  NetworkCacheRepositoryImpl({
    NetworkOptimizationService? service,
  }) : _service = service ?? NetworkOptimizationService();

  @override
  Future<void> initialize() => _service.initialize();

  @override
  Future<http.Response> get(
    String url, {
    Map<String, String>? headers,
    Duration? cacheDuration,
    bool forceRefresh = false,
  }) => _service.get(
      url,
      headers: headers,
      cacheDuration: cacheDuration,
      forceRefresh: forceRefresh,
    );

  @override
  Future<http.Response> post(
    String url, {
    Map<String, String>? headers,
    Object? body,
    bool skipCache = true,
  }) => _service.post(
      url,
      headers: headers,
      body: body,
      skipCache: skipCache,
    );

  @override
  Future<http.Response> put(
    String url, {
    Map<String, String>? headers,
    Object? body,
    bool skipCache = true,
  }) => _service.put(
      url,
      headers: headers,
      body: body,
      skipCache: skipCache,
    );

  @override
  Future<http.Response> delete(
    String url, {
    Map<String, String>? headers,
    bool skipCache = true,
  }) => _service.delete(
      url,
      headers: headers,
      skipCache: skipCache,
    );

  @override
  Future<List<http.Response>> batchRequests(List<BatchRequest> requests) => 
      _service.batchRequests(requests.map((req) => 
        NetworkBatchRequest(
          method: req.method,
          url: req.url,
          headers: req.headers,
          body: req.body,
          cacheDuration: req.cacheDuration,
        )).toList());

  @override
  Future<void> invalidateByPattern(String pattern) => 
      _service.invalidateByPattern(pattern);

  @override
  void clearCache() => _service.clearCache();

  @override
  Map<String, dynamic> getCacheStats() => _service.getCacheStats();

  @override
  void dispose() => _service.dispose();
}

/// Internal batch request class for service compatibility
class NetworkBatchRequest {
  final String method;
  final String url;
  final Map<String, String>? headers;
  final Object? body;
  final Duration? cacheDuration;

  const NetworkBatchRequest({
    required this.method,
    required this.url,
    this.headers,
    this.body,
    this.cacheDuration,
  });
}
