import 'dart:async';
import 'package:flutter/widgets.dart';

import '../../../repositories/cache/ui_cache_repository.dart';
import '../../services/ui_optimization_service.dart';

/// Implementation of UICacheRepository in the Provider layer
/// 
/// This class delegates to the existing UIOptimizationService while
/// following Clean Architecture principles.
class UICacheRepositoryImpl implements UICacheRepository {
  final UIOptimizationService _service;
  
  UICacheRepositoryImpl({
    UIOptimizationService? service,
  }) : _service = service ?? UIOptimizationService();

  @override
  Future<void> initialize() => _service.initialize();

  @override
  ImageProvider getOptimizedImage(
    String imageUrl, {
    Duration? cacheDuration,
    double? scale,
    Map<String, String>? headers,
  }) => _service.getOptimizedImage(
      imageUrl,
      cacheDuration: cacheDuration,
      scale: scale,
      headers: headers,
    );

  @override
  Widget createOptimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    bool addAutomaticKeepAlives = true,
    bool addRepaintBoundaries = true,
    bool addSemanticIndexes = true,
    double? cacheExtent,
    int? semanticChildCount,
    DragStartBehavior dragStartBehavior = DragStartBehavior.start,
    ScrollViewKeyboardDismissBehavior keyboardDismissBehavior = ScrollViewKeyboardDismissBehavior.manual,
    String? restorationId,
    Clip clipBehavior = Clip.hardEdge,
  }) => _service.createOptimizedListView(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      controller: controller,
      addAutomaticKeepAlives: addAutomaticKeepAlives,
      addRepaintBoundaries: addRepaintBoundaries,
      addSemanticIndexes: addSemanticIndexes,
      cacheExtent: cacheExtent,
      semanticChildCount: semanticChildCount,
      dragStartBehavior: dragStartBehavior,
      keyboardDismissBehavior: keyboardDismissBehavior,
      restorationId: restorationId,
      clipBehavior: clipBehavior,
    );

  @override
  Widget createOptimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required SliverGridDelegate gridDelegate,
    ScrollController? controller,
    bool addAutomaticKeepAlives = true,
    bool addRepaintBoundaries = true,
    bool addSemanticIndexes = true,
    double? cacheExtent,
    int? semanticChildCount,
    DragStartBehavior dragStartBehavior = DragStartBehavior.start,
    ScrollViewKeyboardDismissBehavior keyboardDismissBehavior = ScrollViewKeyboardDismissBehavior.manual,
    String? restorationId,
    Clip clipBehavior = Clip.hardEdge,
  }) => _service.createOptimizedGridView(
      itemCount: itemCount,
      itemBuilder: itemBuilder,
      gridDelegate: gridDelegate,
      controller: controller,
      addAutomaticKeepAlives: addAutomaticKeepAlives,
      addRepaintBoundaries: addRepaintBoundaries,
      addSemanticIndexes: addSemanticIndexes,
      cacheExtent: cacheExtent,
      semanticChildCount: semanticChildCount,
      dragStartBehavior: dragStartBehavior,
      keyboardDismissBehavior: keyboardDismissBehavior,
      restorationId: restorationId,
      clipBehavior: clipBehavior,
    );

  @override
  VoidCallback createDebouncedFunction(
    String key,
    VoidCallback function, {
    Duration delay = const Duration(milliseconds: 300),
  }) => _service.createDebouncedFunction(key, function, delay: delay);

  @override
  void registerAnimationController(String key, AnimationController controller) => 
      _service.registerAnimationController(key, controller);

  @override
  void unregisterAnimationController(String key) => 
      _service.unregisterAnimationController(key);

  @override
  Map<String, dynamic> getPerformanceStats() => 
      _service.getPerformanceStats();

  @override
  void clearAllCaches() => _service.clearAllCaches();

  @override
  void dispose() => _service.dispose();
}
