import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image/image.dart' as img;
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;

import '../../../repositories/image_processing/image_processing_repository.dart';
import '../../../statefulbusinesslogic/core/models/image_processing_result.dart';
import '../../exceptions/storage_exception.dart';

/// Implementation of ImageProcessingRepository in the Provider layer
///
/// This class consolidates all image processing logic and eliminates duplication
/// across the codebase while following Clean Architecture principles.
class ImageProcessingRepositoryImpl implements ImageProcessingRepository {
  const ImageProcessingRepositoryImpl._();

  static const ImageProcessingRepositoryImpl _instance =
      ImageProcessingRepositoryImpl._();
  factory ImageProcessingRepositoryImpl() => _instance;

  // Constants following the profile picture pipeline requirements
  static const int maxFileSizeBytes =
      2 * 1024 * 1024; // 2MB as per pipeline specs
  static const int minImageDimension =
      640; // Minimum 640x640 as per requirements
  static const int maxImageDimension = 4096;
  static const int targetDimension =
      1440; // Client-side resize target: 1440x1440 pixels max
  static const List<String> allowedExtensions = [
    'jpg',
    'jpeg',
    'png',
    'webp',
    'heif',
    'heic',
  ];
  static const List<String> allowedMimeTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/heif',
    'image/heic',
  ];

  /// Platform detection for HEIF/HEIC processing logic
  static bool get _isAndroid => !kIsWeb && Platform.isAndroid;
  static bool get _isIOS => !kIsWeb && Platform.isIOS;

  @override
  Future<XFile?> pickFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: targetDimension.toDouble(),
        maxHeight: targetDimension.toDouble(),
        imageQuality: 90, // Initial quality for picker
      );

      if (image == null) return null;

      // Validate the selected image
      await validateImageFile(image);

      return image;
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to pick image from gallery: $e',
      );
    }
  }

  @override
  Future<XFile?> takePhoto() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: targetDimension.toDouble(),
        maxHeight: targetDimension.toDouble(),
        imageQuality: 90, // Initial quality for picker
        preferredCameraDevice: CameraDevice.front, // Front camera for selfies
      );

      if (image == null) return null;

      // Validate the captured image
      await validateImageFile(image);

      return image;
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to capture photo: $e',
      );
    }
  }

  @override
  Future<void> validateImageFile(XFile imageFile) async {
    // Check file size
    final fileSize = await imageFile.length();
    if (fileSize > maxFileSizeBytes) {
      throw StorageException.fileTooLarge(
        actualSizeBytes: fileSize,
        maxSizeBytes: maxFileSizeBytes,
      );
    }

    // Check file extension
    final extension = path.extension(imageFile.path).toLowerCase();
    if (!allowedExtensions.contains(extension.replaceAll('.', ''))) {
      throw StorageException.invalidFormat(
        actualFormat: extension,
        allowedFormats: allowedExtensions,
      );
    }

    // Read file and validate image format
    final imageBytes = await imageFile.readAsBytes();
    final image = img.decodeImage(imageBytes);

    if (image == null) {
      throw StorageException.corruptedFile();
    }

    // Check image dimensions (basic validation - backend will handle resizing)
    if (!_validateBasicRequirements(image)) {
      final width = image.width;
      final height = image.height;
      throw StorageException.invalidDimensions(
        width: width,
        height: height,
        minDimension: minImageDimension,
        maxDimension: maxImageDimension,
      );
    }
  }

  @override
  Future<Uint8List> processImageFromFile(XFile imageFile) async {
    try {
      final extension = path.extension(imageFile.path).toLowerCase();

      // Step 1: Convert to WebP if not already
      Uint8List webpBytes;
      if (extension == '.webp') {
        webpBytes = await imageFile.readAsBytes();
      } else {
        final convertedBytes = await FlutterImageCompress.compressWithFile(
          imageFile.path,
          format: CompressFormat.webp,
          quality: 90,
          minWidth: targetDimension,
          minHeight: targetDimension,
        );

        if (convertedBytes == null) {
          throw StorageException.processingFailed(
            reason: 'Failed to convert to WebP format',
          );
        }
        webpBytes = convertedBytes;
      }

      // Step 2: Ensure exact 1440x1440 dimensions
      final image = img.decodeImage(webpBytes);
      if (image == null) {
        throw StorageException.processingFailed(
          reason: 'Failed to decode image',
        );
      }

      // Step 3: Resize to exact square if needed
      final resizedImage = _resizeToSquare(image, targetDimension);

      // Step 4: Final WebP encoding with optimal quality
      final finalBytes = await FlutterImageCompress.compressWithList(
        Uint8List.fromList(img.encodePng(resizedImage)),
        format: CompressFormat.webp,
        quality: 90,
      );

      if (finalBytes == null) {
        throw StorageException.processingFailed(
          reason: 'Failed final WebP compression',
        );
      }

      return finalBytes;
    } catch (e) {
      throw StorageException.processingFailed(
        reason: 'Image processing failed: $e',
      );
    }
  }

  @override
  Future<ImageInfo> getImageInfo(String imagePath) async {
    try {
      final file = File(imagePath);
      if (!await file.exists()) {
        throw StorageException.fileNotFound(fileName: path.basename(imagePath));
      }

      final imageBytes = await file.readAsBytes();
      final image = img.decodeImage(imageBytes);

      if (image == null) {
        throw StorageException.corruptedFile();
      }

      return ImageInfo(
        width: image.width,
        height: image.height,
        fileSizeBytes: imageBytes.length,
        format: _detectImageFormat(imagePath),
      );
    } catch (e) {
      if (e is StorageException) rethrow;
      throw StorageException.processingFailed(
        reason: 'Failed to get image info: $e',
      );
    }
  }

  @override
  Future<ImageProcessingResult> processImageInIsolate({
    required String imagePath,
    required int minResolution,
    required int maxResolution,
    required int maxFileSizeBytes,
    required double compressionQuality,
  }) async {
    try {
      print('🔄 Starting image processing...');

      // Create XFile from path for validation
      final imageFile = XFile(imagePath);

      // Validate the image
      await validateImageFile(imageFile);

      // Process the image
      final processedBytes = await processImageFromFile(imageFile);

      return ImageProcessingResult.success(
        processedBytes: processedBytes,
        originalSize: await imageFile.length(),
        processedSize: processedBytes.length,
      );
    } catch (e) {
      return ImageProcessingResult.error(e.toString());
    }
  }

  /// Validate basic image requirements
  bool _validateBasicRequirements(img.Image image) {
    return image.width >= minImageDimension &&
        image.height >= minImageDimension &&
        image.width <= maxImageDimension &&
        image.height <= maxImageDimension;
  }

  /// Resize image to square with the specified dimension
  img.Image _resizeToSquare(img.Image image, int dimension) {
    // Determine the size for square crop (use the smaller dimension)
    final cropSize = image.width < image.height ? image.width : image.height;

    // Calculate crop position to center the crop
    final cropX = (image.width - cropSize) ~/ 2;
    final cropY = (image.height - cropSize) ~/ 2;

    // Crop to square
    var processedImage = img.copyCrop(
      image,
      x: cropX,
      y: cropY,
      width: cropSize,
      height: cropSize,
    );

    // Resize to target dimension
    if (cropSize != dimension) {
      processedImage = img.copyResize(
        processedImage,
        width: dimension,
        height: dimension,
      );
    }

    return processedImage;
  }

  /// Detect image format from file path
  String _detectImageFormat(String imagePath) {
    final extension = path.extension(imagePath).toLowerCase();
    switch (extension) {
      case '.jpg':
      case '.jpeg':
        return 'jpeg';
      case '.png':
        return 'png';
      case '.webp':
        return 'webp';
      case '.heif':
        return 'heif';
      case '.heic':
        return 'heic';
      default:
        return 'unknown';
    }
  }
}
