/// Base implementation for cache-first repositories
///
/// This provides common functionality for repositories that prioritize local cache
/// and only fetch from remote when triggered by MQTT events.

import 'dart:async';
import '../../../statefulbusinesslogic/core/repositories/cache_first_repository_interface.dart';
import '../../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';

/// Base class for cache-first repositories
abstract class CacheFirstRepositoryBase
    implements MonitoredCacheFirstRepository {
  CacheFirstRepositoryBase({
    this.config = const CacheConfig(),
    this.debugMode = false,
  });

  final CacheConfig config;
  bool debugMode;

  // State management
  RepositoryState _state = RepositoryState.uninitialized;
  final StreamController<RepositoryState> _stateController =
      StreamController<RepositoryState>.broadcast();

  // Metrics
  DateTime? _lastRefresh;
  int _cacheHitCount = 0;
  int _cacheMissCount = 0;
  int _remoteCallCount = 0;
  int _errorCount = 0;
  String? _lastError;

  // Event handling
  final StreamController<MqttEvent> _eventController =
      StreamController<MqttEvent>.broadcast();

  RepositoryState get state => _state;

  Stream<RepositoryState> get stateStream => _stateController.stream;

  Stream<MqttEvent> get eventStream => _eventController.stream;

  DateTime? get lastRemoteRefresh => _lastRefresh;

  RepositoryMetrics get metrics => RepositoryMetrics(
    state: _state,
    lastRefresh: _lastRefresh,
    cacheHitCount: _cacheHitCount,
    cacheMissCount: _cacheMissCount,
    remoteCallCount: _remoteCallCount,
    errorCount: _errorCount,
    lastError: _lastError,
  );

  @override
  void setDebugMode(bool enabled) {
    debugMode = enabled;
  }

  /// Update repository state and notify listeners
  void _updateState(RepositoryState newState) {
    if (_state != newState) {
      _state = newState;
      _stateController.add(newState);

      if (debugMode) {
        LoggingService.debug('${runtimeType}: State changed to $newState');
      }
    }
  }

  /// Record cache hit
  void _recordCacheHit() {
    _cacheHitCount++;
    if (debugMode) {
      LoggingService.debug(
        '${runtimeType}: Cache hit (total: $_cacheHitCount)',
      );
    }
  }

  /// Record cache miss
  void _recordCacheMiss() {
    _cacheMissCount++;
    if (debugMode) {
      LoggingService.debug(
        '${runtimeType}: Cache miss (total: $_cacheMissCount)',
      );
    }
  }

  /// Record remote call
  void _recordRemoteCall() {
    _remoteCallCount++;
    if (debugMode) {
      LoggingService.debug(
        '${runtimeType}: Remote call (total: $_remoteCallCount)',
      );
    }
  }

  /// Record error
  void _recordError(String error) {
    _errorCount++;
    _lastError = error;
    if (debugMode) {
      LoggingService.error('${runtimeType}: Error - $error');
    }
  }

  @override
  Future<void> initialize() async {
    if (_state != RepositoryState.uninitialized) {
      return;
    }

    _updateState(RepositoryState.initializing);

    try {
      await initializeCache();

      // Load initial data from cache
      if (await hasCachedData) {
        _recordCacheHit();
        if (debugMode) {
          LoggingService.info('${runtimeType}: Initialized with cached data');
        }
      } else {
        // No cached data, fetch from remote
        await refreshFromRemote();
      }

      _updateState(RepositoryState.ready);
    } catch (e) {
      _recordError('Failed to initialize: $e');
      _updateState(RepositoryState.error);
      rethrow;
    }
  }

  @override
  Future<void> refreshFromRemote() async {
    if (_state == RepositoryState.refreshing) {
      return; // Already refreshing
    }

    final previousState = _state;
    _updateState(RepositoryState.refreshing);

    try {
      _recordRemoteCall();
      await fetchFromRemote();
      _lastRefresh = DateTime.now();

      if (debugMode) {
        LoggingService.info(
          '${runtimeType}: Refreshed from remote at $_lastRefresh',
        );
      }

      _updateState(
        previousState == RepositoryState.initializing
            ? RepositoryState.ready
            : previousState,
      );
    } catch (e) {
      _recordError('Failed to refresh from remote: $e');
      _updateState(RepositoryState.error);
      rethrow;
    }
  }

  @override
  Future<void> processRealTimeEvent(MqttEvent event) async {
    try {
      _eventController.add(event);
      await handleMqttEvent(event);

      if (debugMode) {
        LoggingService.info(
          '${runtimeType}: Processed MQTT event: ${event.runtimeType}',
        );
      }
    } catch (e) {
      _recordError('Failed to process MQTT event: $e');
      if (debugMode) {
        LoggingService.error(
          '${runtimeType}: Failed to process MQTT event: $e',
        );
      }
    }
  }

  /// Check if data exists in cache (to be implemented by subclasses)
  @override
  Future<bool> get hasCachedData;

  /// Initialize the cache system (to be implemented by subclasses)
  Future<void> initializeCache();

  /// Fetch fresh data from remote source (to be implemented by subclasses)
  Future<void> fetchFromRemote();

  /// Clear all cached data (to be implemented by subclasses)
  @override
  Future<void> clearCache();

  /// Handle MQTT events (to be implemented by subclasses)
  @override
  Future<void> handleMqttEvent(MqttEvent event);

  /// Dispose resources
  void dispose() {
    _stateController.close();
    _eventController.close();
  }

  /// Helper method to execute cache-first operations
  Future<T> executeCacheFirst<T>({
    required Future<T?> Function() cacheOperation,
    required Future<T> Function() remoteOperation,
    bool forceRemote = false,
  }) async {
    if (!forceRemote) {
      try {
        final cachedResult = await cacheOperation();
        if (cachedResult != null) {
          _recordCacheHit();
          return cachedResult;
        }
      } catch (e) {
        if (debugMode) {
          LoggingService.warning('${runtimeType}: Cache operation failed: $e');
        }
      }
    }

    _recordCacheMiss();
    _recordRemoteCall();
    return await remoteOperation();
  }
}
