import 'dart:typed_data';
import 'dart:io';
import 'package:flutter/material.dart';
import '../../repositories/local_storage/abstract_profile_picture_repository.dart';

/// State management for profile pictures using Provider pattern
/// 
/// This ChangeNotifier manages a multi-layered cache:
/// 1. In-Memory Cache (fastest) - Uint8List data in RAM
/// 2. Disk Cache (persistent) - Managed by Drift database
/// 3. Network (slowest) - Original source via CachedNetworkImage
class ProfilePictureNotifier extends ChangeNotifier {
  final AbstractProfilePictureRepository _repository;

  ProfilePictureNotifier(this._repository);

  // In-memory cache for the current session - prevents repeated disk/network access
  final Map<String, Uint8List> _memoryCache = {};
  
  // Loading states per image URL
  final Map<String, bool> _loadingStates = {};
  
  // Error states per image URL
  final Map<String, String?> _errorStates = {};

  /// Check if an image is currently loading
  bool isLoading(String imageUrl) => _loadingStates[imageUrl] ?? false;

  /// Get error message for an image URL
  String? getError(String imageUrl) => _errorStates[imageUrl];

  /// Get cached image data from memory
  Uint8List? getImageData(String imageUrl) => _memoryCache[imageUrl];

  /// Check if image is available in memory cache
  bool isImageCached(String imageUrl) => _memoryCache.containsKey(imageUrl);

  /// Fetch profile picture with multi-layered caching
  Future<void> fetchProfilePicture(String userId, String imageUrl) async {
    // Skip if already in memory cache
    if (_memoryCache.containsKey(imageUrl)) {
      debugPrint('🖼️ ProfilePictureNotifier: Image already in memory cache: $imageUrl');
      return;
    }

    // Skip if already loading
    if (_loadingStates[imageUrl] == true) {
      debugPrint('🖼️ ProfilePictureNotifier: Image already loading: $imageUrl');
      return;
    }

    _setLoadingState(imageUrl, true);
    _clearError(imageUrl);

    try {
      debugPrint('🖼️ ProfilePictureNotifier: Fetching profile picture for user $userId');
      
      // 1. Check disk cache first (Drift database)
      final cachedPath = await _repository.getProfilePicturePath(userId, imageUrl);
      
      if (cachedPath != null) {
        debugPrint('🖼️ ProfilePictureNotifier: Found in disk cache: $cachedPath');
        
        // Load from disk into memory
        final file = File(cachedPath);
        if (await file.exists()) {
          final imageBytes = await file.readAsBytes();
          _memoryCache[imageUrl] = imageBytes;
          debugPrint('🖼️ ProfilePictureNotifier: Loaded from disk to memory cache');
          
          // Update last accessed time
          await _repository.updateLastAccessed(userId);
        } else {
          debugPrint('🖼️ ProfilePictureNotifier: Cached file no longer exists: $cachedPath');
          // File was deleted, let it fall through to network fetch
        }
      } else {
        debugPrint('🖼️ ProfilePictureNotifier: Not found in disk cache, will use CachedNetworkImage fallback');
        // Not in disk cache - CachedNetworkImage will handle network fetch and caching
      }
    } catch (e) {
      debugPrint('🖼️ ProfilePictureNotifier: Error fetching profile picture: $e');
      _setError(imageUrl, 'Failed to load profile picture: $e');
    } finally {
      _setLoadingState(imageUrl, false);
    }
  }

  /// Invalidate cache and refetch image
  Future<void> invalidateAndRefetch(String userId, String imageUrl) async {
    debugPrint('🖼️ ProfilePictureNotifier: Invalidating cache for user $userId');
    
    // 1. Clear memory cache
    _memoryCache.remove(imageUrl);
    _clearError(imageUrl);
    
    // 2. Clear disk cache
    try {
      // Note: We don't have a direct invalidate method in the repository
      // The repository will handle cache invalidation internally
      await _repository.clearOldCache(Duration.zero); // Clear all old cache
    } catch (e) {
      debugPrint('🖼️ ProfilePictureNotifier: Error clearing disk cache: $e');
    }
    
    // 3. Refetch
    await fetchProfilePicture(userId, imageUrl);
  }

  /// Clear all caches
  Future<void> clearAllCaches() async {
    debugPrint('🖼️ ProfilePictureNotifier: Clearing all caches');
    
    // Clear memory cache
    _memoryCache.clear();
    _loadingStates.clear();
    _errorStates.clear();
    
    // Clear disk cache
    try {
      await _repository.clearAllCache();
    } catch (e) {
      debugPrint('🖼️ ProfilePictureNotifier: Error clearing disk cache: $e');
    }
    
    notifyListeners();
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    final diskStats = await _repository.getCacheStats();
    
    return {
      'memoryCache': {
        'totalImages': _memoryCache.length,
        'totalSizeBytes': _memoryCache.values.fold<int>(
          0, 
          (sum, bytes) => sum + bytes.length,
        ),
      },
      'diskCache': diskStats,
    };
  }

  /// Private helper methods
  void _setLoadingState(String imageUrl, bool isLoading) {
    _loadingStates[imageUrl] = isLoading;
    notifyListeners();
  }

  void _setError(String imageUrl, String error) {
    _errorStates[imageUrl] = error;
    notifyListeners();
  }

  void _clearError(String imageUrl) {
    _errorStates.remove(imageUrl);
    notifyListeners();
  }

  @override
  void dispose() {
    _memoryCache.clear();
    _loadingStates.clear();
    _errorStates.clear();
    super.dispose();
  }
}
