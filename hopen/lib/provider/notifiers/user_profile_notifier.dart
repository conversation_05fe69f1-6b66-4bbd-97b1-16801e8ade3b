import 'dart:async';
import 'package:flutter/foundation.dart';

import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../repositories/user/cached_user_repository.dart';

/// ChangeNotifier for user profile management with instant loading and background refresh
///
/// This notifier implements the "stale-while-revalidate" pattern:
/// 1. Instantly loads cached user data
/// 2. Triggers background refresh
/// 3. Updates UI when fresh data arrives
/// 4. Provides offline support
class UserProfileNotifier extends ChangeNotifier {
  final CachedUserRepository _repository;

  UserModel? _currentUser;
  UserModel? get currentUser => _currentUser;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  String? _error;
  String? get error => _error;

  StreamSubscription<UserModel?>? _userSubscription;

  UserProfileNotifier(this._repository);

  /// Initialize with user ID (typically called after login)
  Future<void> initializeUser(String userId) async {
    debugPrint('👤 UserProfileNotifier: Initializing user $userId');

    _setLoading(true);
    _clearError();

    try {
      // Cancel any existing subscription
      await _userSubscription?.cancel();

      // Start watching the user profile
      _userSubscription = _repository
          .watchUserProfile(userId)
          .listen(
            (user) {
              debugPrint(
                '👤 UserProfileNotifier: User profile updated for $userId',
              );
              _currentUser = user;
              _setLoading(false);
              notifyListeners();
            },
            onError: (error) {
              debugPrint(
                '👤 UserProfileNotifier: Error watching user $userId: $error',
              );
              _setError(error.toString());
              _setLoading(false);
            },
          );

      // Also try to get immediate cached data
      final cachedUser = await _repository.getUserProfile(userId);
      if (cachedUser != null) {
        _currentUser = cachedUser;
        _setLoading(false);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('👤 UserProfileNotifier: Error initializing user $userId: $e');
      _setError(e.toString());
      _setLoading(false);
    }
  }

  /// Cache user profile immediately after authentication
  Future<void> cacheUserProfile(UserModel user) async {
    debugPrint('👤 UserProfileNotifier: Caching user profile for ${user.id}');

    try {
      await _repository.cacheUserProfile(user);
      debugPrint('👤 UserProfileNotifier: User profile cached for ${user.id}');
    } catch (e) {
      debugPrint('👤 UserProfileNotifier: Error caching user profile: $e');
    }
  }

  /// Get user profile by ID (for viewing other users)
  Future<UserModel?> getUserProfile(String userId) async {
    debugPrint('👤 UserProfileNotifier: Getting profile for user $userId');

    try {
      return await _repository.getUserProfile(userId);
    } catch (e) {
      debugPrint('👤 UserProfileNotifier: Error getting user $userId: $e');
      return null;
    }
  }

  /// Watch another user's profile (useful for profile pages)
  Stream<UserModel?> watchUserProfile(String userId) {
    debugPrint('👤 UserProfileNotifier: Watching profile for user $userId');
    return _repository.watchUserProfile(userId);
  }

  /// Update current user profile (optimistic update + network sync)
  Future<void> updateCurrentUser(UserModel updatedUser) async {
    if (_currentUser?.id != updatedUser.id) {
      debugPrint('👤 UserProfileNotifier: Cannot update user - ID mismatch');
      return;
    }

    debugPrint(
      '👤 UserProfileNotifier: Updating current user ${updatedUser.id}',
    );

    // Optimistic update
    _currentUser = updatedUser;
    notifyListeners();

    // TODO: Implement network update when you have an update endpoint
    // For now, we just update the cache
    try {
      // The cache will be updated automatically when the network call succeeds
      // await _repository.updateUserProfile(updatedUser);
    } catch (e) {
      debugPrint('👤 UserProfileNotifier: Error updating user: $e');
      // Revert optimistic update on error
      // You might want to reload from cache here
    }
  }

  /// Refresh current user data from network
  Future<void> refreshCurrentUser() async {
    if (_currentUser == null) {
      debugPrint('👤 UserProfileNotifier: No current user to refresh');
      return;
    }

    debugPrint(
      '👤 UserProfileNotifier: Refreshing current user ${_currentUser!.id}',
    );

    try {
      // The repository will handle the refresh and update the stream
      await _repository.getUserProfile(_currentUser!.id);
    } catch (e) {
      debugPrint('👤 UserProfileNotifier: Error refreshing user: $e');
      _setError(e.toString());
    }
  }

  /// Clear current user (logout)
  Future<void> clearUser() async {
    debugPrint('👤 UserProfileNotifier: Clearing current user');

    await _userSubscription?.cancel();
    _userSubscription = null;
    _currentUser = null;
    _setLoading(false);
    _clearError();
    notifyListeners();
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    return await _repository.getCacheStats();
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    debugPrint('👤 UserProfileNotifier: Clearing cache');
    await _repository.clearCache();
  }

  /// Helper methods
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    if (_error != null) {
      _error = null;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _userSubscription?.cancel();
    super.dispose();
  }

  /// Convenience getters
  String? get currentUserId => _currentUser?.id;
  String? get currentUserName =>
      _currentUser?.firstName ?? _currentUser?.username;
  String? get currentUserEmail => _currentUser?.email;
  String? get currentUserAvatarUrl => _currentUser?.profilePictureUrl;
  bool get hasCurrentUser => _currentUser != null;
  bool get isCurrentUserOnline =>
      _currentUser?.onlineStatus == OnlineStatus.online;
}
