import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/user_model.dart';
import '../../statefulbusinesslogic/core/repositories/cache_first_repository_interface.dart';
import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';

/// Contact request status
enum ContactRequestStatus { pending, accepted, rejected, expired }

/// Contact request model
class ContactRequest {
  const ContactRequest({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.receiverId,
    required this.receiverName,
    required this.sentAt,
    required this.status,
    this.senderAvatarUrl,
    this.receiverAvatarUrl,
    this.message,
    this.respondedAt,
  });

  factory ContactRequest.fromJson(Map<String, dynamic> json) => ContactRequest(
    id: json['id'] as String? ?? '',
    senderId: json['senderId'] as String,
    senderName: json['senderName'] as String? ?? 'Unknown User',
    senderAvatarUrl: json['senderAvatarUrl'] as String?,
    receiverId: json['receiverId'] as String,
    receiverName: json['receiverName'] as String? ?? 'Unknown User',
    receiverAvatarUrl: json['receiverAvatarUrl'] as String?,
    sentAt: DateTime.parse(json['sentAt'] as String),
    status: ContactRequestStatus.values.firstWhere(
      (e) => e.name == json['status'],
      orElse: () => ContactRequestStatus.pending,
    ),
    message: json['message'] as String?,
    respondedAt:
        json['respondedAt'] != null
            ? DateTime.parse(json['respondedAt'] as String)
            : null,
  );
  final String id;
  final String senderId;
  final String senderName;
  final String? senderAvatarUrl;
  final String receiverId;
  final String receiverName;
  final String? receiverAvatarUrl;
  final DateTime sentAt;
  final ContactRequestStatus status;
  final String? message;
  final DateTime? respondedAt;

  Map<String, dynamic> toJson() => {
    'id': id,
    'senderId': senderId,
    'senderName': senderName,
    'senderAvatarUrl': senderAvatarUrl,
    'receiverId': receiverId,
    'receiverName': receiverName,
    'receiverAvatarUrl': receiverAvatarUrl,
    'sentAt': sentAt.toIso8601String(),
    'status': status.name,
    'message': message,
    'respondedAt': respondedAt?.toIso8601String(),
  };
}

/// Cache-first repository interface for managing contact requests
///
/// This repository prioritizes local cache and only fetches from remote
/// when triggered by MQTT events or initial load.
abstract class ContactRequestRepository extends RealTimeEventRepository {
  /// Send a contact request to another user
  Future<Result<ContactRequest>> sendContactRequest(
    String receiverId,
    String message,
  );

  /// Get pending contact requests received by current user (from cache)
  Future<Result<List<ContactRequest>>> getPendingReceivedRequests();

  /// Get pending contact requests sent by current user (from cache)
  Future<Result<List<ContactRequest>>> getPendingSentRequests();

  /// Accept a contact request
  Future<Result<void>> acceptContactRequest(String requestId);

  /// Reject a contact request
  Future<Result<void>> rejectContactRequest(String requestId);

  /// Cancel a contact request (by sender)
  Future<Result<void>> cancelContactRequest(String requestId);

  /// Get contact request by ID (from cache first)
  Future<Result<ContactRequest>> getContactRequest(String requestId);

  /// Check if there's a pending request between two users (from cache)
  Future<Result<bool>> hasPendingRequest(String userId1, String userId2);

  /// Get contact request history (from cache)
  Future<Result<List<ContactRequest>>> getContactRequestHistory();

  /// Get mutual contacts between current user and another user
  Future<Result<List<UserModel>>> getMutualContacts(String userId);

  /// Search for users to send contact requests (always fresh)
  Future<Result<List<UserModel>>> searchUsers(String query);

  /// Get suggested contacts based on mutual friends
  Future<Result<List<UserModel>>> getSuggestedContacts();

  /// Auto-expire old contact requests
  Future<Result<void>> expireOldRequests();

  /// Stream of pending received requests (real-time updates)
  Stream<List<ContactRequest>> get pendingReceivedRequestsStream;

  /// Stream of pending sent requests (real-time updates)
  Stream<List<ContactRequest>> get pendingSentRequestsStream;

  /// Handle contact request MQTT events
  @override
  Future<void> handleMqttEvent(MqttEvent event) async {
    if (event is ContactRequestEvent) {
      await processContactRequestEvent(event);
    }
  }

  /// Process contact request events and update cache
  Future<void> processContactRequestEvent(ContactRequestEvent event);
}
