/// Abstract repository interface for profile picture caching operations
/// 
/// This interface defines the contract for profile picture caching operations
/// following the Repository pattern in Clean Architecture.
/// 
/// The implementation should be in the Provider layer, not directly accessible
/// from the Presentation layer.
abstract class AbstractProfilePictureRepository {
  /// Get the local path for a user's profile picture
  /// Returns the cached local path if available, otherwise downloads and caches
  Future<String?> getProfilePicturePath(String userId, String imageUrl);
  
  /// Clear old cache entries based on age
  Future<void> clearOldCache(Duration maxAge);
  
  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats();
  
  /// Clear all cache entries
  Future<void> clearAllCache();
  
  /// Update the last accessed time for a profile picture
  Future<void> updateLastAccessed(String userId);
  
  /// Check if a profile picture is cached for a user
  Future<bool> isProfilePictureCached(String userId);
} 