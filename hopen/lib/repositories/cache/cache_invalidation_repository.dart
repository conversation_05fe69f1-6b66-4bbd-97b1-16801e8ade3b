/// Repository interface for cache invalidation operations
/// This follows the four-layer dependency rule by defining contracts in the repository layer
abstract class CacheInvalidationRepository {
  /// Initialize the cache invalidation service
  Future<void> initialize();

  /// Invalidate cache by user ID
  Future<void> invalidateUserData(String userId);

  /// Invalidate cache by pattern
  Future<void> invalidateByPattern(String pattern);

  /// Invalidate cache by URL
  Future<void> invalidateByUrl(String url);

  /// Schedule delayed invalidation
  Future<void> scheduleInvalidation(String pattern, {required Duration delay});

  /// Invalidate all caches
  Future<void> invalidateAll();

  /// Get invalidation statistics
  Map<String, dynamic> getInvalidationStats();

  /// Dispose of the service
  void dispose();
}

/// Invalidation strategy configuration
class InvalidationStrategy {
  final List<String> patterns;
  final List<String> dependencies;
  final Duration invalidationDelay;

  const InvalidationStrategy({
    required this.patterns,
    this.dependencies = const [],
    this.invalidationDelay = const Duration(seconds: 5),
  });
}
