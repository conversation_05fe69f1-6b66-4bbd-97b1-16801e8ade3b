/// Repository interface for cache monitoring operations
/// This follows the four-layer dependency rule by defining contracts in the repository layer
abstract class CacheMonitoringRepository {
  /// Initialize the cache monitoring service
  Future<void> initialize();

  /// Register a cache for monitoring
  void registerCache(String cacheName, {String? description});

  /// Record cache hit
  void recordCacheHit(String cacheName, String key, {Duration? responseTime});

  /// Record cache miss
  void recordCacheMiss(String cacheName, String key, {Duration? responseTime});

  /// Record cache eviction
  void recordCacheEviction(String cacheName, String key, {String? reason});

  /// Record cache error
  void recordCacheError(String cacheName, String key, String error);

  /// Record memory usage
  void recordMemoryUsage(String cacheName, int memoryBytes);

  /// Get comprehensive cache statistics
  Map<String, dynamic> getCacheStatistics();

  /// Get cache health report
  Map<String, dynamic> getCacheHealthReport();

  /// Dispose of the service
  void dispose();
}

/// Cache event types for monitoring
enum CacheEventType {
  hit,
  miss,
  eviction,
  error,
}

/// Cache metrics data structure
class CacheMetrics {
  final String name;
  final String description;
  final DateTime createdAt;
  int totalHits = 0;
  int totalMisses = 0;
  int totalEvictions = 0;
  int totalErrors = 0;
  final List<CacheMemoryPoint> memoryHistory = [];

  CacheMetrics({
    required this.name,
    required this.description,
    required this.createdAt,
  });

  double get hitRate {
    final total = totalHits + totalMisses;
    return total > 0 ? (totalHits / total) * 100 : 0.0;
  }
}

/// Memory usage point for tracking
class CacheMemoryPoint {
  final DateTime timestamp;
  final int memoryBytes;

  const CacheMemoryPoint({
    required this.timestamp,
    required this.memoryBytes,
  });
}
