import 'dart:async';
import 'package:flutter/widgets.dart';

/// Repository interface for UI cache operations
/// This follows the four-layer dependency rule by defining contracts in the repository layer
abstract class UICacheRepository {
  /// Initialize the UI cache service
  Future<void> initialize();

  /// Create an optimized image provider with enhanced caching
  ImageProvider getOptimizedImage(
    String imageUrl, {
    Duration? cacheDuration,
    double? scale,
    Map<String, String>? headers,
  });

  /// Create an optimized list view with lazy loading
  Widget createOptimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    bool addAutomaticKeepAlives = true,
    bool addRepaintBoundaries = true,
    bool addSemanticIndexes = true,
    double? cacheExtent,
    int? semanticChildCount,
    DragStartBehavior dragStartBehavior = DragStartBehavior.start,
    ScrollViewKeyboardDismissBehavior keyboardDismissBehavior = ScrollViewKeyboardDismissBehavior.manual,
    String? restorationId,
    Clip clipBehavior = Clip.hardEdge,
  });

  /// Create an optimized grid view with lazy loading
  Widget createOptimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required SliverGridDelegate gridDelegate,
    ScrollController? controller,
    bool addAutomaticKeepAlives = true,
    bool addRepaintBoundaries = true,
    bool addSemanticIndexes = true,
    double? cacheExtent,
    int? semanticChildCount,
    DragStartBehavior dragStartBehavior = DragStartBehavior.start,
    ScrollViewKeyboardDismissBehavior keyboardDismissBehavior = ScrollViewKeyboardDismissBehavior.manual,
    String? restorationId,
    Clip clipBehavior = Clip.hardEdge,
  });

  /// Create debounced function
  VoidCallback createDebouncedFunction(
    String key,
    VoidCallback function, {
    Duration delay = const Duration(milliseconds: 300),
  });

  /// Register animation controller for lifecycle management
  void registerAnimationController(String key, AnimationController controller);

  /// Unregister animation controller
  void unregisterAnimationController(String key);

  /// Get performance statistics
  Map<String, dynamic> getPerformanceStats();

  /// Clear all caches
  void clearAllCaches();

  /// Dispose of the service
  void dispose();
}

/// Frame timing information for performance monitoring
class FrameTimingInfo {
  final int frameNumber;
  final Duration totalDuration;
  final Duration buildDuration;
  final Duration rasterDuration;

  const FrameTimingInfo(
    this.frameNumber,
    this.totalDuration,
    this.buildDuration,
    this.rasterDuration,
  );
}
