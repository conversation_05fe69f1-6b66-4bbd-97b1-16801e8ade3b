import 'package:http/http.dart' as http;

/// Repository interface for network cache operations
/// This follows the four-layer dependency rule by defining contracts in the repository layer
abstract class NetworkCacheRepository {
  /// Initialize the network cache service
  Future<void> initialize();

  /// Make an optimized HTTP GET request with caching
  Future<http.Response> get(
    String url, {
    Map<String, String>? headers,
    Duration? cacheDuration,
    bool forceRefresh = false,
  });

  /// Make an optimized HTTP POST request
  Future<http.Response> post(
    String url, {
    Map<String, String>? headers,
    Object? body,
    bool skipCache = true,
  });

  /// Make an optimized HTTP PUT request
  Future<http.Response> put(
    String url, {
    Map<String, String>? headers,
    Object? body,
    bool skipCache = true,
  });

  /// Make an optimized HTTP DELETE request
  Future<http.Response> delete(
    String url, {
    Map<String, String>? headers,
    bool skipCache = true,
  });

  /// Batch multiple requests for efficiency
  Future<List<http.Response>> batchRequests(List<BatchRequest> requests);

  /// Invalidate cache by pattern
  Future<void> invalidateByPattern(String pattern);

  /// Clear all cached responses
  void clearCache();

  /// Get cache statistics
  Map<String, dynamic> getCacheStats();

  /// Dispose of the service
  void dispose();
}

/// Batch request configuration
class BatchRequest {
  final String method;
  final String url;
  final Map<String, String>? headers;
  final Object? body;
  final Duration? cacheDuration;

  const BatchRequest({
    required this.method,
    required this.url,
    this.headers,
    this.body,
    this.cacheDuration,
  });
}

/// Cached response wrapper
class CachedResponse {
  final http.Response response;
  final DateTime expiresAt;

  const CachedResponse({
    required this.response,
    required this.expiresAt,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
}
