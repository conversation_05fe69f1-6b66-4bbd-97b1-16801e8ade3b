import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/friend_request.dart';
import '../../statefulbusinesslogic/core/repositories/cache_first_repository_interface.dart';
import '../../statefulbusinesslogic/core/events/mqtt_event_interface.dart';

/// Cache-first repository interface for managing auto-generated friend requests from bubble expiry
///
/// This repository prioritizes local cache and only fetches from remote
/// when triggered by MQTT events or initial load.
abstract class FriendRequestRepository extends RealTimeEventRepository {
  /// Get pending friend requests for the current user (from cache)
  Future<Result<List<FriendRequest>>> getPendingFriendRequests();

  /// Accept a friend request by ID
  Future<Result<void>> acceptFriendRequest(String requestId);

  /// Decline a friend request by ID
  Future<Result<void>> declineFriendRequest(String requestId);

  /// Get a specific friend request by ID (from cache first)
  Future<Result<FriendRequest>> getFriendRequest(String requestId);

  /// Get list of current friends (from cache)
  Future<Result<List<String>>> getFriends();

  /// Stream of pending friend requests (real-time updates)
  Stream<List<FriendRequest>> get pendingFriendRequestsStream;

  /// Handle friend request MQTT events
  @override
  Future<void> handleMqttEvent(MqttEvent event) async {
    if (event is FriendRequestEvent) {
      await processFriendRequestEvent(event);
    }
  }

  /// Process friend request events and update cache
  Future<void> processFriendRequestEvent(FriendRequestEvent event);
}
