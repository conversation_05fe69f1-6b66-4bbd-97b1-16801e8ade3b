/// Sync Repository Interface (Repository Layer)
/// 
/// Defines the contract for initial data synchronization using HTTP/3-first approach.
/// This follows the four-layer dependency rule - Repository layer defines contracts
/// that are implemented in the Provider layer.

import '../../statefulbusinesslogic/core/error/result.dart';
import '../../statefulbusinesslogic/core/models/sync_models.dart';

/// Repository interface for HTTP/3-first initial data synchronization
/// 
/// This repository handles the bulk data fetch that occurs after successful login,
/// using HTTP/3 for optimal performance with automatic fallback to HTTP/2.
/// 
/// Key responsibilities:
/// - Fetch all user data in a single HTTP/3 request
/// - Establish MQTT connection for real-time updates  
/// - Store data in local Drift database atomically
/// - Provide sync metrics and performance data
abstract class SyncRepository {
  /// Perform initial bulk data synchronization
  /// 
  /// This method orchestrates the complete sync process:
  /// 1. Fetches all user data via HTTP/3 (with HTTP/2 fallback)
  /// 2. Establishes MQTT connection for real-time updates (parallel)
  /// 3. Stores data in local Drift database (atomic transaction)
  /// 4. Returns comprehensive sync result with metrics
  /// 
  /// [config] Optional configuration for sync behavior
  /// 
  /// Returns [Result<SyncResult>] containing:
  /// - Success/failure status
  /// - Performance metrics (duration, protocol used, etc.)
  /// - MQTT connection status
  /// - Data synchronization details
  /// 
  /// Example usage:
  /// ```dart
  /// final result = await syncRepository.performInitialSync();
  /// if (result.isSuccess) {
  ///   final syncResult = result.data!;
  ///   print('Sync completed in ${syncResult.syncDurationMs}ms using ${syncResult.protocolUsed}');
  /// }
  /// ```
  Future<Result<SyncResult>> performInitialSync([SyncConfig? config]);

  /// Get current sync status
  /// 
  /// Returns the current status of any ongoing sync operation.
  /// Useful for showing progress indicators in the UI.
  SyncStatus get currentStatus;

  /// Stream of sync status updates
  /// 
  /// Provides real-time updates of sync progress for UI reactivity.
  /// Emits [SyncStatus] values as the sync progresses through different phases.
  Stream<SyncStatus> get statusStream;

  /// Get sync metrics from the last operation
  /// 
  /// Returns detailed performance metrics from the most recent sync,
  /// or null if no sync has been performed yet.
  SyncMetrics? get lastSyncMetrics;

  /// Cancel any ongoing sync operation
  /// 
  /// Attempts to gracefully cancel the current sync operation.
  /// Returns true if cancellation was successful, false if no operation was in progress.
  Future<bool> cancelSync();

  /// Check if sync is currently in progress
  bool get isSyncing;

  /// Get the last successful sync timestamp
  /// 
  /// Returns the timestamp of the last successful sync operation,
  /// or null if no successful sync has occurred.
  DateTime? get lastSuccessfulSync;
}
