import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../di/injection_container_refactored.dart' as di;
import '../../statefulbusinesslogic/bloc/profile_picture/profile_picture_bloc.dart';
import '../../statefulbusinesslogic/bloc/profile_picture/profile_picture_event.dart';
import '../../statefulbusinesslogic/bloc/profile_picture/profile_picture_state.dart';
import '../../statefulbusinesslogic/core/services/profile_picture_service.dart';
import '../../statefulbusinesslogic/core/services/hopen_cache_manager.dart';
import '../../repositories/local_storage/abstract_profile_picture_repository.dart';
import '../../provider/notifiers/profile_picture_notifier.dart';

// HopenCacheManager and HopenHttpFileService moved to separate file to avoid circular imports

/// A reusable profile picture widget that displays a Superellipse avatar
/// with proper loading states, error handling, and fallback to initials
///
/// This widget uses StatefulWidget to maintain cached state and prevent
/// unnecessary rebuilds and image reloading.
class ProfilePictureWidget extends StatefulWidget {
  const ProfilePictureWidget({
    super.key,
    this.imageUrl,
    this.firstName,
    this.lastName,
    this.radius = 40,
    this.borderRadius,
    this.onTap,
    this.showEditIcon = false,
    this.backgroundColor,
  });
  final String? imageUrl;
  final String? firstName;
  final String? lastName;
  final double radius;
  final double? borderRadius;
  final VoidCallback? onTap;
  final bool showEditIcon;
  final Color? backgroundColor;

  @override
  State<ProfilePictureWidget> createState() => _ProfilePictureWidgetState();
}

class _ProfilePictureWidgetState extends State<ProfilePictureWidget>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true; // Keep widget alive to prevent rebuilds

  // Memoization to prevent unnecessary rebuilds
  String? _lastImageUrl;
  Widget? _memoizedWidget;

  @override
  void initState() {
    super.initState();
    _triggerImageFetch();
  }

  @override
  void didUpdateWidget(ProfilePictureWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only trigger fetch if URL actually changed
    if (oldWidget.imageUrl != widget.imageUrl) {
      _triggerImageFetch();
      _memoizedWidget = null; // Clear memoization
    }
  }

  void _triggerImageFetch() {
    if (widget.imageUrl != null && widget.imageUrl!.startsWith('http')) {
      final userId = _extractUserIdFromUrl(widget.imageUrl!) ?? 'unknown';

      // Trigger fetch in the notifier (non-blocking)
      WidgetsBinding.instance.addPostFrameCallback((_) {
        try {
          final notifier = di.sl<ProfilePictureNotifier>();
          notifier.fetchProfilePicture(userId, widget.imageUrl!);
        } catch (e) {
          print('❌ Error accessing ProfilePictureNotifier: $e');
        }
      });
    }
  }

  bool _shouldRebuild() {
    return _lastImageUrl != widget.imageUrl || _memoizedWidget == null;
  }

  void _updateMemoizationParams() {
    _lastImageUrl = widget.imageUrl;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    // Use memoization to prevent unnecessary rebuilds
    if (_shouldRebuild()) {
      print(
        '🖼️ ProfilePictureWidget: Building new widget for ${widget.imageUrl}',
      );

      final initials = ProfilePictureService.generateInitials(
        widget.firstName,
        widget.lastName,
      );
      final avatarColor = widget.backgroundColor ?? const Color(0xFF6B7280);
      final effectiveBorderRadius =
          widget.borderRadius ?? (widget.radius * 2) * 0.4;

      _memoizedWidget = RepaintBoundary(
        key: ValueKey('profile_${widget.imageUrl}'),
        child: GestureDetector(
          onTap: widget.onTap,
          child: Stack(
            children: [
              Container(
                width: widget.radius * 2,
                height: widget.radius * 2,
                decoration: ShapeDecoration(
                  shape: RoundedSuperellipseBorder(
                    borderRadius: BorderRadius.circular(effectiveBorderRadius),
                  ),
                  color: widget.backgroundColor ?? const Color(0x1AFFFFFF),
                ),
                child: _buildAvatarContent(
                  initials,
                  avatarColor,
                  effectiveBorderRadius,
                ),
              ),
              if (widget.showEditIcon) _buildEditIcon(effectiveBorderRadius),
            ],
          ),
        ),
      );
      _updateMemoizationParams();
    } else {
      print(
        '🖼️ ProfilePictureWidget: Using memoized widget for ${widget.imageUrl}',
      );
    }

    return _memoizedWidget!;
  }

  Widget _buildEditIcon(double effectiveBorderRadius) {
    return Positioned(
      bottom: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: ShapeDecoration(
          shape: RoundedSuperellipseBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius ?? 40),
            side: BorderSide(
              color: Theme.of(context).scaffoldBackgroundColor,
              width: 2,
            ),
          ),
          color: Theme.of(context).primaryColor,
        ),
        child: Icon(
          Icons.camera_alt,
          size: widget.radius * 0.4,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildAvatarContent(
    String initials,
    Color avatarColor,
    double effectiveBorderRadius,
  ) {
    if (widget.imageUrl == null || !widget.imageUrl!.startsWith('http')) {
      return _buildInitialsWidget(initials, avatarColor, effectiveBorderRadius);
    }

    final userId = _extractUserIdFromUrl(widget.imageUrl!) ?? 'unknown';

    // Use Provider pattern to access ProfilePictureNotifier
    try {
      final notifier = di.sl<ProfilePictureNotifier>();

      // Check memory cache first
      final imageData = notifier.getImageData(widget.imageUrl!);
      if (imageData != null) {
        print(
          '🖼️ ProfilePictureWidget: Using memory cache for ${widget.imageUrl}',
        );
        return ClipRRect(
          borderRadius: BorderRadius.circular(effectiveBorderRadius),
          child: Image.memory(
            imageData,
            key: ValueKey('memory_${widget.imageUrl}'),
            width: widget.radius * 2,
            height: widget.radius * 2,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              print('❌ Memory image error: $error');
              return _buildInitialsWidget(
                initials,
                avatarColor,
                effectiveBorderRadius,
              );
            },
          ),
        );
      }

      // Check if loading
      if (notifier.isLoading(widget.imageUrl!)) {
        return _buildLoadingWidget(effectiveBorderRadius);
      }

      // Check for errors
      final error = notifier.getError(widget.imageUrl!);
      if (error != null) {
        print('❌ ProfilePictureNotifier error: $error');
        return _buildInitialsWidget(
          initials,
          avatarColor,
          effectiveBorderRadius,
        );
      }
    } catch (e) {
      print('❌ Error accessing ProfilePictureNotifier: $e');
    }

    // Fallback to CachedNetworkImage (handles disk cache and network)
    print(
      '🖼️ ProfilePictureWidget: Using CachedNetworkImage fallback for ${widget.imageUrl}',
    );
    return ClipRRect(
      borderRadius: BorderRadius.circular(effectiveBorderRadius),
      child: CachedNetworkImage(
        imageUrl: widget.imageUrl!,
        key: ValueKey('cached_${widget.imageUrl}'),
        cacheManager: HopenCacheManager(),
        width: widget.radius * 2,
        height: widget.radius * 2,
        fit: BoxFit.cover,
        placeholder:
            (context, url) => _buildLoadingWidget(effectiveBorderRadius),
        errorWidget: (context, url, error) {
          print('❌ CachedNetworkImage error: $error');
          return _buildInitialsWidget(
            initials,
            avatarColor,
            effectiveBorderRadius,
          );
        },
        fadeInDuration: Duration.zero, // Remove animations to prevent rebuilds
        fadeOutDuration: Duration.zero,
      ),
    );
  }

  Widget _buildLoadingWidget(double effectiveBorderRadius) => Container(
    width: widget.radius * 2,
    height: widget.radius * 2,
    decoration: ShapeDecoration(
      shape: RoundedSuperellipseBorder(
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
      ),
      color: Colors.grey[200],
    ),
    child: Center(
      child: SizedBox(
        width: widget.radius * 0.6,
        height: widget.radius * 0.6,
        child: const CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    ),
  );

  Widget _buildInitialsWidget(
    String initials,
    Color backgroundColor,
    double effectiveBorderRadius,
  ) => Container(
    width: widget.radius * 2,
    height: widget.radius * 2,
    decoration: ShapeDecoration(
      shape: RoundedSuperellipseBorder(
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
      ),
      color: backgroundColor,
    ),
    child: Center(
      child: Text(
        initials,
        style: TextStyle(
          color: Colors.white,
          fontSize: widget.radius * 0.6,
          fontWeight: FontWeight.bold,
        ),
      ),
    ),
  );

  /// Extract user ID from image URL if possible
  String? _extractUserIdFromUrl(String imageUrl) {
    try {
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;

      // Look for user ID in path segments
      for (int i = 0; i < pathSegments.length; i++) {
        if (pathSegments[i] == 'users' && i + 1 < pathSegments.length) {
          return pathSegments[i + 1];
        }
        if (pathSegments[i] == 'media' && i + 1 < pathSegments.length) {
          return pathSegments[i + 1];
        }
      }

      // If no user ID found, try to extract from filename
      final filename = pathSegments.lastOrNull;
      if (filename != null && filename.contains('_')) {
        final parts = filename.split('_');
        if (parts.isNotEmpty) {
          return parts.first;
        }
      }

      return null;
    } catch (e) {
      print('❌ Error extracting user ID from URL: $e');
      return null;
    }
  }
}

/// Profile picture picker dialog
class ProfilePicturePickerDialog extends StatefulWidget {
  const ProfilePicturePickerDialog({
    super.key,
    required this.imageUrl,
    required this.firstName,
    required this.lastName,
  });

  final String? imageUrl;
  final String? firstName;
  final String? lastName;

  @override
  State<ProfilePicturePickerDialog> createState() =>
      _ProfilePicturePickerDialogState();

  /// Show the profile picture picker dialog
  static Future<void> show(
    BuildContext context, {
    required String? imageUrl,
    required String? firstName,
    required String? lastName,
  }) => showDialog(
    context: context,
    barrierDismissible: true,
    barrierColor: Colors.black.withOpacity(
      0.85,
    ), // Proper semi-transparent background
    builder:
        (context) => ProfilePicturePickerDialog(
          imageUrl: imageUrl,
          firstName: firstName,
          lastName: lastName,
        ),
  );
}

class _ProfilePicturePickerDialogState
    extends State<ProfilePicturePickerDialog> {
  late ProfilePictureBloc _profilePictureBloc;
  String? _selectedImagePath;
  bool _isUploadingImage = false;

  @override
  void initState() {
    super.initState();
    _profilePictureBloc = di.sl<ProfilePictureBloc>();
  }

  @override
  void dispose() {
    _profilePictureBloc.close();
    super.dispose();
  }

  void _pickFromGallery() {
    _profilePictureBloc.add(const PickFromGalleryLocalEvent());
  }

  void _takePhoto() {
    _profilePictureBloc.add(const TakePhotoLocalEvent());
  }

  void _validateAndSave() {
    if (_selectedImagePath != null) {
      // Validate and save the new image
      _profilePictureBloc.add(
        ValidateImageEvent(imagePath: _selectedImagePath!),
      );
      // Don't close immediately, wait for validation result
    } else {
      // No new image selected, just close the dialog (keeping existing image)
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final spacingHeight = screenHeight / 48;
    final fieldHeight = screenHeight / 16;
    final avatarSize = screenHeight * 0.25; // Larger size for dialog

    final initials = ProfilePictureService.generateInitials(
      widget.firstName,
      widget.lastName,
    );
    final avatarColor = const Color(0xFF6B7280); // Default gray

    return BlocListener<ProfilePictureBloc, ProfilePictureState>(
      bloc: _profilePictureBloc,
      listener: (context, state) {
        if (state is ProfilePictureLoading) {
          setState(() {
            _isUploadingImage = true;
          });
        } else {
          setState(() {
            _isUploadingImage = false;
          });

          if (state is ProfilePictureSuccess) {
            setState(() {
              _selectedImagePath = state.result.url;
            });
            // Show success message and close dialog
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Profile picture updated successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context);
          } else if (state is ProfilePictureValidated) {
            if (state.isValid) {
              // Image is valid, close dialog
              Navigator.pop(context);
            } else {
              // Show validation error
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.error ?? 'Image validation failed'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          } else if (state is ProfilePictureProcessedLocally) {
            // Update selected image path for local processing
            setState(() {
              _selectedImagePath = state.localPath;
            });
          } else if (state is ProfilePictureError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      },
      child: FadeTransition(
        opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: ModalRoute.of(context)!.animation!,
            curve: Curves.easeOut,
          ),
        ),
        child: SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 0.05),
            end: Offset.zero,
          ).animate(
            CurvedAnimation(
              parent: ModalRoute.of(context)!.animation!,
              curve: Curves.easeOutCubic,
            ),
          ),
          child: Material(
            color:
                Colors
                    .transparent, // Remove the dark overlay since barrierColor handles it
            child: SafeArea(
              child: Padding(
                padding: EdgeInsets.all(spacingHeight * 1.5),
                child: Column(
                  children: [
                    // Spacer to push content down
                    const Spacer(),
                    // Centered content
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Large avatar display
                        Container(
                          width: avatarSize,
                          height: avatarSize,
                          decoration: ShapeDecoration(
                            shape: RoundedSuperellipseBorder(
                              borderRadius: BorderRadius.circular(
                                avatarSize * 0.4,
                              ), // Match contacts_page.dart calculation
                            ),
                            color: avatarColor,
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(
                              avatarSize * 0.4,
                            ), // Match contacts_page.dart calculation
                            child: _buildLargeAvatarContent(
                              _selectedImagePath ?? widget.imageUrl,
                              initials,
                              avatarColor,
                              avatarSize,
                            ),
                          ),
                        ),
                        SizedBox(height: spacingHeight * 2),
                        // Buttons row - matching step5_profile_picture_page.dart dimensions
                        SizedBox(
                          height: fieldHeight,
                          child: Row(
                            children: [
                              Expanded(
                                child: _buildButton(
                                  context: context,
                                  label: 'Gallery',
                                  icon: Icons.photo_library,
                                  onPressed: _pickFromGallery,
                                  fieldHeight: fieldHeight,
                                  spacingHeight: spacingHeight,
                                ),
                              ),
                              SizedBox(width: spacingHeight),
                              Expanded(
                                child: _buildButton(
                                  context: context,
                                  label: 'Camera',
                                  icon: Icons.camera_alt,
                                  onPressed: _takePhoto,
                                  fieldHeight: fieldHeight,
                                  spacingHeight: spacingHeight,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // Modify profile picture button
                        SizedBox(height: spacingHeight),
                        SizedBox(
                          width: double.infinity,
                          height: fieldHeight,
                          child: _buildModifyButton(
                            context: context,
                            onPressed: _validateAndSave,
                            fieldHeight: fieldHeight,
                          ),
                        ),
                      ],
                    ),
                    // Spacer to push content up
                    const Spacer(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLargeAvatarContent(
    String? imageUrl,
    String initials,
    Color avatarColor,
    double avatarSize,
  ) {
    print(
      '🖼️ _buildLargeAvatarContent() - imageUrl: $imageUrl, initials: $initials',
    );

    if (imageUrl != null && imageUrl!.isNotEmpty) {
      print('🖼️ Large avatar - Image URL is provided: $imageUrl');

      if (imageUrl!.startsWith('http')) {
        print(
          '🖼️ Large avatar - Using CachedNetworkImage with HopenCacheManager (bypasses certificate issues + better caching)',
        );
        return CachedNetworkImage(
          imageUrl: imageUrl!,
          cacheManager: HopenCacheManager(),
          width: avatarSize,
          height: avatarSize,
          fit: BoxFit.cover,
          placeholder: (context, url) {
            print('🔄 Large avatar - CachedNetworkImage loading: $url');
            return _buildLargeLoadingWidget(avatarSize);
          },
          errorWidget: (context, url, error) {
            print('❌ Large avatar - CachedNetworkImage error for $url: $error');
            return _buildLargeInitialsWidget(initials, avatarColor, avatarSize);
          },
          fadeInDuration: const Duration(milliseconds: 200),
          fadeOutDuration: const Duration(milliseconds: 100),
        );
      } else {
        print('🖼️ Large avatar - Using Image.file for local file path');
        // Treat as local file path
        return Image.file(
          File(imageUrl!),
          width: avatarSize,
          height: avatarSize,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            print('❌ Large avatar - Local file load error: $error');
            return _buildLargeInitialsWidget(initials, avatarColor, avatarSize);
          },
        );
      }
    }

    print('🖼️ Large avatar - No image URL, showing initials: $initials');
    return _buildLargeInitialsWidget(initials, avatarColor, avatarSize);
  }

  Widget _buildLargeLoadingWidget(double avatarSize) => Container(
    width: avatarSize,
    height: avatarSize,
    color: Colors.grey[200],
    child: Center(
      child: SizedBox(
        width: avatarSize * 0.3,
        height: avatarSize * 0.3,
        child: const CircularProgressIndicator(
          strokeWidth: 3,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    ),
  );

  Widget _buildLargeInitialsWidget(
    String initials,
    Color backgroundColor,
    double avatarSize,
  ) => Container(
    width: avatarSize,
    height: avatarSize,
    decoration: BoxDecoration(
      color: backgroundColor,
      borderRadius: const BorderRadius.all(Radius.circular(18)),
    ),
    child: Center(
      child: Text(
        initials,
        style: TextStyle(
          color: Colors.white,
          fontSize: avatarSize * 0.3,
          fontWeight: FontWeight.bold,
        ),
      ),
    ),
  );

  Widget _buildButton({
    required BuildContext context,
    required String label,
    required IconData icon,
    required VoidCallback? onPressed,
    required double fieldHeight,
    required double spacingHeight,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withValues(alpha: 0.1),
        foregroundColor: Colors.white,
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(18),
          side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
        ),
        padding: EdgeInsets.zero,
        minimumSize: Size.zero,
        maximumSize: const Size(double.infinity, double.infinity),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        fixedSize: Size.fromHeight(fieldHeight),
      ),
      icon: Icon(icon),
      label: Text(label),
    );
  }

  Widget _buildModifyButton({
    required BuildContext context,
    required VoidCallback? onPressed,
    required double fieldHeight,
  }) {
    final isEnabled = !_isUploadingImage;

    return ElevatedButton(
      onPressed: isEnabled ? onPressed : null,
      style: ElevatedButton.styleFrom(
        backgroundColor:
            isEnabled
                ? Colors.blue
                : Colors.grey, // Same as "Next" button in signup
        foregroundColor: Colors.white,
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(18),
        ),
        padding: EdgeInsets.zero,
        minimumSize: const Size(0, 0),
        maximumSize: const Size(double.infinity, double.infinity),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child:
          _isUploadingImage
              ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
              : const Text(
                'Save',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
    );
  }
}
