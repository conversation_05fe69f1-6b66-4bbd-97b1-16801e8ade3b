import 'dart:math';
import 'package:flutter/material.dart';

/// PS3-style animated background widget that renders a shader-based wave effect
/// 
/// This widget creates an animated background using a custom shader that generates
/// flowing wave patterns reminiscent of the PlayStation 3's aesthetic.
class PS3Background extends StatefulWidget {
  /// Optional child widget to display on top of the background
  final Widget? child;
  
  /// Animation duration for one complete cycle
  final Duration duration;
  
  /// Wave intensity multiplier (0.1 to 2.0)
  final double intensity;
  
  /// Color tint for the waves (default: PS3 blue theme)
  final Color waveColor;

  const PS3Background({
    super.key,
    this.child,
    this.duration = const Duration(seconds: 8),
    this.intensity = 1.0,
    this.waveColor = const Color(0xFF185ADB),
  });

  @override
  State<PS3Background> createState() => _PS3BackgroundState();
}

class _PS3BackgroundState extends State<PS3Background>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.linear,
    ));

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          painter: PS3WavePainter(
            time: _animation.value,
            intensity: widget.intensity,
            waveColor: widget.waveColor,
          ),
          size: Size.infinite,
          child: child,
        );
      },
      child: widget.child,
    );
  }
}

/// Custom painter that implements the PS3 shader effect
class PS3WavePainter extends CustomPainter {
  final double time;
  final double intensity;
  final Color waveColor;

  PS3WavePainter({
    required this.time,
    required this.intensity,
    required this.waveColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..shader = _createWaveShader(size);

    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      paint,
    );
  }

  /// Creates a radial gradient shader that simulates the PS3 wave effect
  RadialGradient _createWaveShader(Size size) {
    // Convert the shader parameters to Flutter's gradient system
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * 0.8;
    
    // Create multiple wave layers with different frequencies
    final colors = <Color>[];
    final stops = <double>[];
    
    // Base color (dark blue)
    colors.add(const Color(0xFF0A1931));
    stops.add(0.0);
    
    // Wave layers
    for (int i = 1; i <= 7; i++) {
      final waveIntensity = _calculateWaveIntensity(i, time, intensity);
      final waveColor = _blendColor(
        const Color(0xFF0A1931),
        waveColor,
        waveIntensity * 0.3,
      );
      
      colors.add(waveColor);
      stops.add(0.1 + (i * 0.12));
    }
    
    // Final color
    colors.add(waveColor.withOpacity(0.8));
    stops.add(1.0);
    
    return RadialGradient(
      center: Alignment.center,
      radius: 1.0,
      colors: colors,
      stops: stops,
    );
  }

  /// Calculates wave intensity based on the shader algorithm
  double _calculateWaveIntensity(int waveIndex, double time, double intensity) {
    // Simplified version of the shader algorithm
    final frequency = waveIndex * 3.3;
    final amplitude = 0.3 * cos(waveIndex) * sin(time * frequency);
    final phase = sin(time * 0.2 + waveIndex * 7.1);
    
    return (amplitude * phase * intensity).clamp(0.0, 1.0);
  }

  /// Blends two colors with a given factor
  Color _blendColor(Color base, Color blend, double factor) {
    return Color.lerp(base, blend, factor.clamp(0.0, 1.0))!;
  }

  @override
  bool shouldRepaint(PS3WavePainter oldDelegate) {
    return oldDelegate.time != time ||
           oldDelegate.intensity != intensity ||
           oldDelegate.waveColor != waveColor;
  }
}

/// Alternative implementation using a more complex wave pattern
class PS3ComplexBackground extends StatefulWidget {
  final Widget? child;
  final Duration duration;
  final double intensity;
  final Color primaryColor;
  final Color secondaryColor;

  const PS3ComplexBackground({
    super.key,
    this.child,
    this.duration = const Duration(seconds: 10),
    this.intensity = 1.0,
    this.primaryColor = const Color(0xFF185ADB),
    this.secondaryColor = const Color(0xFF3D2C8D),
  });

  @override
  State<PS3ComplexBackground> createState() => _PS3ComplexBackgroundState();
}

class _PS3ComplexBackgroundState extends State<PS3ComplexBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.linear,
    ));

    _controller.repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return CustomPaint(
          painter: PS3ComplexWavePainter(
            time: _animation.value,
            intensity: widget.intensity,
            primaryColor: widget.primaryColor,
            secondaryColor: widget.secondaryColor,
          ),
          size: Size.infinite,
          child: child,
        );
      },
      child: widget.child,
    );
  }
}

/// Complex wave painter that creates multiple overlapping wave patterns
class PS3ComplexWavePainter extends CustomPainter {
  final double time;
  final double intensity;
  final Color primaryColor;
  final Color secondaryColor;

  PS3ComplexWavePainter({
    required this.time,
    required this.intensity,
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Create multiple wave layers
    for (int i = 1; i <= 8; i++) {
      _drawWaveLayer(canvas, size, i);
    }
  }

  void _drawWaveLayer(Canvas canvas, Size size, int layerIndex) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.overlay
      ..shader = _createLayerShader(size, layerIndex);

    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      paint,
    );
  }

  RadialGradient _createLayerShader(Size size, int layerIndex) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width * (0.3 + layerIndex * 0.1);
    
    final waveValue = _calculateComplexWave(layerIndex, time, intensity);
    final color = layerIndex % 2 == 0 ? primaryColor : secondaryColor;
    
    return RadialGradient(
      center: Alignment.center,
      radius: 1.0,
      colors: [
        color.withOpacity(0.0),
        color.withOpacity(waveValue * 0.2),
        color.withOpacity(waveValue * 0.1),
        color.withOpacity(0.0),
      ],
      stops: const [0.0, 0.3, 0.7, 1.0],
    );
  }

  double _calculateComplexWave(int layerIndex, double time, double intensity) {
    final frequency = layerIndex * 3.3;
    final amplitude = 0.3 * cos(layerIndex) * sin(time * frequency);
    final phase = sin(time * 0.2 + layerIndex * 7.1);
    final modulation = cos(time * 0.5 + layerIndex * 2.1);
    
    return (amplitude * phase * modulation * intensity).clamp(0.0, 1.0);
  }

  @override
  bool shouldRepaint(PS3ComplexWavePainter oldDelegate) {
    return oldDelegate.time != time ||
           oldDelegate.intensity != intensity ||
           oldDelegate.primaryColor != primaryColor ||
           oldDelegate.secondaryColor != secondaryColor;
  }
} 

//
// HERE IS THE ORIGINAL SHADER CODE :
// float f,b,x,y;float3 c=float3(0,.08,.15);for(float i=1;i<8;i++){x=uv.x*1.6+t*sin(i*3.3)*.4+i*4.3;f=.3*cos(i)*sin(x);b=sin(uv.x*sin(i+t*.2)*3.4+cos(i+7.1))*.1+.896;y=smoothstep(b,1,1-abs(uv.y-f))*(b-.8)*2.5;c+=float3(y,y-.04,y*i*.4);}return float4(c,1);

// HERE IS THE SOURCE CODE :
// https://x.com/dankuntz/status/1942434755565191612