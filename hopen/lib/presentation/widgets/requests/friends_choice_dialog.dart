import 'dart:async'; // Ensure 'dart:async' is imported for Timer

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../statefulbusinesslogic/bloc/friend_selection/friend_selection_bloc.dart';
import '../../../statefulbusinesslogic/bloc/friend_selection/friend_selection_event.dart';
import '../../../statefulbusinesslogic/bloc/friend_selection/friend_selection_state.dart';
import '../../../statefulbusinesslogic/core/models/user_model.dart';
import '../../utils/color_transition_controller.dart';
import '../animated_gradient_text.dart';


class FriendsChoiceDialog extends StatefulWidget {

  const FriendsChoiceDialog({
    required this.bubbleId, required this.bubbleName, required this.formerMembers, super.key,
  });
  final String bubbleId;
  final String bubbleName;
  final List<UserModel> formerMembers;

  static Future<void> show(
    BuildContext context, {
    required String bubbleId,
    required String bubbleName,
    required List<UserModel> formerMembers,
  }) {
    // Get the FriendSelectionBloc from dependency injection
    final friendSelectionBloc = BlocProvider.of<FriendSelectionBloc>(context);

    // Load the former members into the BLoC
    friendSelectionBloc.add(LoadFormerMembersEvent(
      bubbleId: bubbleId,
      bubbleName: bubbleName,
    ),);

    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.9),
      builder: (context) => BlocProvider.value(
        value: friendSelectionBloc,
        child: FriendsChoiceDialog(
          bubbleId: bubbleId,
          bubbleName: bubbleName,
          formerMembers: formerMembers.take(4).toList(),
        ),
      ),
    );
  }

  @override
  State<FriendsChoiceDialog> createState() => _FriendsChoiceDialogState();
}

class _FriendsChoiceDialogState extends State<FriendsChoiceDialog> {
  late FriendSelectionBloc _friendSelectionBloc;
  final Set<String> _selectedMemberIds = {};
  bool _isSubmitting = false;
  String? _errorMessage;

  /// Controller for managing color transitions
  late ColorTransitionController _colorController;

  @override
  void initState() {
    super.initState();
    _friendSelectionBloc = BlocProvider.of<FriendSelectionBloc>(context);
    _colorController = ColorTransitionController();
    _colorController.startColorLoop();

    // Listen to color changes and update the UI
    _colorController.addListener(() {
      if (mounted) setState(() {});
    });
  }

  @override
  void dispose() {
    _colorController.dispose();
    super.dispose();
  }

  double _getImageSize(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 370) {
      return 120;
    } else if (width < 600) {
      return 140;
    } else {
      return 150;
    }
  }

  double _getDialogVerticalSpacing(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width < 370) {
      return 6;
    } else if (width < 600) {
      return 8;
    } else {
      return 10;
    }
  }


  @override
  Widget build(BuildContext context) => BlocListener<FriendSelectionBloc, FriendSelectionState>(
      listener: (context, state) {
        // Handle state changes
        if (state.status == FriendSelectionStatus.submitting) {
          setState(() {
            _isSubmitting = true;
            _errorMessage = null;
          });
        } else if (state.status == FriendSelectionStatus.success) {
          // Close the dialog on success
          Navigator.of(context).pop();
        } else if (state.status == FriendSelectionStatus.error) {
          setState(() {
            _isSubmitting = false;
            // Convert the error message to a user-friendly message
            if (state.errorMessage != null) {
              // Use the error message directly
              _errorMessage = state.errorMessage;
            } else {
              _errorMessage = 'An unexpected error occurred. Please try again.';
            }
          });
        }
      },
      child: _buildDialogContent(context),
    );

  Widget _buildDialogContent(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final consistentTileHeight = screenHeight / 12;
    final contentAreaHeight = screenHeight * 0.5;

    // Get current color and glow effect from the controller
    final currentActiveColor = _colorController.currentColor;
    final titleGlowEffect = _colorController.getGlowEffect();

    // --- MODIFICATION REMOVED: Dynamic buttonText logic ---
    // String buttonText;
    // if (_selectedMemberIds.isEmpty && widget.formerMembers.isNotEmpty) {
    //   buttonText = 'I choose no one';
    // } else if (_selectedMemberIds.length == 1) {
    //   buttonText = 'I\'ve chosen my new friend';
    // } else if (_selectedMemberIds.length > 1) {
    //   buttonText = 'I\'ve chosen my new friends';
    // } else {
    //      buttonText = 'I\'ve chosen my new friend(s)';
    // }
    // --- MODIFICATION END ---

    final imageSize = _getImageSize(context);
    final verticalSpacing = _getDialogVerticalSpacing(context);

    return AlertDialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
      insetPadding: EdgeInsets.zero,
      titlePadding: const EdgeInsets.only(
        top: 16,
        left: 24,
        right: 24,
      ),
      contentPadding: const EdgeInsets.symmetric(),
      title: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedGradientText(
              text: 'Your bubble has popped!',
              style: TextStyle(
                fontFamily: 'Omnes',
                fontSize: ColorTransitionController.getTitleSize(context),
                fontWeight: FontWeight.bold,
              ),
              colors: const <Color>[
                Color(0xFFFF00FF), // #f0f
                Color(0xFFFB43BB), // #fb43bb
                Color(0xFFF3C935), // #f3c935
                Color(0xFFF0FF00), // #f0ff00
                Color(0xFFC4FF2D), // #c4ff2d
                Color(0xFF91FF64), // #91ff64
                Color(0xFF64FF93), // #64ff93
                Color(0xFF40FFBA), // #40ffba
                Color(0xFF24FFD8), // #24ffd8
                Color(0xFF10FFED), // #10ffed
                Color(0xFF04FFFA), // #04fffa
                Color(0xFF00FFFF), // aqua
              ],
              stops: const <double>[
                0,
                0.12,
                0.37,
                0.48,
                0.53,
                0.6,
                0.67,
                0.74,
                0.81,
                0.88,
                0.94,
                1,
              ],
              duration: const Duration(seconds: 8),
            ),
            SizedBox(height: verticalSpacing),
            Image.asset(
              'assets/images/3d/500px/normal/notify-heart.png',
              width: imageSize,
              height: imageSize,
              errorBuilder: (context, error, stackTrace) {
                print('Error loading image: $error');
                return Icon(Icons.favorite_border, color: Colors.white54, size: imageSize * 0.5);
              },
            ),
            SizedBox(height: verticalSpacing),
            Text(
              "It's time to choose your new friends.",
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.white70,
                fontSize: ColorTransitionController.getSubtitleSize(context),
              ),
            ),
          ],
        ),
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width,
        height: widget.formerMembers.isEmpty ? 1 : contentAreaHeight,
        child: widget.formerMembers.isEmpty
            ? _buildEmptyMembersList(context)
            : _buildMembersList(context, consistentTileHeight),
      ),
      actions: [
        Column(
           mainAxisAlignment: MainAxisAlignment.center,

          children: [
            Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Text(
                'You will be able to interact with each one of your new friends individually.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.7),
                  fontSize: 12,
                  fontFamily: 'Omnes',
                ),
              ),
            ),
            if (_errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text(
                  _errorMessage!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.red,
                    fontSize: 14,
                  ),
                ),
              ),
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _isSubmitting
                    ? null
                    : () {
                        print('Selected member IDs: $_selectedMemberIds');
                        // Logic to show confirmation dialog or submit directly
                        if (widget.formerMembers.isNotEmpty && _selectedMemberIds.length < widget.formerMembers.length) {
                          _showConfirmationDialog(context);
                        } else {
                          _submitSelections();
                        }
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF00A9FF),
                  foregroundColor: Colors.white,
                  disabledBackgroundColor: Colors.grey.withValues(alpha: 0.3),
                  disabledForegroundColor: Colors.white.withValues(alpha: 0.5),
                  shape: RoundedSuperellipseBorder(
                    borderRadius: BorderRadius.circular(18),
                  ),
                  elevation: 5,
                  shadowColor: const Color(0xFF00A9FF).withValues(alpha: 0.5),
                ),
                child: const Text(
                        'Confirm',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEmptyMembersList(BuildContext context) => Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          'No former members to select from.',
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.white54,
            fontSize: ColorTransitionController.getSubtitleSize(context) - 2,
          ),
        ),
      ),
    );

  Widget _buildMembersList(BuildContext context, double tileHeight) {
    final membersToDisplay = widget.formerMembers.take(4).toList();
    final screenHeight = MediaQuery.of(context).size.height;
    final verticalPadding = screenHeight / 96;

    return ListView.builder(
      padding: const EdgeInsets.only(left: 20, right: 20, top: 2, bottom: 2),
      itemCount: membersToDisplay.length,
      itemBuilder: (context, index) {
        final member = membersToDisplay[index];
        final isSelected = _selectedMemberIds.contains(member.id);
        return Padding(
          padding: EdgeInsets.symmetric(vertical: verticalPadding),
          child: _MemberChoiceCard(
            member: member,
            isSelected: isSelected,
            tileHeight: tileHeight,
            onTap: () {
              setState(() {
                if (isSelected) {
                  _selectedMemberIds.remove(member.id);
                } else {
                  _selectedMemberIds.add(member.id);
                }
              });
            },
          ),
        );
      },
    );
  }

  void _showConfirmationDialog(BuildContext context) {
    final unselectedCount = widget.formerMembers.length - _selectedMemberIds.length;

    if (unselectedCount <= 0 && widget.formerMembers.isNotEmpty) {
        Navigator.of(context).pop();
        return;
    }

    final unselectedNames = widget.formerMembers
        .where((member) => !_selectedMemberIds.contains(member.id))
        .map((member) => member.firstName ?? 'Unknown')
        .toList();

    final String namesText;
    if (unselectedNames.length > 1) {
      final lastName = unselectedNames.last;
      final otherNames = unselectedNames.take(unselectedNames.length - 1).join(', ');
      namesText = '$otherNames and $lastName';
    } else {
      namesText = unselectedNames.join();
    }

    String contentPart1;
    if (_selectedMemberIds.isEmpty && widget.formerMembers.isNotEmpty) {
        contentPart1 = unselectedCount == 1
            ? '$namesText will not become your friend.'
            : 'These contacts will not become your friends: $namesText.';
    } else {
        contentPart1 = unselectedCount == 1
            ? '$namesText will not become your friend.'
            : 'These contacts will not become your friends: $namesText.';
    }

    final contentPart2 = unselectedCount == 1
        ? 'You will need to share a new bubble with him/her to have another chance to become friends.'
        : 'You will need to share a new bubble with them to have another chance to become friends.';

    final fullContent = '$contentPart1\n\n$contentPart2';

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
          backgroundColor: const Color(0xFF1A2B4D),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(25)),
          title: const Text(
            'Are you sure?',
            style: TextStyle(
              color: Colors.red,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          content: Text(
            fullContent,
            style: const TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
          actionsAlignment: MainAxisAlignment.spaceEvenly,
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
              child: const Text(
                'Let me rethink',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                _submitSelections();
              },
              child: const Text(
                'Confirm',
                style: TextStyle(color: Color(0xFF00FFFF), fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
    );
  }

  /// Submits the selected member IDs to the BLoC.
  void _submitSelections() {
    _friendSelectionBloc.add(SubmitFriendSelectionsEvent(
      bubbleId: widget.bubbleId,
      selectedMemberIds: _selectedMemberIds.toList(),
    ),);
  }
}

class _MemberChoiceCard extends StatelessWidget {

  const _MemberChoiceCard({
    required this.member,
    required this.isSelected,
    required this.onTap,
    required this.tileHeight,
  });
  final UserModel member;
  final bool isSelected;
  final VoidCallback onTap;
  final double tileHeight;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth = screenWidth - 40;
    final cardHeight = tileHeight;
    final avatarSize = cardHeight * 0.7;

    final currentPadding = isSelected
        ? const EdgeInsets.symmetric(horizontal: 11, vertical: 7)
        : const EdgeInsets.symmetric(horizontal: 12, vertical: 8);

    const selectedGlowColor = Color(0xFF00FFFF);

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        curve: Curves.easeInOut,
        width: cardWidth,
        height: cardHeight,
        padding: currentPadding,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF00A9FF).withValues(alpha: 0.3) : Colors.white.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: isSelected ? selectedGlowColor : Colors.white.withValues(alpha: 0.2),
            width: isSelected ? 2.0 : 1.0,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: selectedGlowColor.withValues(alpha: 0.6),
                    blurRadius: 12,
                    spreadRadius: 2,
                  ),
                ]
              : [],
        ),
        child: Row(
          children: [
            Container(
              width: avatarSize,
              height: avatarSize,
              decoration: ShapeDecoration(
                shape: RoundedSuperellipseBorder(
                  borderRadius: BorderRadius.circular(avatarSize * 0.4),
                  side: BorderSide(color: Colors.white.withValues(alpha: 0.2), width: 1.5),
                ),
                color: Colors.white.withValues(alpha: 0.3),
              ),
              child: Center(
                child: Text(
                  (member.firstName?.isNotEmpty == true) ? member.firstName![0].toUpperCase() : '?',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: avatarSize * 0.4,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    member.firstName ?? 'Unknown',
                    textAlign: TextAlign.left,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      fontSize: cardHeight * 0.25,
                    ),
                  ),
                  if (member.username != null && member.username!.isNotEmpty) ...[
                    const SizedBox(height: 2),
                    Text(
                      '@${member.username}',
                      textAlign: TextAlign.left,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: cardHeight * 0.18,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Theme(
              data: Theme.of(context).copyWith(
                unselectedWidgetColor: Colors.white70,
              ),
              child: Checkbox(
                value: isSelected,
                onChanged: (value) {
                  onTap();
                },
                activeColor: selectedGlowColor,
                checkColor: Colors.black,
                visualDensity: VisualDensity.compact,
                side: BorderSide(color: Colors.white.withValues(alpha: 0.5), width: 1.5),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}