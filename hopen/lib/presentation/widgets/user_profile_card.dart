import 'dart:ui' show ImageFilter;

import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

import '../../statefulbusinesslogic/core/services/activity_status_service.dart';
import 'name_edit_dialog.dart';
import 'online_status_indicator.dart';
import 'profile_picture_widget.dart';

/// A card widget to display summarized user profile information with dynamic online status.
///
/// This widget listens to the ActivityStatusService and automatically updates
/// the online status based on app lifecycle and user preferences.
///
/// This follows the four-layer dependency rule by being in the presentation layer
/// and only depending on the business logic layer (ActivityStatusService).
class UserProfileCard extends StatefulWidget {
  const UserProfileCard({
    required this.firstName,
    required this.lastName,
    required this.username,
    required this.baseTileHeight,
    super.key,
    this.avatarUrl,
    this.onProfilePictureTap,
    this.onNameEdit,
  });

  final String firstName;
  final String lastName;
  final String username;
  final String? avatarUrl;
  final double baseTileHeight;
  final VoidCallback? onProfilePictureTap;
  final Function(String firstName, String lastName, String username)? onNameEdit;

  @override
  State<UserProfileCard> createState() => _UserProfileCardState();
}

class _UserProfileCardState extends State<UserProfileCard> {
  late ActivityStatusService _activityStatusService;
  bool _isOnlineVisible = true; // Default to true (show activity by default)

  @override
  void initState() {
    super.initState();
    _activityStatusService = ActivityStatusService.instance;
    
    // Initialize the service if not already done
    _activityStatusService.initialize();
    
    // Listen to visibility changes
    _activityStatusService.visibilityStream.listen((isVisible) {
      if (mounted) {
        setState(() {
          _isOnlineVisible = isVisible;
        });
      }
    });
    
    // Set initial visibility
    _isOnlineVisible = _activityStatusService.isOnlineVisible;
  }

  void _showProfilePictureDialog() {
    ProfilePicturePickerDialog.show(
      context,
      imageUrl: widget.avatarUrl,
      firstName: widget.firstName,
      lastName: widget.lastName,
    );
  }

  void _showNameEditDialog() {
    NameEditDialog.show(
      context,
      initialFirstName: widget.firstName,
      initialLastName: widget.lastName,
      initialUsername: widget.username,
    ).then((result) {
      if (result != null && widget.onNameEdit != null) {
        widget.onNameEdit!(
          result['firstName']!,
          result['lastName']!,
          result['username']!,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    print('🖼️ UserProfileCard.build() - avatarUrl: ${widget.avatarUrl}, firstName: ${widget.firstName}, lastName: ${widget.lastName}, isOnlineVisible: $_isOnlineVisible');

    final targetHeight = widget.baseTileHeight * 1.6; // Match BubbleStatusCard height
    final avatarSize = targetHeight * 0.7; // Match ProfileOptionTile avatar proportion
    final verticalPadding = MediaQuery.of(context).size.height / 96;
    final nameFontSize = widget.baseTileHeight * 0.25;
    final usernameFontSize = widget.baseTileHeight * 0.18;
    final onlineIndicatorSize = nameFontSize * 0.5; // Adjusted size for indicator
    
    // Responsive border radius calculation
    final responsiveBorderRadius = (targetHeight * 0.40).clamp(20.0, 40.0);

    return Padding(
      padding: EdgeInsets.only(bottom: verticalPadding),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(responsiveBorderRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Container(
            height: targetHeight,
            decoration: ShapeDecoration(
              gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFF00C4FF), Color(0xFF00A3E0)],
              ),
              shape: RoundedSuperellipseBorder(
                borderRadius: BorderRadius.circular(responsiveBorderRadius),
                side: BorderSide(color: Colors.white.withValues(alpha: 0.3)),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => _showProfilePictureDialog(),
                  child: ProfilePictureWidget(
                    imageUrl: widget.avatarUrl,
                    firstName: widget.firstName,
                    lastName: widget.lastName,
                    radius: avatarSize / 2,
                    backgroundColor: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: GestureDetector(
                    onTap: _showNameEditDialog,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            AutoSizeText(
                              '${widget.firstName} ${widget.lastName}',
                              style: TextStyle(
                                fontSize: nameFontSize,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (_isOnlineVisible)
                              Padding(
                                padding: const EdgeInsets.only(left: 8),
                                child: OnlineStatusIndicator(
                                  isOnline: true, // Show indicator only when visible
                                  size: onlineIndicatorSize,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        AutoSizeText(
                          '@${widget.username}',
                          style: TextStyle(
                            fontSize: usernameFontSize,
                            color: Colors.white.withValues(alpha: 0.7),
                          ),
                          maxLines: 1,
                          minFontSize: 10,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
