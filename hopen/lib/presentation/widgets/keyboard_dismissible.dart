import 'package:flutter/material.dart';

/// A widget that dismisses the keyboard when tapping outside of text fields.
/// 
/// This widget wraps its child with a GestureDetector that unfocuses the current
/// focus scope when tapping on non-interactive areas. This is a common UX pattern
/// that improves the user experience by allowing users to easily dismiss the keyboard.
/// 
/// Usage:
/// ```dart
/// KeyboardDismissible(
///   child: Scaffold(
///     body: Column(
///       children: [
///         TextField(...),
///         // Other widgets
///       ],
///     ),
///   ),
/// )
/// ```
class KeyboardDismissible extends StatelessWidget {
  /// Creates a KeyboardDismissible widget.
  /// 
  /// The [child] parameter is required and represents the widget tree that
  /// should be wrapped with keyboard dismissal functionality.
  const KeyboardDismissible({
    required this.child,
    super.key,
  });

  /// The widget below this widget in the tree.
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // Use onTap to dismiss keyboard when tapping outside text fields
      onTap: () {
        // Unfocus the current focus scope, which dismisses the keyboard
        FocusScope.of(context).unfocus();
      },
      // Use HitTestBehavior.translucent to ensure taps are detected even on transparent areas
      behavior: HitTestBehavior.translucent,
      // Don't consume the tap event, allow it to propagate to child widgets
      child: child,
    );
  }
}

/// A more advanced version of KeyboardDismissible that provides additional configuration options.
/// 
/// This widget allows you to customize the behavior of keyboard dismissal, such as
/// whether to dismiss on scroll, custom tap handling, and more.
class AdvancedKeyboardDismissible extends StatelessWidget {
  /// Creates an AdvancedKeyboardDismissible widget.
  /// 
  /// The [child] parameter is required and represents the widget tree that
  /// should be wrapped with keyboard dismissal functionality.
  /// 
  /// The [dismissOnScroll] parameter determines whether the keyboard should be
  /// dismissed when the user scrolls. Defaults to true.
  /// 
  /// The [onTapOutside] parameter is an optional callback that is called when
  /// the user taps outside of text fields. If provided, this callback is called
  /// before the keyboard is dismissed.
  const AdvancedKeyboardDismissible({
    required this.child,
    this.dismissOnScroll = true,
    this.onTapOutside,
    super.key,
  });

  /// The widget below this widget in the tree.
  final Widget child;

  /// Whether to dismiss the keyboard when scrolling. Defaults to true.
  final bool dismissOnScroll;

  /// Optional callback that is called when tapping outside text fields.
  /// This callback is called before the keyboard is dismissed.
  final VoidCallback? onTapOutside;

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        // Dismiss keyboard on scroll if enabled
        if (dismissOnScroll && notification is ScrollUpdateNotification) {
          FocusScope.of(context).unfocus();
        }
        return false; // Don't consume the notification
      },
      child: GestureDetector(
        onTap: () {
          // Call the optional callback first
          onTapOutside?.call();
          // Then unfocus to dismiss the keyboard
          FocusScope.of(context).unfocus();
        },
        behavior: HitTestBehavior.translucent,
        child: child,
      ),
    );
  }
} 