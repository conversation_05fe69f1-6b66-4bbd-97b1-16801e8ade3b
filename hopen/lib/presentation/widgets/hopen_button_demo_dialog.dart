import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// A dialog that demonstrates the different states of the Hopen button
/// in the bottom navigation bar based on user's bubble and call status.
class HopenButtonDemoDialog extends StatefulWidget {
  const HopenButtonDemoDialog({super.key});

  /// Static method to easily show the dialog with proper styling.
  static Future<void> show(BuildContext context) => showDialog<void>(
    context: context,
    barrierColor: Colors.black.withValues(
      alpha: 0.85,
    ), // Set darkening effect to 0.85 opacity
    builder: (dialogContext) => const HopenButtonDemoDialog(),
  );

  @override
  State<HopenButtonDemoDialog> createState() => _HopenButtonDemoDialogState();
}

class _HopenButtonDemoDialogState extends State<HopenButtonDemoDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    )..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions for responsive sizing
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    
    // Simplified responsive calculations using established patterns
    final titleSize = (screenWidth * 0.045).clamp(16.0, 20.0);
    final descriptionSize = (screenWidth * 0.035).clamp(12.0, 16.0);
    final buttonSize = (screenWidth * 0.15).clamp(60.0, 90.0);
    final spacing = (screenHeight * 0.02).clamp(16.0, 24.0);

    return FadeTransition(
      opacity: Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(
          parent: ModalRoute.of(context)!.animation!,
          curve: Curves.easeOut,
        ),
      ),
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.05),
          end: Offset.zero,
        ).animate(
          CurvedAnimation(
            parent: ModalRoute.of(context)!.animation!,
            curve: Curves.easeOutCubic,
          ),
        ),
        child: Dialog(
          backgroundColor: Colors.transparent,
          insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
          child: GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.85,
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              child: SafeArea(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Dialog title
                      Text(
                        'The Hopen button is deactivated',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: titleSize,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: spacing * 3),

                      // Case 1: Not in a bubble
                      _buildButtonState(
                        context,
                        button: _buildWhiteButton(buttonSize),
                        description: 'When you are not in a bubble, it is deactivated.',
                        descriptionSize: descriptionSize,
                      ),

                      SizedBox(height: spacing),

                      // Case 2: In bubble but no call
                      _buildButtonState(
                        context,
                        button: _buildGradientButton(buttonSize),
                        description: 'When you are in a bubble, it is activated.\nYou can call your bubble anytime.',
                        descriptionSize: descriptionSize,
                      ),

                      SizedBox(height: spacing),

                      // Case 3: In bubble with active call
                      _buildButtonState(
                        context,
                        button: _buildAnimatedGradientButton(buttonSize),
                        description: "When your bubble is in a call, it is animated.\nYou can join you bubble's call anytime.",
                        descriptionSize: descriptionSize,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildButtonState(
    BuildContext context, {
    required Widget button,
    required String description,
    required double descriptionSize,
  }) => Column(
    children: [
      // Center the button
      Center(child: button),
      Text(
        description,
        style: TextStyle(
          color: Colors.white,
          fontSize: descriptionSize,
          height: 1.4,
        ),
        textAlign: TextAlign.center,
      ),
    ],
  );

  Widget _buildWhiteButton(double buttonSize) {
    return SizedBox(
      width: buttonSize,
      height: buttonSize,
      child: SvgPicture.asset(
        'assets/icons/hopen-logo.svg',
        width: buttonSize,
        height: buttonSize,
        colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
        fit: BoxFit.fill,
      ),
    );
  }

  Widget _buildGradientButton(double buttonSize) {
    return SizedBox(
      width: buttonSize,
      height: buttonSize,
      child: ShaderMask(
        shaderCallback:
            (bounds) => const LinearGradient(
              // Using exact colors from the SVG file's internal gradient
              colors: [
                Color(0xFFF000FF), // #f0f
                Color(0xFFFB43BB), // #fb43bb
                Color(0xFFF3C935), // #f3c935
                Color(0xFFF0FF00), // #f0ff00
                Color(0xFFC4FF2D), // #c4ff2d
                Color(0xFF91FF64), // #91ff64
                Color(0xFF64FF93), // #64ff93
                Color(0xFF40FFBA), // #40ffba
                Color(0xFF24FFD8), // #24ffd8
                Color(0xFF10FFED), // #10ffed
                Color(0xFF04FFFA), // #04fffa
                Color(0xFF00FFFF), // #00ffff
              ],
              // Using exact stops from the SVG file
              stops: [
                0.0, // offset="0"
                0.12, // offset=".12"
                0.37, // offset=".37"
                0.48, // offset=".48"
                0.53, // offset=".53"
                0.60, // offset=".6"
                0.67, // offset=".67"
                0.74, // offset=".74"
                0.81, // offset=".81"
                0.88, // offset=".88"
                0.94, // offset=".94"
                1.0, // offset="1" (implicit in the SVG)
              ],
              // Fixed gradient alignment relative to the fixed-size container
              begin: Alignment(-0.9, 0),
              end: Alignment(0.9, 0),
            ).createShader(bounds),
        blendMode: BlendMode.srcIn,
        child: SvgPicture.asset(
          'assets/icons/hopen-logo.svg',
          width: buttonSize,
          height: buttonSize,
          // The SVG will exactly fill the fixed-size container
          fit: BoxFit.fill,
        ),
      ),
    );
  }

  Widget _buildAnimatedGradientButton(double buttonSize) {
    const gradientColors = <Color>[
      Color(0xFFFF00FF), // #ff00ff
      Color(0xFFFB43BB),
      Color(0xFFF3C935),
      Color(0xFFF0FF00),
      Color(0xFFC4FF2D),
      Color(0xFF91FF64),
      Color(0xFF64FF93),
      Color(0xFF40FFBA),
      Color(0xFF24FFD8),
      Color(0xFF10FFED),
      Color(0xFF04FFFA),
      Color(0xFF00FFFF),
    ];

    const gradientStops = <double>[
      0,
      0.12,
      0.37,
      0.48,
      0.53,
      0.60,
      0.67,
      0.74,
      0.81,
      0.88,
      0.94,
      1,
    ];

    return SizedBox(
      width: buttonSize,
      height: buttonSize,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) => ShaderMask(
          shaderCallback: (bounds) => LinearGradient(
            colors: gradientColors,
            stops: gradientStops,
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            transform: GradientRotation(_animationController.value * 2 * math.pi),
          ).createShader(bounds),
          blendMode: BlendMode.srcIn,
          child: SvgPicture.asset(
            'assets/icons/hopen-logo.svg',
            width: buttonSize,
            height: buttonSize,
            fit: BoxFit.fill,
          ),
        ),
      ),
    );
  }
}
