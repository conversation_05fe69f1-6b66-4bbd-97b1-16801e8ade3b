import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../di/injection_container_refactored.dart' as di;
import '../../provider/notifiers/profile_picture_notifier.dart';
import '../../statefulbusinesslogic/core/services/profile_picture_service.dart';
import '../../statefulbusinesslogic/core/services/hopen_cache_manager.dart';

/// Optimized profile picture widget using Provider pattern for state management
/// 
/// This widget eliminates the rebuild/reload issue by:
/// 1. Using Provider for state management (prevents unnecessary rebuilds)
/// 2. Multi-layered caching (memory -> disk -> network)
/// 3. Memoization of widget builds
/// 4. Stable keys to prevent widget recreation
class OptimizedProfilePictureWidget extends StatefulWidget {
  const OptimizedProfilePictureWidget({
    super.key,
    this.imageUrl,
    this.firstName,
    this.lastName,
    this.userId,
    this.radius = 40,
    this.borderRadius,
    this.onTap,
    this.showEditIcon = false,
    this.backgroundColor,
  });

  final String? imageUrl;
  final String? firstName;
  final String? lastName;
  final String? userId; // Required for proper caching
  final double radius;
  final double? borderRadius;
  final VoidCallback? onTap;
  final bool showEditIcon;
  final Color? backgroundColor;

  @override
  State<OptimizedProfilePictureWidget> createState() => _OptimizedProfilePictureWidgetState();
}

class _OptimizedProfilePictureWidgetState extends State<OptimizedProfilePictureWidget> 
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true; // Keep widget alive to prevent rebuilds

  // Memoization variables to prevent unnecessary rebuilds
  String? _lastImageUrl;
  String? _lastUserId;
  Widget? _memoizedWidget;

  @override
  void initState() {
    super.initState();
    _triggerImageFetch();
  }

  @override
  void didUpdateWidget(OptimizedProfilePictureWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Only trigger fetch if URL or userId actually changed
    if (oldWidget.imageUrl != widget.imageUrl || oldWidget.userId != widget.userId) {
      _triggerImageFetch();
      _memoizedWidget = null; // Clear memoization
    }
  }

  void _triggerImageFetch() {
    if (widget.imageUrl != null && widget.imageUrl!.startsWith('http')) {
      final userId = widget.userId ?? _extractUserIdFromUrl(widget.imageUrl!) ?? 'unknown';
      
      // Trigger fetch in the notifier (non-blocking)
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final notifier = di.sl<ProfilePictureNotifier>();
        notifier.fetchProfilePicture(userId, widget.imageUrl!);
      });
    }
  }

  bool _shouldRebuild() {
    return _lastImageUrl != widget.imageUrl || 
           _lastUserId != widget.userId ||
           _memoizedWidget == null;
  }

  void _updateMemoizationParams() {
    _lastImageUrl = widget.imageUrl;
    _lastUserId = widget.userId;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    // Use memoization to prevent unnecessary rebuilds
    if (_shouldRebuild()) {
      debugPrint('🖼️ OptimizedProfilePictureWidget: Building new widget for ${widget.imageUrl}');
      
      _memoizedWidget = RepaintBoundary(
        key: ValueKey('profile_${widget.imageUrl}_${widget.userId}'),
        child: _buildProfilePicture(),
      );
      _updateMemoizationParams();
    } else {
      debugPrint('🖼️ OptimizedProfilePictureWidget: Using memoized widget for ${widget.imageUrl}');
    }

    return _memoizedWidget!;
  }

  Widget _buildProfilePicture() {
    final initials = ProfilePictureService.generateInitials(widget.firstName, widget.lastName);
    final avatarColor = widget.backgroundColor ?? const Color(0xFF6B7280);
    final effectiveBorderRadius = widget.borderRadius ?? (widget.radius * 2) * 0.4;

    return GestureDetector(
      onTap: widget.onTap,
      child: Stack(
        children: [
          Container(
            width: widget.radius * 2,
            height: widget.radius * 2,
            decoration: ShapeDecoration(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(effectiveBorderRadius),
              ),
              color: widget.backgroundColor ?? const Color(0x1AFFFFFF),
            ),
            child: _buildAvatarContent(initials, avatarColor, effectiveBorderRadius),
          ),
          if (widget.showEditIcon) _buildEditIcon(effectiveBorderRadius),
        ],
      ),
    );
  }

  Widget _buildAvatarContent(String initials, Color avatarColor, double effectiveBorderRadius) {
    if (widget.imageUrl == null || !widget.imageUrl!.startsWith('http')) {
      return _buildInitialsWidget(initials, avatarColor, effectiveBorderRadius);
    }

    final userId = widget.userId ?? _extractUserIdFromUrl(widget.imageUrl!) ?? 'unknown';

    return Consumer<ProfilePictureNotifier>(
      builder: (context, notifier, child) {
        // Check memory cache first
        final imageData = notifier.getImageData(widget.imageUrl!);
        if (imageData != null) {
          debugPrint('🖼️ OptimizedProfilePictureWidget: Using memory cache for ${widget.imageUrl}');
          return ClipRRect(
            borderRadius: BorderRadius.circular(effectiveBorderRadius),
            child: Image.memory(
              imageData,
              key: ValueKey('memory_${widget.imageUrl}'),
              width: widget.radius * 2,
              height: widget.radius * 2,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                debugPrint('❌ Memory image error: $error');
                return _buildInitialsWidget(initials, avatarColor, effectiveBorderRadius);
              },
            ),
          );
        }

        // Check if loading
        if (notifier.isLoading(widget.imageUrl!)) {
          return _buildLoadingWidget(effectiveBorderRadius);
        }

        // Check for errors
        final error = notifier.getError(widget.imageUrl!);
        if (error != null) {
          debugPrint('❌ ProfilePictureNotifier error: $error');
          return _buildInitialsWidget(initials, avatarColor, effectiveBorderRadius);
        }

        // Fallback to CachedNetworkImage (handles disk cache and network)
        debugPrint('🖼️ OptimizedProfilePictureWidget: Using CachedNetworkImage fallback for ${widget.imageUrl}');
        return ClipRRect(
          borderRadius: BorderRadius.circular(effectiveBorderRadius),
          child: CachedNetworkImage(
            imageUrl: widget.imageUrl!,
            key: ValueKey('cached_${widget.imageUrl}'),
            cacheManager: HopenCacheManager(),
            width: widget.radius * 2,
            height: widget.radius * 2,
            fit: BoxFit.cover,
            placeholder: (context, url) => _buildLoadingWidget(effectiveBorderRadius),
            errorWidget: (context, url, error) {
              debugPrint('❌ CachedNetworkImage error: $error');
              return _buildInitialsWidget(initials, avatarColor, effectiveBorderRadius);
            },
            fadeInDuration: Duration.zero, // Remove animations to prevent rebuilds
            fadeOutDuration: Duration.zero,
          ),
        );
      },
    );
  }

  Widget _buildInitialsWidget(String initials, Color backgroundColor, double effectiveBorderRadius) {
    return Container(
      width: widget.radius * 2,
      height: widget.radius * 2,
      decoration: ShapeDecoration(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(effectiveBorderRadius),
        ),
        color: backgroundColor,
      ),
      child: Center(
        child: Text(
          initials,
          style: TextStyle(
            color: Colors.white,
            fontSize: widget.radius * 0.6,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingWidget(double effectiveBorderRadius) {
    return Container(
      width: widget.radius * 2,
      height: widget.radius * 2,
      decoration: ShapeDecoration(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(effectiveBorderRadius),
        ),
        color: Colors.grey[300],
      ),
      child: Center(
        child: SizedBox(
          width: widget.radius * 0.8,
          height: widget.radius * 0.8,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[600]!),
          ),
        ),
      ),
    );
  }

  Widget _buildEditIcon(double effectiveBorderRadius) {
    return Positioned(
      bottom: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: ShapeDecoration(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(widget.borderRadius ?? 40),
            side: BorderSide(
              color: Theme.of(context).scaffoldBackgroundColor,
              width: 2,
            ),
          ),
          color: Theme.of(context).primaryColor,
        ),
        child: Icon(
          Icons.camera_alt,
          size: widget.radius * 0.4,
          color: Colors.white,
        ),
      ),
    );
  }

  String? _extractUserIdFromUrl(String imageUrl) {
    try {
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;

      for (int i = 0; i < pathSegments.length; i++) {
        if (pathSegments[i] == 'users' && i + 1 < pathSegments.length) {
          return pathSegments[i + 1];
        }
        if (pathSegments[i] == 'media' && i + 1 < pathSegments.length) {
          return pathSegments[i + 1];
        }
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error extracting user ID from URL: $e');
      return null;
    }
  }
}
