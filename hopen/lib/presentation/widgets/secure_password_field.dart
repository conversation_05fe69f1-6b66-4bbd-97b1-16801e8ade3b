import 'dart:async';

import 'package:flutter/material.dart';

import '../../statefulbusinesslogic/core/enums/password_security_status.dart';
import '../../statefulbusinesslogic/core/services/password_security_service.dart';
import '../../statefulbusinesslogic/core/services/security_service.dart';
import '../../statefulbusinesslogic/core/services/logging_service.dart';

class SecurePasswordField extends StatefulWidget {

  const SecurePasswordField({
    required this.controller, 
    required this.hintText, 
    super.key,
    this.validator,
    this.fontSize = 16,
    this.showSecurityIndicator = true,
    this.onSecurityStatusChanged,
    this.securityService, // Added as optional dependency
  });
  final TextEditingController controller;
  final String hintText;
  final String? Function(String?)? validator;
  final double? fontSize;
  final bool showSecurityIndicator;
  final void Function(PasswordSecurityStatus)? onSecurityStatusChanged;
  final SecurityService? securityService; // Optional security service

  @override
  State<SecurePasswordField> createState() => _SecurePasswordFieldState();
}

class _SecurePasswordFieldState extends State<SecurePasswordField> {
  bool _obscureText = true;
  PasswordSecurityStatus _securityStatus = PasswordSecurityStatus.initial;
  Timer? _debounceTimer;
  bool _isCheckingBreach = false;
  bool _isBreached = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onPasswordChanged);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    widget.controller.removeListener(_onPasswordChanged);
    super.dispose();
  }

  void _onPasswordChanged() {
    final password = widget.controller.text;
    
    // Cancel previous timer
    _debounceTimer?.cancel();
    
    if (password.isEmpty) {
      setState(() {
        _securityStatus = PasswordSecurityStatus.initial;
        _isCheckingBreach = false;
      });
      widget.onSecurityStatusChanged?.call(_securityStatus);
      return;
    }

    // Quick local validation
          final strength = LocalPasswordStrengthEvaluator.evaluatePasswordStrength(password);
    setState(() {
      _securityStatus = PasswordSecurityStatus.checkingLocal;
    });

    // Debounce HiBP check for better UX (wait 1 second after user stops typing)
    _debounceTimer = Timer(const Duration(milliseconds: 1000), () {
      _checkPasswordSecurity(password, strength);
    });
  }

  Future<void> _checkPasswordSecurity(String password, PasswordStrength strength) async {
    if (password.length < 8) {
      setState(() {
        _securityStatus = PasswordSecurityStatus.weak;
        _isCheckingBreach = false;
      });
      widget.onSecurityStatusChanged?.call(_securityStatus);
      return;
    }

    setState(() {
      _isCheckingBreach = true;
      _securityStatus = PasswordSecurityStatus.checkingBreach;
    });

    try {
      // Check breach only if security service is provided
      if (widget.securityService != null) {
        final isBreached = await widget.securityService!.checkPasswordBreach(password);
        
        setState(() {
          _isCheckingBreach = false;
          _isBreached = isBreached;
          _securityStatus = isBreached 
              ? PasswordSecurityStatus.compromised 
              : _mapStrengthToStatus(strength);
        });
      } else {
        // Fall back to local strength assessment if no security service
        setState(() {
          _isCheckingBreach = false;
          _securityStatus = _mapStrengthToStatus(strength);
        });
      }
      
      widget.onSecurityStatusChanged?.call(_securityStatus);
    } catch (e) {
      // If HiBP check fails, fall back to local strength assessment
      setState(() {
        _isCheckingBreach = false;
        _securityStatus = _mapStrengthToStatus(strength);
      });
      widget.onSecurityStatusChanged?.call(_securityStatus);
      LoggingService.warning('Password breach check failed: $e');
    }
  }

  PasswordSecurityStatus _mapStrengthToStatus(PasswordStrength strength) {
    if (strength.score <= 1) {
      return PasswordSecurityStatus.weak;
    } else if (strength.score <= 2) {
      return PasswordSecurityStatus.good;
    } else {
      return PasswordSecurityStatus.strong;
    }
  }

  Future<void> _checkPasswordBreach(String password) async {
    if (password.isEmpty || widget.securityService == null) return;
    
    try {
      final isBreached = await widget.securityService!.checkPasswordBreach(password);
      
      if (mounted) {
        setState(() {
          _isBreached = isBreached;
        });
      }
    } catch (e) {
      // If breach checking fails, don't block the user
      if (mounted) {
        setState(() {
          _isBreached = false;
        });
      }
      LoggingService.warning('Password breach check failed: $e');
    }
  }

  Color _getSecurityColor() {
    switch (_securityStatus) {
      case PasswordSecurityStatus.initial:
      case PasswordSecurityStatus.checkingLocal:
      case PasswordSecurityStatus.checkingBreach:
        return Colors.grey;
      case PasswordSecurityStatus.weak:
        return Colors.orange;
      case PasswordSecurityStatus.compromised:
        return Colors.red;
      case PasswordSecurityStatus.good:
        return const Color(0xFF00FFFF);
      case PasswordSecurityStatus.strong:
      case PasswordSecurityStatus.secure:
        return const Color(0xFF00FFFF);
      case PasswordSecurityStatus.error:
        return Colors.red;
    }
  }

  String _getSecurityText() {
    switch (_securityStatus) {
      case PasswordSecurityStatus.initial:
        return '';
      case PasswordSecurityStatus.checkingLocal:
        return 'Checking password...';
      case PasswordSecurityStatus.checkingBreach:
        return 'Checking security...';
      case PasswordSecurityStatus.weak:
        return 'Weak password';
      case PasswordSecurityStatus.compromised:
        return 'Password found in data breaches';
      case PasswordSecurityStatus.good:
        return 'Good password';
      case PasswordSecurityStatus.strong:
        return 'Strong password';
      case PasswordSecurityStatus.secure:
        return 'Secure password';
      case PasswordSecurityStatus.error:
        return 'Security check failed';
    }
  }

  String _getDynamicLabelText() {
    // Show security status in the label when there's a status to show
    if (_securityStatus != PasswordSecurityStatus.initial) {
      final securityText = _getSecurityText();
      if (securityText.isNotEmpty) {
        return securityText;
      }
    }
    // Fall back to original hint text
    return widget.hintText;
  }

  IconData _getSecurityIcon() {
    switch (_securityStatus) {
      case PasswordSecurityStatus.initial:
      case PasswordSecurityStatus.checkingLocal:
      case PasswordSecurityStatus.checkingBreach:
        return Icons.security;
      case PasswordSecurityStatus.weak:
        return Icons.warning;
      case PasswordSecurityStatus.compromised:
        return Icons.dangerous;
      case PasswordSecurityStatus.good:
        return Icons.check_circle_outline;
      case PasswordSecurityStatus.strong:
      case PasswordSecurityStatus.secure:
        return Icons.verified_user;
      case PasswordSecurityStatus.error:
        return Icons.error;
    }
  }

  @override
  Widget build(BuildContext context) => DynamicLabelTextField(
      controller: widget.controller,
      hintText: widget.hintText,
      dynamicLabelText: _getDynamicLabelText(),
      labelColor: _getSecurityColor(),
      obscureText: _obscureText,
      validator: widget.validator,
      fontSize: widget.fontSize,
      suffixIcon: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Security indicator
          if (widget.showSecurityIndicator && _securityStatus != PasswordSecurityStatus.initial) ...[
            if (_isCheckingBreach)
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Colors.white.withValues(alpha: 0.7),
                  ),
                ),
              )
            else
              Icon(
                _getSecurityIcon(),
                color: _getSecurityColor(),
                size: 20,
              ),
            const SizedBox(width: 8),
          ],
          // Visibility toggle
          IconButton(
            icon: Icon(
              _obscureText ? Icons.visibility_off : Icons.visibility,
              color: Colors.white.withValues(alpha: 0.7),
            ),
            onPressed: () {
              setState(() {
                _obscureText = !_obscureText;
              });
            },
            padding: EdgeInsets.zero,
          ),
        ],
      ),
    );
}

/// A custom text field that supports dynamic label text and color
class DynamicLabelTextField extends StatefulWidget {

  const DynamicLabelTextField({
    required this.controller, required this.hintText, required this.dynamicLabelText, super.key,
    this.labelColor,
    this.obscureText = false,
    this.keyboardType,
    this.validator,
    this.readOnly = false,
    this.onTap,
    this.suffixIcon,
    this.prefixIcon,
    this.fontSize = 16,
  });
  final TextEditingController controller;
  final String hintText;
  final String dynamicLabelText;
  final Color? labelColor;
  final bool obscureText;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;
  final bool readOnly;
  final VoidCallback? onTap;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final double? fontSize;

  @override
  State<DynamicLabelTextField> createState() => _DynamicLabelTextFieldState();
}

class _DynamicLabelTextFieldState extends State<DynamicLabelTextField> {
  bool _isFocused = false;
  bool _hasError = false;
  bool _hasText = false;
  bool _userHasInteracted = false;

  @override
  void initState() {
    super.initState();
    _hasText = widget.controller.text.isNotEmpty;
  }

  void _checkAndUpdateErrorState(String value) {
    if (widget.validator != null && _userHasInteracted) {
      final error = widget.validator!(value);
      final hasError = error != null;
      
      if (_hasError != hasError) {
        setState(() {
          _hasError = hasError;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final fieldHeight = screenHeight / 16;

    // CRITICAL VERTICAL FIX: Always render border with consistent thickness
    Color borderColor;
    if (_hasError && _userHasInteracted) {
      borderColor = Colors.red;
    } else if (_isFocused) {
      borderColor = const Color(0xFF00FFFF);
    } else {
      borderColor = Colors.transparent; // Same thickness, prevents vertical shifts
    }

    return Container(
      width: double.infinity,
      height: fieldHeight,
      decoration: ShapeDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        shape: RoundedSuperellipseBorder(
          borderRadius: BorderRadius.circular(18),
          side: BorderSide(
            color: borderColor,
            width: 1.0,
          ),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: Row(
          children: [
            if (widget.prefixIcon != null) ...[
              Padding(
                padding: const EdgeInsets.only(left: 12),
                child: widget.prefixIcon,
              ),
            ],
            Expanded(
              child: Focus(
                onFocusChange: (hasFocus) {
                  setState(() => _isFocused = hasFocus);
                  
                  if (hasFocus && !_userHasInteracted) {
                    _userHasInteracted = true;
                  }
                  
                  if (!hasFocus && _userHasInteracted) {
                    _checkAndUpdateErrorState(widget.controller.text);
                  }
                },
                child: TextFormField(
                  controller: widget.controller,
                  obscureText: widget.obscureText,
                  keyboardType: widget.keyboardType,
                  readOnly: widget.readOnly,
                  onTap: widget.onTap,
                  validator: widget.validator,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: widget.fontSize,
                    height: 0,
                    fontFamily: 'Omnes',
                  ),
                  cursorColor: Colors.white,
                  cursorHeight: widget.fontSize,
                  decoration: InputDecoration(
                    labelText: widget.dynamicLabelText,
                    labelStyle: TextStyle(
                      color: Colors.white.withValues(alpha: 0.7),
                      fontFamily: 'Omnes',
                    ),
                    floatingLabelStyle: TextStyle(
                      color: widget.labelColor ?? const Color(0xFF00FFFF),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      fontFamily: 'Omnes',
                    ),
                    // CRITICAL VERTICAL FIX: Lock label position to prevent vertical shifts
                    floatingLabelBehavior: (_userHasInteracted || _hasText)
                        ? FloatingLabelBehavior.auto // Auto behavior: goes inline when unfocused and no text
                        : FloatingLabelBehavior.never, // Stay inline until interaction
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    // CRITICAL VERTICAL FIX: Responsive padding to prevent vertical text shifts
                    contentPadding: EdgeInsets.only(
                      left: widget.prefixIcon == null ? 16.0 : 8.0,
                      right: 16,
                      bottom: fieldHeight * 0.38, // RESPONSIVE: 38% of field height for perfect cursor positioning on different devices
                      top: fieldHeight * 0.1,     // RESPONSIVE: 10% of field height for consistent top spacing
                    ),
                    // CRITICAL VERTICAL FIX: Completely disable error text to prevent layout shifts
                    errorStyle: const TextStyle(height: 0.01, color: Colors.transparent),
                    errorMaxLines: 1,
                  ),
                  onChanged: (value) {
                    final hasText = value.isNotEmpty;
                    var needsUpdate = false;

                    if (_hasText != hasText) {
                      _hasText = hasText;
                      needsUpdate = true;
                    }

                    if (!_userHasInteracted) {
                      _userHasInteracted = true;
                    }

                    if (_isFocused && _userHasInteracted) {
                      _checkAndUpdateErrorState(value);
                    }

                    if (needsUpdate) {
                      setState(() {});
                    }
                  },
                ),
              ),
            ),
            if (widget.suffixIcon != null) ...[
              Padding(
                padding: const EdgeInsets.only(left: 8, right: 12),
                child: widget.suffixIcon,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

// PasswordSecurityStatus enum is now defined in password_security_service.dart 