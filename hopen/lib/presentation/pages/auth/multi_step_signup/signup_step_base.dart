import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../widgets/custom_text_field.dart';
import '../../../widgets/custom_toast.dart';
import '../../../widgets/keyboard_dismissible.dart';

abstract class SignupStepBase extends StatefulWidget {

  const SignupStepBase({
    required this.title, required this.currentStep, required this.totalSteps, required this.onNext, super.key,
    this.onBack,
  });
  final String title;
  final int currentStep;
  final int totalSteps;
  final VoidCallback onNext;
  final VoidCallback? onBack;
}

abstract class SignupStepBaseState<T extends SignupStepBase> extends State<T> {
  final _formKey = GlobalKey<FormState>();

  bool isFormValid() => _formKey.currentState?.validate() ?? false;

  void handleNext() {
    if (isFormValid()) {
      widget.onNext();
    } else {
      // Show custom toast when validation fails
      CustomToast.showError(
        context,
        'Please enter all your infos to continue',
      );
    }
  }

  // Method to be overridden by child classes to provide step-specific content
  List<Widget> buildStepContent(BuildContext context);

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    final fieldHeight = screenHeight / 16;
    final spacingHeight = screenHeight / 48; // EXACTLY 1/48
    final titleFontSize = screenHeight * 0.035;
    final logoHeight = 64 * (screenWidth / 375); // Match MainAppBar logo size

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: const Color(0xFF0A2955),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
        automaticallyImplyLeading: false,
        leadingWidth: 72,
        leading: Padding(
          padding: const EdgeInsets.only(left: 24),
          child:
              widget.onBack != null
                  ? IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: widget.onBack,
                  )
                  : IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => context.go('/login'),
                  ),
        ),
        title: Image.asset(
          'assets/images/hopen-logotype.png',
          height: logoHeight,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            print('Error loading logo: $error');
            return const SizedBox(height: 40); // Placeholder on error
          },
        ),
        centerTitle: true, // Ensure it's centered
        actions: const [SizedBox(width: 72)],
      ),
      body: SafeArea(
        top: false,
        child: KeyboardDismissible(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          SizedBox(height: spacingHeight), // Adjusted top spacing
                          Center(
                            child: Text(
                              widget.title,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: titleFontSize,
                                fontWeight: FontWeight.w600,
                                height: 1.2,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          SizedBox(height: spacingHeight),
                          LinearProgressIndicator(
                            value: widget.currentStep / widget.totalSteps,
                            backgroundColor: Colors.white.withValues(alpha: 0.2),
                            valueColor: const AlwaysStoppedAnimation<Color>(
                              Colors.blue,
                            ),
                            minHeight: 6,
                            borderRadius: BorderRadius.circular(3),
                          ),
                          SizedBox(height: spacingHeight * 2),

                          ...buildStepContent(context),
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).padding.bottom + 10,
                      top: 10,
                    ),
                    child: SizedBox(
                      width: double.infinity,
                      height: fieldHeight,
                      child: ElevatedButton(
                        onPressed: handleNext,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          shape: RoundedSuperellipseBorder(
                            borderRadius: BorderRadius.circular(18),
                          ),
                          padding: EdgeInsets.zero,
                          minimumSize: const Size(0, 0),
                          maximumSize: const Size(
                            double.infinity,
                            double.infinity,
                          ),
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: Text(
                          widget.currentStep == widget.totalSteps
                              ? 'Create your account'
                              : 'Next',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build a text field using the CustomTextField
  Widget buildTextField({
    required TextEditingController controller,
    required String hintText,
    bool obscureText = false,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    bool readOnly = false,
    VoidCallback? onTap,
  }) => CustomTextField(
      controller: controller,
      hintText: hintText,
      obscureText: obscureText,
      keyboardType: keyboardType,
      validator: validator,
      readOnly: readOnly,
      onTap: onTap,
    );
}
