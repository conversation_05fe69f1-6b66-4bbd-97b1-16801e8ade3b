import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_event.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_state.dart';
import '../../../statefulbusinesslogic/bloc/home_navigation/home_navigation_bloc.dart';
import '../../router/app_router.dart';
import '../../widgets/animated_gradient_background.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_toast.dart';
import '../../widgets/keyboard_dismissible.dart';
import '../../widgets/secure_password_field.dart';

//TODO: This page needs a corresponding route in app_router.dart
//TODO: The onNext logic should verify the code and navigate to a new password page

class ResetPasswordVerificationPage extends StatefulWidget {
  const ResetPasswordVerificationPage({super.key});

  @override
  State<ResetPasswordVerificationPage> createState() =>
      _ResetPasswordVerificationPageState();
}

class _ResetPasswordVerificationPageState
    extends State<ResetPasswordVerificationPage> {
  final TextEditingController _verificationCodeController =
      TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _verificationCodeController.dispose();
    super.dispose();
  }

  void _onNextPressed() async {
    if (_formKey.currentState?.validate() ?? false) {
      try {
        // Show loading indicator
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: CircularProgressIndicator(),
          ),
        );

        final verificationCode = _verificationCodeController.text;

        // Simulate code verification (in real implementation, you'd verify with backend)
        await Future.delayed(const Duration(seconds: 2));

        // For demo purposes, accept any 6-digit code
        if (verificationCode.length == 6) {
          if (mounted) {
            Navigator.pop(context); // Hide loading

            CustomToast.showSuccess(
              context,
              'Verification code confirmed!',
            );

            // Navigate to create new password page with the verification token
            context.go('${AppRoutes.createNewPassword}?token=$verificationCode');
          }
        } else {
          throw Exception('Invalid verification code');
        }
      } catch (e) {
        if (mounted) {
          Navigator.pop(context); // Hide loading

          CustomToast.showError(
            context,
            'Invalid verification code. Please try again.',
          );
        }
      }
    }
  }

  void _onBackPressed() {
    // Navigate back to the forgot password page
    context.go(AppRoutes.forgotPassword);
  }

  void _resendCode() {
    CustomToast.showInfo(context, 'Resend code (Not implemented yet)');
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final fieldHeight = screenHeight / 16;
    final spacingHeight = screenHeight / 48;
    final titleFontSize = screenHeight * 0.035;
    final logoHeight = 64 * (screenWidth / 375); // Match MainAppBar logo size

    const pageTitle = 'Enter reset code';

    final stepContent = <Widget>[
      Text(
        "We've sent a password reset code to your email address. Please enter it below.",
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.7),
          fontSize: 16,
          height: 1.5,
        ),
        textAlign: TextAlign.center,
      ),
      SizedBox(height: spacingHeight * 2),
      CustomTextField(
        controller: _verificationCodeController,
        hintText: 'Enter reset code',
        keyboardType: TextInputType.number,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please enter the reset code';
          }
          if (value.length != 6) {
            return 'Reset code must be 6 digits';
          }
          if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
            return 'Reset code must contain only numbers';
          }
          return null;
        },
      ),
      SizedBox(height: spacingHeight),
      TextButton(
        onPressed: _resendCode,
        style: TextButton.styleFrom(
          padding: EdgeInsets.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          alignment: Alignment.center,
        ),
        child: Text(
          'Resend reset code',
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 16,
          ),
        ),
      ),
    ];

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: const Color(0xFF0A2955),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
        automaticallyImplyLeading: false,
        leadingWidth: 72,
        leading: Padding(
          padding: const EdgeInsets.only(left: 24),
          child: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: _onBackPressed,
          ),
        ),
        title: Image.asset(
          'assets/images/hopen-logotype.png',
          height: logoHeight,
          fit: BoxFit.contain,
        ),
        centerTitle: true,
        actions: const [SizedBox(width: 72)],
      ),
      body: SafeArea(
        top: false,
        child: KeyboardDismissible(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          SizedBox(height: spacingHeight),
                          Center(
                            child: Text(
                              pageTitle,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: titleFontSize,
                                fontWeight: FontWeight.w600,
                                height: 1.2,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          SizedBox(height: spacingHeight * 2),
                          // No progress bar like in SignupStepBase, as this is a standalone page.
                          // If a progress indication is needed, it would be different.
                          ...stepContent,
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).padding.bottom + 10,
                      top: 10,
                    ),
                    child: SizedBox(
                      width: double.infinity,
                      height: fieldHeight,
                      child: ElevatedButton(
                        onPressed: _onNextPressed,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          shape: RoundedSuperellipseBorder(
                            borderRadius: BorderRadius.circular(18),
                          ),
                          padding: EdgeInsets.zero,
                          minimumSize: const Size(0, 0),
                          maximumSize: const Size(
                            double.infinity,
                            double.infinity,
                          ),
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: const Text(
                          'Reset your password',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
