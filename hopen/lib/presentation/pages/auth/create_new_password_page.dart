import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_event.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_state.dart';
import '../../../statefulbusinesslogic/bloc/home_navigation/home_navigation_bloc.dart';
import '../../router/app_router.dart';
import '../../widgets/animated_gradient_background.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_toast.dart';
import '../../widgets/keyboard_dismissible.dart';
import '../../widgets/secure_password_field.dart';

//TODO: This page needs a corresponding route in app_router.dart
//TODO: The onNext logic should validate passwords and attempt to update the password.

class CreateNewPasswordPage extends StatefulWidget {
  const CreateNewPasswordPage({super.key});

  @override
  State<CreateNewPasswordPage> createState() => _CreateNewPasswordPageState();
}

class _CreateNewPasswordPageState extends State<CreateNewPasswordPage> {
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _updatePassword() async {
    if (_formKey.currentState!.validate()) {
      try {
        // Show loading indicator
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const Center(
            child: CircularProgressIndicator(),
          ),
        );

        // Get the reset token from route parameters
        final resetToken = GoRouterState.of(context).pathParameters['token'] ?? '';

        if (resetToken.isEmpty) {
          throw Exception('Invalid reset token');
        }

        // Simulate password update since update event not wired yet
        await Future.delayed(const Duration(seconds: 2));

        if (mounted) {
          Navigator.pop(context); // Hide loading

          CustomToast.showSuccess(
            context,
            'Password updated successfully!',
          );

          // Navigate to login page
          context.go(AppRoutes.login);
        }
      } catch (e) {
        if (mounted) {
          Navigator.pop(context); // Hide loading

          CustomToast.showError(
            context,
            'Failed to update password: $e',
          );
        }
      }
    }
  }

  void _onBackPressed() {
    // Navigate back to the reset password verification page
    context.go(AppRoutes.resetPasswordVerification);
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final fieldHeight = screenHeight / 16;
    final spacingHeight = screenHeight / 48;
    final titleFontSize = screenHeight * 0.035;
    final logoHeight = 64 * (screenWidth / 375); // Match MainAppBar logo size

    const pageTitle = 'Create your new password';

    final stepContent = <Widget>[
      Text(
        'Please enter and confirm your new password below.',
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.7),
          fontSize: 16,
          height: 1.5,
        ),
        textAlign: TextAlign.center,
      ),
      SizedBox(height: spacingHeight * 2),
      CustomPasswordField(
        controller: _newPasswordController,
        hintText: 'New password',
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please enter a new password';
          }
          if (value.length < 8) {
            // Example validation: min length
            return 'Password must be at least 8 characters';
          }
          // TODO: Add more password strength validation if needed
          return null;
        },
      ),
      SizedBox(height: spacingHeight),
      CustomPasswordField(
        controller: _confirmPasswordController,
        hintText: 'Confirm new password',
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'Please confirm your new password';
          }
          if (value != _newPasswordController.text) {
            return 'Passwords do not match';
          }
          return null;
        },
      ),
    ];

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: const Color(0xFF0A2955),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
        automaticallyImplyLeading: false,
        leadingWidth: 72,
        leading: Padding(
          padding: const EdgeInsets.only(left: 24),
          child: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: _onBackPressed,
          ),
        ),
        title: Image.asset(
          'assets/images/hopen-logotype.png',
          height: logoHeight,
          fit: BoxFit.contain,
        ),
        centerTitle: true,
        actions: const [SizedBox(width: 72)],
      ),
      body: SafeArea(
        top: false,
        child: KeyboardDismissible(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Form(
              key: _formKey,
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          SizedBox(height: spacingHeight),
                          Center(
                            child: Text(
                              pageTitle,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: titleFontSize,
                                fontWeight: FontWeight.w600,
                                height: 1.2,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          SizedBox(
                            height: spacingHeight * 2,
                          ), // Consistent spacing
                          ...stepContent,
                        ],
                      ),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).padding.bottom + 10,
                      top: 10,
                    ),
                    child: SizedBox(
                      width: double.infinity,
                      height: fieldHeight,
                      child: ElevatedButton(
                        onPressed: _updatePassword,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          shape: RoundedSuperellipseBorder(
                            borderRadius: BorderRadius.circular(18),
                          ),
                          padding: EdgeInsets.zero,
                          minimumSize: const Size(0, 0),
                          maximumSize: const Size(
                            double.infinity,
                            double.infinity,
                          ),
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: const Text(
                          'Create your new password',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
