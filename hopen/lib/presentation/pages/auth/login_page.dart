import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import '../../../statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_event.dart';
import '../../router/app_router.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_toast.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_state.dart';
import '../../widgets/animated_gradient_background.dart';
import '../../../statefulbusinesslogic/bloc/home_navigation/home_navigation_bloc.dart';
import '../../widgets/keyboard_dismissible.dart';
import '../../widgets/secure_password_field.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: AnimatedGradientBackground(
        child: MultiBlocListener(
          listeners: [
            BlocListener<AuthBloc, AuthState>(
              listenWhen: (previous, current) => previous.status != current.status,
              listener: (context, state) {
                if (state.status == AuthStatus.authenticated) {
                  Navigator.of(context, rootNavigator: true).maybePop();
                  final welcomeName = state.firstName ?? state.email?.split('@').first ?? 'User';
                  CustomToast.showSuccess(context, 'Welcome back, $welcomeName!');
                  context.read<HomeNavigationBloc>().add(const DetermineHomeNavigation());
                } else if (state.status == AuthStatus.error) {
                  Navigator.of(context, rootNavigator: true).maybePop();
                  CustomToast.showError(context, state.errorMessage ?? 'Authentication failed');
                }
              },
            ),
            BlocListener<HomeNavigationBloc, HomeNavigationState>(
              listenWhen: (p, c) => p.status != c.status && c.status == HomeNavigationStatus.ready,
              listener: (context, navState) {
                if (navState.destination != null) {
                  final route = switch (navState.destination!) {
                    HomeDestination.bubble => AppRoutes.bubble,
                    HomeDestination.friends => AppRoutes.friends,
                    HomeDestination.contacts => AppRoutes.contacts,
                  };
                  context.go(route);
                }
              },
            ),
          ],
          child: SafeArea(
            child: KeyboardDismissible(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Form(
                  key: _formKey,
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(height: MediaQuery.of(context).size.height * 0.00),
                        // Hopen logotype with improved responsiveness
                        Container(
                          width: MediaQuery.of(context).size.width * 0.4,
                          constraints: const BoxConstraints(
                            maxWidth: 300,
                            minWidth: 200,
                          ),
                          child: Image.asset(
                            'assets/images/hopen-logotype.png',
                            fit: BoxFit.contain,
                            errorBuilder: (context, error, stackTrace) {
                              debugPrint(
                                'Error loading logo: $error\nStackTrace: $stackTrace',
                              );
                              return Container(
                                height: 60,
                                color: Colors.red.withValues(alpha: 0.1),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.error_outline,
                                      color: Colors.white,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Error loading logo: ${error.toString()}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                        // Tagline
                        Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: 'take the time to make\n',
                                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.normal,
                                  fontSize: MediaQuery.of(context).size.height * 0.028,
                                  fontFamily: 'Omnes',
                                ),
                              ),
                              TextSpan(
                                text: 'true friends',
                                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: MediaQuery.of(context).size.height * 0.028,
                                  fontFamily: 'Omnes',
                                ),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height / 48),
                        // Username or email input
                        CustomTextField(
                          controller: _emailController,
                          hintText: 'Email or username',
                          keyboardType: TextInputType.text,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your email or username';
                            }

                            // Check if it's an email format
                            final emailRegex = RegExp(
                              r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                            );

                            // Check if it's a valid username format (3-50 characters, alphanumeric, underscore, hyphen)
                            final usernameRegex = RegExp(
                              r'^[a-zA-Z0-9_-]{3,50}$',
                            );

                            // Accept if it's either a valid email or valid username
                            if (!emailRegex.hasMatch(value) && !usernameRegex.hasMatch(value)) {
                              return 'Please enter a valid email or username';
                            }

                            return null;
                          },
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height / 48),
                        // Password input
                        CustomPasswordField(
                          controller: _passwordController,
                          hintText: 'Password',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter your password';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height / 48),
                        // Login button
                        SizedBox(
                          width: double.infinity,
                          height: MediaQuery.of(context).size.height / 16,
                          child: ElevatedButton(
                            onPressed: () {
                              if (_formKey.currentState!.validate()) {
                                context.read<AuthBloc>().add(
                                  LoginEvent(
                                    email: _emailController.text,
                                    password: _passwordController.text,
                                  ),
                                );
                              } else {
                                // Show custom toast when validation fails
                                CustomToast.showError(
                                  context,
                                  'Enter your email or username and password to login',
                                );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: Colors.black,
                              shape: RoundedSuperellipseBorder(
                                borderRadius: BorderRadius.circular(18),
                              ),
                              padding: EdgeInsets.zero,
                              minimumSize: const Size(0, 0),
                              maximumSize: const Size(
                                double.infinity,
                                double.infinity,
                              ),
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: const Text(
                              'Login',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Omnes',
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height / 48),
                        // Continue with Google
                        SizedBox(
                          width: double.infinity,
                          height: MediaQuery.of(context).size.height / 16,
                          child: ElevatedButton.icon(
                            onPressed: () {
                              context.read<AuthBloc>().add(
                                const LoginWithGoogleEvent(),
                              );
                              // Navigation will be handled by BlocListener on success
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: Colors.black,
                              shape: RoundedSuperellipseBorder(
                                borderRadius: BorderRadius.circular(18),
                              ),
                              padding: EdgeInsets.zero,
                              minimumSize: const Size(0, 0),
                              maximumSize: const Size(
                                double.infinity,
                                double.infinity,
                              ),
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            icon: SvgPicture.asset(
                              'assets/icons/google.svg',
                              width: 24,
                              height: 24,
                            ),
                            label: const Text(
                              'Continue with Google',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Omnes',
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height / 48),
                        // Continue with Apple
                        SizedBox(
                          width: double.infinity,
                          height: MediaQuery.of(context).size.height / 16,
                          child: ElevatedButton.icon(
                            onPressed: () {
                              context.read<AuthBloc>().add(
                                const LoginWithAppleEvent(),
                              );
                              // Navigation will be handled by BlocListener on success
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.black,
                              foregroundColor: Colors.white,
                              shape: RoundedSuperellipseBorder(
                                borderRadius: BorderRadius.circular(18),
                              ),
                              padding: EdgeInsets.zero,
                              minimumSize: const Size(0, 0),
                              maximumSize: const Size(
                                double.infinity,
                                double.infinity,
                              ),
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            icon: SvgPicture.asset(
                              'assets/icons/apple.svg',
                              width: 24,
                              height: 24,
                              colorFilter: const ColorFilter.mode(
                                Colors.white,
                                BlendMode.srcIn,
                              ),
                            ),
                            label: const Text(
                              'Continue with Apple',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                fontFamily: 'Omnes',
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height / 48),
                        // Forgot password link
                        RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            text: 'Forgot your password?',
                            style: const TextStyle(
                              color: Color(0xFF00FFFF),
                              fontFamily: 'Omnes',
                            ),
                            recognizer:
                                TapGestureRecognizer()
                                  ..onTap = () {
                                    context.go(AppRoutes.forgotPassword);
                                  },
                          ),
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height / 48),
                        // Sign up link
                        RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontFamily: 'Omnes',
                            ),
                            children: [
                              const TextSpan(text: "Don't have an account yet? "),
                              TextSpan(
                                text: 'Sign up',
                                style: const TextStyle(
                                  color: Color(0xFF00FFFF),
                                  fontFamily: 'Omnes',
                                ),
                                recognizer:
                                    TapGestureRecognizer()
                                      ..onTap = () {
                                        // Navigate to the multi-step signup process
                                        context.go(AppRoutes.multiStepSignup);
                                      },
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: MediaQuery.of(context).size.height / 48),
                        // Terms and policy
                        RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 12,
                              fontFamily: 'Omnes',
                            ),
                            children: const [
                              TextSpan(text: 'By signing up, you agree to our '),
                              TextSpan(
                                text: 'terms of service',
                                style: TextStyle(fontFamily: 'Omnes'),
                              ),
                              TextSpan(
                                text: ', ',
                                style: TextStyle(fontFamily: 'Omnes'),
                              ),
                              TextSpan(
                                text: 'confidentiality policy',
                                style: TextStyle(fontFamily: 'Omnes'),
                              ),
                              TextSpan(
                                text: ', ',
                                style: TextStyle(fontFamily: 'Omnes'),
                              ),
                              TextSpan(
                                text: 'security rules',
                                style: TextStyle(fontFamily: 'Omnes'),
                              ),
                              TextSpan(
                                text: ' and ',
                                style: TextStyle(fontFamily: 'Omnes'),
                              ),
                              TextSpan(
                                text: 'privacy policy',
                                style: TextStyle(fontFamily: 'Omnes'),
                              ),
                              TextSpan(
                                text: '.',
                                style: TextStyle(fontFamily: 'Omnes'),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
