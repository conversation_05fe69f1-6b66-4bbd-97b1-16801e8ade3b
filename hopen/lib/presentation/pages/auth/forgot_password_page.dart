import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../router/app_router.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_toast.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_event.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_state.dart';
import '../../widgets/keyboard_dismissible.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final _emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>(); // Defined locally

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  // Manually replicated buildTextField from SignupStepBaseState
  Widget _buildStyledTextField({
    required TextEditingController controller,
    required String hintText,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    // Using fixed height similar to CustomTextField's typical usage in SignupStepBase
    return CustomTextField(
      controller: controller,
      hintText: hintText,
      keyboardType: keyboardType,
      validator: validator ?? (value) {
          if (value == null || value.isEmpty) {
            return "Please enter your email";
          }
          return null;
        },
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    // Dimensions from SignupStepBase for styling consistency
    final fieldHeight = screenHeight / 16;
    final spacingHeight = screenHeight / 48; // EXACTLY 1/48
    final titleFontSize = screenHeight * 0.035;
    final logoHeight = 64 * (screenWidth / 375); // Match MainAppBar logo size

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state.status == AuthStatus.passwordResetSent) {
          Navigator.of(context).pop();
          CustomToast.showSuccess(context, 'Password reset email sent successfully');
        } else if (state.status == AuthStatus.error) {
          CustomToast.showError(context, state.errorMessage ?? 'Failed to send reset email');
        }
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: const Color(0xFF0A2955), // From SignupStepBase
        appBar: AppBar(
          // Styled like SignupStepBase AppBar
          backgroundColor: Colors.transparent,
          elevation: 0,
          scrolledUnderElevation: 0,
          surfaceTintColor: Colors.transparent,
          automaticallyImplyLeading: false, // Added from SignupStepBase
          leadingWidth: 72, // Added from SignupStepBase
          leading: Padding(
            // Added from SignupStepBase
            padding: const EdgeInsets.only(
              left: 24,
            ), // Added from SignupStepBase
            child: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => context.go(AppRoutes.login),
            ),
          ),
          title: Image.asset(
            'assets/images/hopen-logotype.png',
            height: logoHeight,
            fit: BoxFit.contain,
          ),
          centerTitle: true,
          actions: const [
            SizedBox(width: 72),
          ], // Updated from 56 to 72 to match SignupStepBase
        ),
        body: SafeArea(
          top: false, // Consistent with SignupStepBase
          child: KeyboardDismissible(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24), // Consistent
              child: Form(
                key: _formKey, // Using locally defined _formKey
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            SizedBox(
                              height: spacingHeight,
                            ), // Consistent top spacing
                            Center(
                              child: Text(
                                'Forgot your password?', // Page specific title
                                style: TextStyle(
                                  // Style from SignupStepBase title
                                  color: Colors.white,
                                  fontSize: titleFontSize,
                                  fontWeight: FontWeight.w600,
                                  height: 1.2,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                            // No progress bar for this page
                            SizedBox(
                              height: spacingHeight * 2,
                            ), // Spacing after title (was after progress bar)
                            // Content replicated from Step4EmailVerificationPage's buildStepContent
                            Text(
                              "Enter your email address below, and we'll send you instructions to reset your password.",
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.7),
                                fontSize: 16,
                                height: 1.5,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            SizedBox(height: spacingHeight * 2),
                            _buildStyledTextField(
                              controller: _emailController,
                              hintText: 'Enter your email',
                              keyboardType: TextInputType.emailAddress,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter your email';
                                }
                                if (!RegExp(
                                  r'^[^@]+@[^@]+\.[^@]+$',
                                ).hasMatch(value)) {
                                  return 'Please enter a valid email address';
                                }
                                return null;
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                    // Primary button styled like SignupStepBase's "Next" button
                    BlocBuilder<AuthBloc, AuthState>(
                      builder: (context, state) {
                        final isLoading = state.status == AuthStatus.loading;
                        
                        return Padding(
                          padding: EdgeInsets.only(
                            bottom: MediaQuery.of(context).padding.bottom + 10,
                            top: 10,
                          ),
                          child: SizedBox(
                            width: double.infinity,
                            height: fieldHeight,
                            child: ElevatedButton(
                              onPressed: isLoading ? null : () {
                                if (_formKey.currentState!.validate()) {
                                  context.read<AuthBloc>().add(
                                    SendPasswordResetEmailEvent(email: _emailController.text),
                                  );
                                }
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue,
                                foregroundColor: Colors.white,
                                shape: RoundedSuperellipseBorder(
                                  borderRadius: BorderRadius.circular(18),
                                ),
                                elevation: 0,
                                padding: EdgeInsets.zero,
                                minimumSize: const Size(0, 0),
                                maximumSize: const Size(double.infinity, double.infinity),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                              ),
                              child: isLoading
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white,
                                        ),
                                      ),
                                    )
                                  : const Text(
                                      'Send reset link',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
