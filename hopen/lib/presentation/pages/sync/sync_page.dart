/// Sync Page - HTTP/3-First Hybrid Sync Architecture Demo
/// 
/// This page demonstrates the HTTP/3-first sync architecture with:
/// - Real-time sync status updates
/// - Performance metrics display
/// - Protocol detection (HTTP/3 vs HTTP/2)
/// - MQTT connection status
/// - Retry functionality

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../statefulbusinesslogic/bloc/sync/sync_bloc.dart';
import '../../../statefulbusinesslogic/core/models/sync_models.dart';
import '../../widgets/common/loading_indicator.dart';

class SyncPage extends StatelessWidget {
  const SyncPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('HTTP/3-First Sync'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: BlocProvider(
        create: (context) => SyncBloc(syncRepository: context.read()),
        child: const _SyncPageContent(),
      ),
    );
  }
}

class _SyncPageContent extends StatelessWidget {
  const _SyncPageContent();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'HTTP/3-First Hybrid Sync',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Demonstrates high-performance data synchronization using HTTP/3 with automatic fallback to HTTP/2, plus real-time MQTT updates.',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Sync Status
          Expanded(
            child: BlocBuilder<SyncBloc, SyncState>(
              builder: (context, state) {
                return Column(
                  children: [
                    // Status Card
                    _buildStatusCard(context, state),
                    const SizedBox(height: 16),

                    // Action Buttons
                    _buildActionButtons(context, state),
                    const SizedBox(height: 16),

                    // Results/Metrics
                    if (state is SyncSuccess) ...[
                      Expanded(child: _buildSuccessResults(context, state)),
                    ] else if (state is SyncFailure) ...[
                      Expanded(child: _buildErrorResults(context, state)),
                    ] else if (state is SyncInProgress) ...[
                      Expanded(child: _buildProgressResults(context, state)),
                    ] else ...[
                      const Expanded(
                        child: Center(
                          child: Text('Ready to sync. Tap "Start Sync" to begin.'),
                        ),
                      ),
                    ],
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusCard(BuildContext context, SyncState state) {
    final theme = Theme.of(context);
    
    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (state) {
      case SyncInitial():
        statusColor = theme.colorScheme.outline;
        statusIcon = Icons.sync_disabled;
        statusText = 'Not Started';
      case SyncInProgress():
        statusColor = theme.colorScheme.primary;
        statusIcon = Icons.sync;
        statusText = state.status.displayName;
      case SyncSuccess():
        statusColor = theme.colorScheme.primary;
        statusIcon = Icons.check_circle;
        statusText = 'Completed Successfully';
      case SyncFailure():
        statusColor = theme.colorScheme.error;
        statusIcon = Icons.error;
        statusText = 'Failed';
      case SyncCancelled():
        statusColor = theme.colorScheme.outline;
        statusIcon = Icons.cancel;
        statusText = 'Cancelled';
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(statusIcon, color: statusColor, size: 32),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Sync Status',
                    style: theme.textTheme.titleMedium,
                  ),
                  Text(
                    statusText,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (state is SyncInProgress) ...[
                    const SizedBox(height: 8),
                    Text(
                      state.message,
                      style: theme.textTheme.bodySmall,
                    ),
                    const SizedBox(height: 8),
                    const LinearProgressIndicator(),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, SyncState state) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: state is SyncInProgress
                ? null
                : () => context.read<SyncBloc>().add(const StartSyncEvent()),
            icon: const Icon(Icons.sync),
            label: const Text('Start Sync'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: state is SyncInProgress
                ? () => context.read<SyncBloc>().add(const CancelSyncEvent())
                : null,
            icon: const Icon(Icons.stop),
            label: const Text('Cancel'),
          ),
        ),
      ],
    );
  }

  Widget _buildSuccessResults(BuildContext context, SyncSuccess state) {
    final result = state.result;
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Sync Completed Successfully',
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildMetricRow('Duration', '${result.syncDurationMs}ms ${result.performance.emoji}'),
            _buildMetricRow('Protocol', result.protocolUsed),
            _buildMetricRow('MQTT Connected', result.mqttConnected ? 'Yes ✅' : 'No ❌'),
            _buildMetricRow('Data Types', '${result.dataTypes.length} types'),
            _buildMetricRow('Performance', result.performance.displayName),
            const SizedBox(height: 16),
            Text(
              'Data Types Synchronized:',
              style: theme.textTheme.titleSmall,
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: result.dataTypes.map((type) => Chip(
                label: Text(type),
                backgroundColor: theme.colorScheme.primaryContainer,
              )).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorResults(BuildContext context, SyncFailure state) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.error, color: theme.colorScheme.error),
                const SizedBox(width: 8),
                Text(
                  'Sync Failed',
                  style: theme.textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Error Details:',
              style: theme.textTheme.titleSmall,
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                state.error,
                style: theme.textTheme.bodyMedium,
              ),
            ),
            if (state.canRetry) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: () => context.read<SyncBloc>().add(const RetrySync()),
                icon: const Icon(Icons.refresh),
                label: const Text('Retry Sync'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProgressResults(BuildContext context, SyncInProgress state) {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LoadingIndicator(),
            SizedBox(height: 16),
            Text('Synchronizing data...'),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }
}
