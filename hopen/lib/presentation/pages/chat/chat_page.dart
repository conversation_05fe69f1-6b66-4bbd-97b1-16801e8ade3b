import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:audioplayers/audioplayers.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:record/record.dart';

import '../../../di/injection_container_refactored.dart' as di;
import '../../../statefulbusinesslogic/bloc/chat/chat_bloc.dart';
import '../../../statefulbusinesslogic/bloc/chat/chat_event.dart';
import '../../../statefulbusinesslogic/bloc/chat/chat_state.dart';
import '../../../statefulbusinesslogic/bloc/voice_recording/voice_recording_bloc.dart';
import '../../../statefulbusinesslogic/bloc/voice_recording/voice_recording_event.dart';
import '../../../statefulbusinesslogic/bloc/voice_recording/voice_recording_state.dart';
import '../../../statefulbusinesslogic/core/models/chat_message.dart';
import '../../../statefulbusinesslogic/core/notifiers/nav_bar_visibility_notifier.dart';
import '../../router/app_router.dart';
import '../../widgets/chat_bubble.dart';
import '../../widgets/custom_toast.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/keyboard_dismissible.dart';
import '../../widgets/online_status_indicator.dart';
import '../call/friends_call_page.dart';
import '../camera/camera_awesome_page.dart';

// Enum for the type of call to start
enum CallType { audio, video, screenShare }

class ChatPage extends StatefulWidget { // Added online status parameter

  const ChatPage({
    required this.friendId,
    required this.friendName,
    required this.bubbleId,
    super.key,
    this.friendProfilePhoto,
    this.friendUsername,
    this.isOnline = false, // Added to constructor, default to false
  });
  // We need a simple representation of the friend for the AppBar
  final String friendId;
  final String friendName;
  final String bubbleId;
  final String? friendProfilePhoto; // Can be null
  final String? friendUsername; // Added username field
  final bool isOnline;

  // Placeholder for the current user ID
  static const String currentUserId = 'current_user_123';

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final TextEditingController _textController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<ChatMessage> _messages = [];
  final bool _isLoadingMore = false; // Make final
  bool _isTyping = false; // New: For typing indicator
  Timer? _typingTimer; // New: To simulate typing indicator
  bool _isSearching = false;
  List<ChatMessage> _searchResults = [];
  StreamSubscription<ChatMessage>? _messageSubscription;
  bool _hasText = false;

  // Voice message recording related variables
  String? _recordedFilePath;

  // For reply functionality
  ChatMessage? _replyToMessage;

  late ChatBloc _chatBloc;
  late VoiceRecordingBloc _voiceRecordingBloc;
  final AudioPlayer _audioPlayer = AudioPlayer();

  @override
  void initState() {
    super.initState();
    _chatBloc = context.read<ChatBloc>();
    _voiceRecordingBloc = di.sl<VoiceRecordingBloc>();
    _chatBloc.add(InitializeChatEvent(
      chatId: widget.friendId,
      bubbleId: widget.bubbleId,
    ));
    
    _searchController.addListener(_onSearchChanged);
    _textController.addListener(() {
      setState(() {
        _hasText = _textController.text.trim().isNotEmpty;
      });
    });
    Provider.of<NavBarVisibilityNotifier>(context, listen: false).hideNavBar();
  }

  @override
  Widget build(BuildContext context) {
    // Define colors (customize these)
    // const Color chatBackgroundColor = Color(0xFF0A1128); // No longer needed
    // const Color appBarColor = Color(0xFF005CB2); // Use gradient instead
    const inputBackgroundColor = Color(0xFF1A2B4D);

    return BlocBuilder<ChatBloc, ChatState>(
      builder: (context, state) {
        return BlocBuilder<VoiceRecordingBloc, VoiceRecordingState>(
          builder: (context, recordingState) {
        return GradientBackground(
          child: Scaffold(
            backgroundColor: Colors.transparent,
            appBar:
                _isSearching ? _buildSearchAppBar() : _buildCustomAppBar(context),
            body: KeyboardDismissible(
              child: Column(
                children: [
                  Expanded(
                    child:
                        _isSearching && _searchResults.isNotEmpty
                            ? _buildSearchResultsList()
                            : ListView.builder(
                              controller: _scrollController,
                              reverse: true,
                              padding: const EdgeInsets.symmetric(vertical: 10),
                              itemCount:
                                  _messages.length +
                                  (_isLoadingMore ? 1 : 0) +
                                  (_isTyping ? 1 : 0),
                              itemBuilder: (context, index) {
                                // Show typing indicator at the top of the reversed list when typing
                                if (_isTyping && index == 0) {
                                  return _buildTypingIndicator();
                                }

                                // Adjust index for the message list to account for typing indicator
                                final messageIndex = _isTyping ? index - 1 : index;

                                // If we're at the end and loading more, show a loading indicator
                                if (_isLoadingMore &&
                                    messageIndex == _messages.length) {
                                  return const Center(
                                    child: Padding(
                                      padding: EdgeInsets.all(8),
                                      child: CircularProgressIndicator(),
                                    ),
                                  );
                                }

                                final message = _messages[messageIndex];
                                final isMe =
                                    message.senderId == ChatPage.currentUserId;

                                var showDateSeparator = false;
                                if (messageIndex == _messages.length - 1) {
                                  showDateSeparator = true;
                                } else {
                                  final previousMessage = _messages[messageIndex + 1];
                                  if (!DateUtils.isSameDay(
                                    message.timestamp,
                                    previousMessage.timestamp,
                                  )) {
                                    showDateSeparator = true;
                                  }
                                }

                                return Column(
                                  children: [
                                    if (showDateSeparator)
                                      _buildDateSeparator(message.timestamp),
                                    ChatBubble(
                                      message: message,
                                      isMe: isMe,
                                      // DEACTIVATED: Reactions
                                      //     (emoji) => _addReaction(message.id, emoji),
                                      onMediaTap:
                                          message.mediaType != MediaType.text
                                              ? () => _handleMediaTap(message)
                                              : null,
                                      onReply: _handleReply,
                                      onForward: _handleForward,
                                      friendName: widget.friendName,
                                    ),
                                  ],
                                );
                              },
                            ),
                  ),
                  if (_replyToMessage != null) _buildReplyBar(),
                      if (recordingState.showVoiceRecorder)
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(recordingState.formattedDuration),
                              const SizedBox(width: 8),
                              const Icon(Icons.mic, color: Colors.red),
                            ],
                          ),
                  ),
                  _buildMessageInputArea(inputBackgroundColor),
                ],
              ),
            ),
          ),
            );
          },
        );
      },
    );
  }

  Widget _buildSearchResultsList() => ListView.builder(
      itemCount: _searchResults.length,
      padding: const EdgeInsets.symmetric(vertical: 10),
      itemBuilder: (context, index) {
        final message = _searchResults[index];
        final isMe = message.senderId == ChatPage.currentUserId;

        return ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          leading: CircleAvatar(
            backgroundColor: Colors.white.withValues(alpha: 0.2),
            child: Text(
              isMe ? 'Me' : widget.friendName[0],
              style: const TextStyle(color: Colors.white),
            ),
          ),
          title: Text(
            isMe ? 'Me' : widget.friendName,
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                message.content,
                style: const TextStyle(color: Colors.white70),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                DateFormat('MMM d, yyyy • h:mm a').format(message.timestamp),
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.5),
                  fontSize: 12,
                ),
              ),
            ],
          ),
          tileColor: Colors.white.withValues(alpha: 0.05),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(color: Colors.white.withValues(alpha: 0.1)),
          ),
          onTap: () => _scrollToMessage(message.id),
        );
      },
    );

  PreferredSizeWidget _buildSearchAppBar() => AppBar(
      backgroundColor: const Color.fromARGB(0, 255, 255, 255).withValues(alpha: 0.1),
      elevation: 0,
      scrolledUnderElevation: 0,
      surfaceTintColor: Colors.transparent,
      centerTitle: false,
      titleSpacing: 0,
      title: TextField(
        controller: _searchController,
        style: const TextStyle(color: Colors.white),
        textAlignVertical: TextAlignVertical.center,
        decoration: InputDecoration(
          hintText: 'Search conversation...',
          hintStyle: TextStyle(color: Colors.white.withValues(alpha: 0.5)),
          border: InputBorder.none,
          prefixIcon: Icon(Icons.search, color: Colors.white.withValues(alpha: 0.5)),
          isCollapsed: true,
          contentPadding: const EdgeInsets.symmetric(vertical: 12),
          suffixIcon:
              _searchController.text.isNotEmpty
                  ? IconButton(
                    icon: const Icon(Icons.clear, color: Colors.white70),
                    onPressed: _searchController.clear,
                  )
                  : null,
        ),
        autofocus: true,
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: _toggleSearch,
      ),
    );

  // --- Custom AppBar ---
  PreferredSizeWidget _buildCustomAppBar(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final appBarHeight = (screenHeight * 0.05).clamp(50.0, 60.0);
    final avatarRadius = appBarHeight * 0.3;
    final iconSize = appBarHeight * 0.25;
    final titleFontSize = appBarHeight * 0.18;
    final statusFontSize = appBarHeight * 0.18;
    final horizontalPadding = screenWidth * 0.02;
    final leadingIconSize = iconSize * 1.4;

    return PreferredSize(
      preferredSize: Size.fromHeight(appBarHeight),
      child: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        surfaceTintColor: Colors.transparent,
        automaticallyImplyLeading: false,
        flexibleSpace: DecoratedBox(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Color(0xFF00C4FF), Color(0xFF00A3E0)],
            ),
            borderRadius: BorderRadius.vertical(bottom: Radius.circular(20)),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.only(
                left: horizontalPadding / 12, // Reduce left padding further
                right: horizontalPadding + 8.0, // Increased right padding
              ),
              child: Row(
                children: [
                  // Back arrow
                  IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                    iconSize: leadingIconSize,
                    splashRadius: leadingIconSize * 1.2,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  // Avatar
                  GestureDetector(
                    onTap: _navigateToUserProfile,
                    child: CircleAvatar(
                      radius: avatarRadius,
                      backgroundColor: Colors.white.withValues(alpha: 0.3),
                      backgroundImage:
                          widget.friendProfilePhoto != null
                              ? NetworkImage(widget.friendProfilePhoto!)
                              : null,
                      child:
                          widget.friendProfilePhoto == null
                              ? Text(
                                widget.friendName.isNotEmpty
                                    ? widget.friendName[0].toUpperCase()
                                    : '?',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: avatarRadius * 0.8,
                                ),
                              )
                              : null,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Name and status
                  Expanded(
                    child: GestureDetector(
                      onTap: _navigateToUserProfile,
                      behavior: HitTestBehavior.opaque,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Flexible(
                                child: Text(
                                  widget.friendName,
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: titleFontSize,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(left: 6),
                                child: OnlineStatusIndicator(
                                  isOnline: widget.isOnline,
                                ), // Use the widget
                              ),
                            ],
                          ),
                          if (widget.friendUsername?.isNotEmpty ?? false)
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 2,
                              ), // Add small top padding
                              child: Text(
                                '@${widget.friendUsername!}', // Display username with @
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.8),
                                  fontSize:
                                      statusFontSize *
                                      0.9, // Slightly smaller font
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 4),
                  // Action icons
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildAppBarIcon(
                        'assets/icons/phone.svg',
                        iconSize,
                        callType: CallType.audio,
                      ),
                      const SizedBox(width: 12),
                      _buildAppBarIcon(
                        'assets/icons/video.svg',
                        iconSize,
                        callType: CallType.video,
                      ),
                      const SizedBox(width: 12),
                      _buildAppBarIcon(
                        'assets/icons/smartphone.svg',
                        iconSize,
                        callType: CallType.screenShare,
                      ),
                      const SizedBox(width: 12),
                      // Add search icon with the same style as other icons
                      InkWell(
                        onTap: _toggleSearch,
                        child: Icon(
                          Icons.search,
                          color: Colors.white,
                          size: iconSize,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Updated to accept size and call type
  Widget _buildAppBarIcon(String assetPath, double size, {CallType? callType}) => InkWell(
      onTap: () {
        if (callType != null) {
          // Navigate to the FriendsCallPage with appropriate settings
          Navigator.push(
            context,
            MaterialPageRoute(builder:
                  (context) => FriendsCallPage(
                    friendId: widget.friendId,
                    friendName: widget.friendName,
                    friendProfilePhoto: widget.friendProfilePhoto,
                    initialIsVideoEnabled: callType == CallType.video,
                    initialIsScreenSharing: callType == CallType.screenShare,
                  ),
            ),
          );
        }
      },
      child: SvgPicture.asset(
        assetPath,
        colorFilter: const ColorFilter.mode(Colors.white, BlendMode.srcIn),
        height: size,
        width: size,
      ),
    );

  // --- Date Separator ---
  Widget _buildDateSeparator(DateTime date) => Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Center(
        child: Text(
          DateFormat.yMMMMd().format(date), // e.g., February 12, 2022
          style: const TextStyle(
            color: Color(0xFF00FFFF), // Cyan color
            fontSize: 14,
          ),
        ),
      ),
    );

  // --- Message Input Area ---
  Widget _buildMessageInputArea(Color backgroundColor) => Material(
      color: Colors.transparent,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              // TODO: Add Emoji Picker Button if desired (requires package)
              // IconButton(
              //   icon: Icon(Icons.emoji_emotions_outlined, color: Colors.grey),
              //   onPressed: () { /* Implement emoji picker */ },
              // ),
              Expanded(
                child: TextField(
                  controller: _textController,
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Message',
                    hintStyle: TextStyle(color: Colors.grey[500]),
                    filled: true,
                    fillColor: Colors.white.withOpacity(
                      0.1,
                    ), // Slightly lighter input field
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 10,
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                  ),
                  textCapitalization: TextCapitalization.sentences,
                  minLines: 1,
                  maxLines: 5, // Allow multi-line input
                  onChanged: _onTextChanged,
                ),
              ),
              IconButton(
                icon: Icon(Icons.attach_file, color: Colors.grey[500]),
                onPressed: _pickFile,
              ),
              IconButton(
                icon: Icon(Icons.camera_alt_outlined, color: Colors.grey[500]),
                onPressed: _takePhoto,
              ),
              IconButton(
                icon:
                    _hasText
                        ? SvgPicture.asset(
                          'assets/icons/location-arrow.svg',
                          width: 24,
                          height: 24,
                          colorFilter: const ColorFilter.mode(
                            Color(0xFF00FFFF),
                            BlendMode.srcIn,
                          ),
                        )
                        : Icon(Icons.mic, color: Colors.grey[500]),
                onPressed: _hasText ? _sendMessage : _startRecording,
              ),
            ],
          ),
        ),
      ),
    );

  // New: Build the typing indicator
  Widget _buildTypingIndicator() => Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 4,
      ), // Match ChatBubble padding
      child: Align(
        alignment: Alignment.centerLeft,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.3),
            ),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDot(0),
              const SizedBox(width: 3),
              _buildDot(1),
              const SizedBox(width: 3),
              _buildDot(2),
            ],
          ),
        ),
      ),
    );

  // New: Build each dot in the typing indicator with animation
  Widget _buildDot(int index) => TweenAnimationBuilder<double>(
      tween: Tween(begin: 0, end: 1),
      duration: Duration(
        milliseconds: 300 + (index * 200),
      ), // Staggered animation
      builder: (context, value, child) => Transform.translate(
          offset: Offset(0, -3 * sin(value * 3.14)),
          child: Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.7),
              shape: BoxShape.circle,
            ),
          ),
        ),
    );

  Widget _buildReplyBar() {
    final isMe = _replyToMessage!.senderId == ChatPage.currentUserId;

    return Container(
      color: const Color(0xFF1A2B4D).withValues(alpha: 0.8),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: isMe ? const Color(0xFF00A3FF) : Colors.greenAccent[400],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isMe
                      ? 'Replying to yourself'
                      : 'Replying to ${widget.friendName}',
                  style: TextStyle(
                    fontSize: 12,
                    color:
                        isMe
                            ? const Color(0xFF00A3FF)
                            : Colors.greenAccent[400],
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _replyToMessage!.content,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white70),
            onPressed: _cancelReply,
            iconSize: 20,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  // Voice recorder UI
  Widget _buildVoiceRecorderWidget(VoiceRecordingState state) => Container(
      color: const Color(0xFF1A2B4D).withValues(alpha: 0.9),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Recording icon with animation
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: state.isPaused ? Colors.amber : Colors.red,
              boxShadow: [
                BoxShadow(
                  color: (state.isPaused ? Colors.amber : Colors.red).withOpacity(
                    0.5,
                  ),
                  blurRadius: state.isPaused ? 0 : 10,
                  spreadRadius: state.isPaused ? 0 : 2,
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          // Timer display
          Text(
            state.formattedDuration,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
              fontFamily: 'monospace',
            ),
          ),
          const Spacer(),
          // Cancel button
          IconButton(
            icon: const Icon(Icons.delete_outline, color: Colors.white70),
            onPressed: _cancelRecording,
          ),
          const SizedBox(width: 8),
          // Pause/Resume button
          IconButton(
            icon: Icon(
              state.isPaused ? Icons.play_arrow : Icons.pause,
              color: Colors.white,
            ),
            onPressed: state.isPaused ? _resumeRecording : _pauseRecording,
          ),
          const SizedBox(width: 8),
          // Send button
          IconButton(
            icon: SvgPicture.asset(
              'assets/icons/location-arrow.svg',
              width: 24,
              height: 24,
              colorFilter: const ColorFilter.mode(
                Color(0xFF00FFFF),
                BlendMode.srcIn,
              ),
            ),
            onPressed: _stopRecording,
          ),
        ],
      ),
    );

  // --- Navigation Helper (Optional but good practice) ---
  void _navigateToUserProfile() {
    // Hide nav bar before navigation
    Provider.of<NavBarVisibilityNotifier>(context, listen: false).hideNavBar();
    // Use GoRouter instead of MaterialPageRoute
    context.push('${AppRoutes.userProfile}/${widget.friendId}');
  }

  // Start recording
  Future<void> _startRecording() async {
    try {
      if (!await _requestPermission(Permission.microphone)) return;
      
      // Start recording using BLoC
      _voiceRecordingBloc.add(const StartRecording());
    } catch (e) {
      if (mounted) {
        CustomToast.showError(context, 'Failed to start recording: $e');
      }
    }
  }

  // Pause recording
  Future<void> _pauseRecording() async {
    try {
      _voiceRecordingBloc.add(const PauseRecording());
    } catch (e) {
      if (mounted) {
        CustomToast.showError(context, 'Failed to pause recording: $e');
      }
    }
  }

  // Resume recording
  Future<void> _resumeRecording() async {
    try {
      _voiceRecordingBloc.add(const ResumeRecording());
    } catch (e) {
      if (mounted) {
        CustomToast.showError(context, 'Failed to resume recording: $e');
      }
    }
  }

  // Stop recording and send or cancel
  Future<void> _stopRecording({bool sendMessage = true}) async {
    try {
      _voiceRecordingBloc.add(const StopRecording());

      // Note: The actual file path handling is now done in the VoiceRecordingBloc
      // We would need to subscribe to the bloc state to get the file path
      // For now, this is a simplified implementation
    } catch (e) {
      if (mounted) {
        CustomToast.showError(context, 'Failed to stop recording: $e');
      }
    }
  }

  // Cancel recording
  Future<void> _cancelRecording() async {
    await _stopRecording(sendMessage: false);

    // Delete the recorded file if it exists
    if (_recordedFilePath != null) {
      try {
        final file = File(_recordedFilePath!);
        if (await file.exists()) {
          await file.delete();
        }
      } catch (e) {
        debugPrint('Error deleting temporary recording: $e');
      }
    }

    _recordedFilePath = null;
  }

  Future<void> _sendMessage() async {
    final text = _textController.text.trim();
    if (text.isEmpty) return;

    // Send message through BLoC
    _chatBloc.add(SendMessage(
      text: text.trim(),
      bubbleId: widget.bubbleId,
    ));

    setState(() {
      _replyToMessage = null;
    });
    _textController.clear();
  }

  Future<void> _sendMediaMessage(
    File file,
    MediaType type, {
    String? mediaTitle,
  }) async {
    // Create a temporary message with sending status
    final tempMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: mediaTitle ?? 'Media file',
      timestamp: DateTime.now(),
      senderId: ChatPage.currentUserId,
      mediaType: type,
      mediaUrl: file.path, // Temporary local file path
      caption: mediaTitle,
    );

    try {
      // Add message immediately to show loading state
      setState(() {
        _messages.insert(0, tempMessage);
      });

      // Use the repository to send media message
      final result = await _chatBloc.sendMediaMessage(
        chatId: widget.friendId,
        senderId: ChatPage.currentUserId,
        mediaFile: file.path,
        mediaType: type.name,
        caption: mediaTitle,
      );

      // For now, just show success/error messages since the result type is Map
      if (result['success'] == true) {
        // Update message with the sent message data
        final index = _messages.indexWhere((msg) => msg.id == tempMessage.id);
        if (index != -1) {
          setState(() {
            _messages[index] = tempMessage.copyWith(isDelivered: true);
          });
        }
      } else {
        // Update message status to failed on error
        final index = _messages.indexWhere((msg) => msg.id == tempMessage.id);
        if (index != -1) {
          setState(() {
            _messages[index] = tempMessage.copyWith(isDelivered: false);
          });
        }
        CustomToast.showError(context, 'Failed to send media: ${result['error'] ?? 'Unknown error'}');
      }
    } catch (e) {
      print('Error sending media message: $e');
      
      // Update message status to failed on error
      final index = _messages.indexWhere((msg) => msg.id == tempMessage.id);
      if (index != -1) {
        setState(() {
          _messages[index] = tempMessage.copyWith(isDelivered: false);
        });
      }
      
      CustomToast.showError(context, 'Failed to send media: $e');
    }
  }

  // --- Permission Handling Helper ---
  Future<bool> _requestPermission(Permission permission) async {
    final status = await permission.status;
    if (status.isGranted) {
      return true;
    }
    if (status.isDenied) {
      if (await permission.request().isGranted) {
        return true;
      } else {
        // User denied the permission
        if (mounted) {
          // Check if widget is still in the tree
          CustomToast.showError(context, '${permission.toString().split('.').last} permission denied');
        }
        return false;
      }
    } else if (status.isPermanentlyDenied) {
      // User permanently denied the permission
      if (mounted) {
        // Check if widget is still in the tree
        CustomToast.showError(context, '${permission.toString().split('.').last} permission permanently denied, please enable it in settings');
      }
      return false;
    }
    return false; // Should not happen unless restricted, etc.
  }
  // --- End Permission Handling Helper ---

  Future<void> _takePhoto() async {
    // Use our enhanced camera experience with CameraAwesomePage
    await CameraAwesomePage.captureMediaAndSend(
      context,
      onSendMedia: (result) async {
        // This is the logic that was previously after captureMedia returned
        await _sendMediaMessage(
          File(result.file.path),
          result.isVideo ? MediaType.video : MediaType.image,
        );
      },
    );
    // No need to check the 'sentSuccessfully' boolean here for now,
    // as CameraAwesomePage handles showing an error SnackBar.
    // If further action is needed based on success/failure, it can be added.
  }

  Future<void> _pickFile() async {
    // Request storage permission ONLY IF NOT ON WEB
    if (!kIsWeb && !await _requestPermission(Permission.storage)) return;

    try {
      final result = await FilePicker.platform.pickFiles();
      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        final fileName = result.files.first.name;

        await _sendMediaMessage(file, MediaType.file, mediaTitle: fileName);
      }
    } catch (e) {
      if (mounted) {
        CustomToast.showError(context, 'Error picking file: $e');
      }
    }
  }

  void _onTextChanged(String text) {
    // Send typing indicator through repository
    _chatBloc.add(SendTypingStatus(isTyping: text.isNotEmpty));
  }

  void _handleReply(ChatMessage message) {
    setState(() {
      _replyToMessage = message;
    });
    // Focus the text field for reply
    FocusScope.of(context).requestFocus(FocusNode());
    _textController.text = '';
  }

  void _cancelReply() {
    setState(() {
      _replyToMessage = null;
    });
  }

  void _handleForward(ChatMessage message) {
    final forwardedMessage = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: message.content,
      timestamp: DateTime.now(),
      senderId: ChatPage.currentUserId,
      mediaType: message.mediaType,
      mediaUrl: message.mediaUrl,
      caption: message.caption,
    );

    setState(() {
      _messages.insert(0, forwardedMessage);
    });

    // Send forwarded message through chat bloc
    context.read<ChatBloc>().add(SendMessageEvent(
      chatId: widget.friendId,
      message: message.content,
      mediaType: message.mediaType.name,
    ));

    CustomToast.showInfo(context, 'Message forwarded');
  }

  // DEACTIVATED: Reaction functionality
  // void _addReaction(String messageId, String emoji) {
  //   final index = _messages.indexWhere((msg) => msg.id == messageId);
  //   if (index != -1) {
  //     setState(() {
  //       final message = _messages[index];
  //       // Increment the count if this emoji already exists, otherwise set to 1
  //       message.reactions[emoji] = (message.reactions[emoji] ?? 0) + 1;
  //       // Force rebuild
  //       _messages[index] = message;
  //     });
  //   }
  // }

  // Handle media tap
  void _handleMediaTap(ChatMessage message) {
    if (message.mediaType == MediaType.audio) {
      // Play audio message
      _audioPlayer.play(UrlSource(message.mediaUrl!));

      CustomToast.showInfo(context, 'Playing voice message');
    } else {
      // Existing code for other media types
      CustomToast.showInfo(context, "Opening ${message.mediaType.toString().split('.').last}: ${message.mediaUrl}");
    }
  }

  void _onSearchChanged() {
    _performSearch(_searchController.text);
  }

  void _performSearch(String query) {
    if (query.isEmpty) {
      setState(() {
        _searchResults.clear();
      });
      return;
    }

    final results =
        _messages
            .where(
              (message) =>
                  message.content.toLowerCase().contains(query.toLowerCase()) ||
                  (message.caption?.toLowerCase().contains(
                        query.toLowerCase(),
                      ) ??
                      false),
            )
            .toList();

    setState(() {
      _searchResults = results;
    });
  }

  void _toggleSearch() {
    setState(() {
      _isSearching = !_isSearching;
      if (!_isSearching) {
        _searchController.clear();
        _searchResults.clear();
      }
    });
  }

  void _scrollToMessage(String messageId) {
    // Find the index of the message in the original list
    final index = _messages.indexWhere((msg) => msg.id == messageId);
    if (index != -1) {
      // Close search
      setState(() {
        _isSearching = false;
        _searchResults.clear();
        _searchController.clear();
      });

      // Need to calculate the position in the ListView
      // This is a bit complex due to the reversed list and date separators
      var position = 0;
      var foundMessage = false;

      for (var i = _messages.length - 1; i >= 0; i--) {
        if (i == index) {
          foundMessage = true;
          break;
        }
        position++;

        // Check if there's a date separator after this message
        if (i > 0) {
          final hasSeparator =
              !DateUtils.isSameDay(
                _messages[i].timestamp,
                _messages[i - 1].timestamp,
              );
          if (hasSeparator) {
            position++;
          }
        }
      }

      if (foundMessage) {
        // Scroll to the message
        _scrollController.animateTo(
          position * 80.0, // Approximate height of each item
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );

        // Highlight the message temporarily
        // This would involve more complex state management to highlight a specific message
      }
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    _typingTimer?.cancel();
    _messageSubscription?.cancel();
    _audioPlayer.dispose();
    // Timer cleanup no longer needed - handled by VoiceRecordingBloc
    Provider.of<NavBarVisibilityNotifier>(context, listen: false).setNavBarVisible(true);
    _voiceRecordingBloc.close();
    super.dispose();
  }
}
