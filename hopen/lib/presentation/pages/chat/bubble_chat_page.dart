import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../di/injection_container_refactored.dart' as di;
import '../../../statefulbusinesslogic/bloc/chat/chat_bloc.dart';
import '../../../statefulbusinesslogic/bloc/chat/chat_event.dart';
import '../../../statefulbusinesslogic/bloc/chat/chat_state.dart';
import '../../../statefulbusinesslogic/bloc/voice_recording/voice_recording_bloc.dart';
import '../../../statefulbusinesslogic/bloc/voice_recording/voice_recording_event.dart';
import '../../../statefulbusinesslogic/bloc/voice_recording/voice_recording_state.dart';
import '../../widgets/keyboard_dismissible.dart';

// Define the color palette for bubble members (same as BubblePage)
const List<Color> _memberColors = [
  Color(0xFF10FFED), // Bright Cyan
  Color(0xFF64FF93), // Bright Green
  Color(0xFFC4FF2D), // Lime Green
  Color(0xFFF0FF00), // Yellow
];

class BubbleChatPage extends StatefulWidget {
  final String chatId;

  const BubbleChatPage({
    Key? key,
    required this.chatId,
  }) : super(key: key);

  @override
  State<BubbleChatPage> createState() => _BubbleChatPageState();
}

class _BubbleChatPageState extends State<BubbleChatPage> {
  final TextEditingController _messageController = TextEditingController();
  late ChatBloc _chatBloc;
  late final VoiceRecordingBloc _voiceRecordingBloc;

  @override
  void initState() {
    super.initState();
    _chatBloc = context.read<ChatBloc>();
    _voiceRecordingBloc = di.sl<VoiceRecordingBloc>();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _voiceRecordingBloc.close();
    super.dispose();
  }

  void _handleStartRecording() {
    _voiceRecordingBloc.add(const StartRecording());
  }

  void _handleStopRecording() {
    _voiceRecordingBloc.add(const StopRecording());
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VoiceRecordingBloc, VoiceRecordingState>(
      bloc: _voiceRecordingBloc,
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('Bubble Chat'),
          ),
          body: KeyboardDismissible(
            child: Column(
              children: [
                Expanded(
                  child: BlocBuilder<ChatBloc, ChatState>(
                    builder: (context, state) {
                      if (state is ChatLoading) {
                        return const Center(child: CircularProgressIndicator());
                      }
                      if (state is ChatLoaded) {
                        return ListView.builder(
                          reverse: true,
                          itemCount: state.messages.length,
                          itemBuilder: (context, index) {
                            final message = state.messages[index];
                            return ListTile(
                              title: Text(message.content),
                            );
                          },
                        );
                      }
                      return const Center(child: Text('No messages'));
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () {
                          // TODO: Show media picker
                        },
                        icon: const Icon(Icons.attach_file),
                      ),
                      Expanded(
                        child: TextField(
                          controller: _messageController,
                          decoration: const InputDecoration(
                            hintText: 'Type a message...',
                          ),
                        ),
                      ),
                      GestureDetector(
                        onLongPress: _handleStartRecording,
                        onLongPressUp: _handleStopRecording,
                        child: IconButton(
                          onPressed: () {
                            if (_messageController.text.isNotEmpty) {
                              _chatBloc.add(SendMessageEvent(
                                bubbleId: widget.chatId,
                                message: _messageController.text,
                                mediaType: 'text',
                              ));
                              _messageController.clear();
                            }
                          },
                          icon: Icon(state.isRecording ? Icons.mic : Icons.send),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
