import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'package:uuid/uuid.dart';
import 'dart:async';
import 'dart:io';

import '../../../provider/repositories/auth/auth_repository_impl.dart';
import '../../../provider/services/auth/ory_auth_service.dart';
import '../../../provider/notifiers/user_profile_notifier.dart';
import '../../../repositories/auth/auth_repository.dart';
import '../../../repositories/user/user_repository.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_event.dart';
import '../../../statefulbusinesslogic/core/models/user_model.dart';
import '../../../statefulbusinesslogic/core/notifiers/nav_bar_visibility_notifier.dart';
import '../../../provider/services/image_processing_service.dart';
import '../../../provider/repositories/profile_picture/profile_picture_repository_impl.dart';
import '../../../di/injection_container_refactored.dart' as di;
import '../../router/app_router.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/main_app_bar.dart';
import '../../widgets/profile_option_tile.dart';
import '../../widgets/profile_picture_widget.dart';
import '../../widgets/user_profile_card.dart';
import '../../widgets/custom_toast.dart';
import './about_page.dart';
import 'help_support_page.dart';
import 'language_selection_page.dart';
import 'notifications_page.dart';
import 'privacy_page.dart';
import 'security_page.dart';
import '../../../presentation/widgets/image_picker_bottom_sheet.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_state.dart';
import '../../../statefulbusinesslogic/bloc/user_profile/user_profile_bloc.dart';

/// The main user profile screen.
///
/// Displays user information via [UserProfileCard] and provides access to
/// various sub-pages like settings (notifications, privacy, security, language),
/// help, and logout functionality.
class ProfilePage extends StatefulWidget {
  /// Constructs a [ProfilePage].
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

/// State for [ProfilePage].
///
/// Manages the visibility of the main navigation bar when navigating
/// to child pages pushed onto the navigation stack.
class _ProfilePageState extends State<ProfilePage> {
  late NavBarVisibilityNotifier _navBarNotifier;
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _usernameController = TextEditingController();
  bool _isProcessingImage = false;
  String? _localImagePath; // Holds local path for immediate preview

  // Cache user data to prevent glitches during profile picture updates
  String? _cachedFirstName;
  String? _cachedLastName;
  String? _cachedUsername;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Obtain the notifier controlling nav bar visibility.
    _navBarNotifier = Provider.of<NavBarVisibilityNotifier>(
      context,
      listen: false,
    );

    // Show nav bar if this page is the current top-most route.
    final route = ModalRoute.of(context);
    if (route?.isCurrent ?? false) {
      _navBarNotifier.showNavBar();
    }
  }

  @override
  void initState() {
    super.initState();
    // Don't clear local image path - let it persist across navigation

    // Initialize user profile with Provider (replaces BLoC)
    final authState = context.read<AuthBloc>().state;
    if (authState.status == AuthStatus.authenticated &&
        authState.userId != null) {
      print(
        '🔍 ProfilePage.initState: Initializing user profile for ${authState.userId} with Provider',
      );

      // Initialize the UserProfileNotifier with the current user
      // This will automatically load cached data instantly and refresh in background
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final userProfileNotifier = context.read<UserProfileNotifier>();
        userProfileNotifier.initializeUser(authState.userId!);
      });
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _usernameController.dispose();
    _navBarNotifier.showNavBar();
    super.dispose();
  }

  // void _populateFields(UserModel user) {
  // _firstNameController.text = user.firstName ?? '';
  // _lastNameController.text = user.lastName ?? '';
  // _emailController.text = user.email ?? '';
  // _usernameController.text = user.username ?? '';
  // }

  Future<void> _handleProfilePictureUpdate() async {
    // Get current user data for the dialog
    final authState = context.read<AuthBloc>().state;
    final profileState = context.read<UserProfileBloc>().state;

    String? firstName;
    String? lastName;
    String? avatarUrl;

    if (profileState is UserProfileLoaded) {
      firstName = profileState.user.firstName;
      lastName = profileState.user.lastName;
      avatarUrl = _localImagePath ?? profileState.user.profilePictureUrl;
    } else {
      firstName = _cachedFirstName ?? authState.firstName;
      lastName = _cachedLastName ?? '';
      avatarUrl = _localImagePath;
    }

    // Use the ProfilePicturePickerDialog with hero animation
    await ProfilePicturePickerDialog.show(
      context,
      imageUrl: avatarUrl,
      firstName: firstName,
      lastName: lastName,
    );
  }

  Future<void> _handleNameEdit(
    String firstName,
    String lastName,
    String username,
  ) async {
    try {
      // Get current user ID
      final authState = context.read<AuthBloc>().state;
      final currentUserId = authState.userId;

      if (currentUserId == null) {
        CustomToast.showError(context, 'User not authenticated');
        return;
      }

      // Update profile via API
      final userRepository = di.sl<UserRepository>();

      // Create updated user model
      final updatedUser = UserModel(
        id: currentUserId,
        firstName: firstName,
        lastName: lastName,
        username: username,
        email: authState.email, // Keep existing email
        profilePictureUrl: _localImagePath, // Keep current profile picture
      );

      await userRepository.updateUser(updatedUser);

      // Refresh Ory session data to get updated user info
      try {
        final oryAuthService = di.sl<OryAuthService>();
        await oryAuthService.refreshUserData();
        print('✅ Ory user data refreshed after profile update');

        // Clear the cached user in AuthRepository to force fresh data on next login
        final authRepository = di.sl<AuthRepository>();
        if (authRepository is AuthRepositoryImpl) {
          (authRepository as AuthRepositoryImpl).clearCachedUser();
          print('✅ AuthRepository cache cleared to force fresh user data');
        }
      } catch (e) {
        print('⚠️ Failed to refresh Ory user data: $e');
        // Don't fail the whole operation if refresh fails
      }

      // Force AuthBloc to refresh user data from Ory
      // This ensures the "Welcome back" message uses the updated name
      context.read<AuthBloc>().add(CheckAuthStatusEvent());

      // Update cached values immediately for UI responsiveness
      setState(() {
        _cachedFirstName = firstName;
        _cachedLastName = lastName;
        _cachedUsername = username;
      });

      CustomToast.showSuccess(context, 'Profile updated successfully!');
    } catch (e) {
      print('❌ Failed to update profile: $e');
      CustomToast.showError(
        context,
        'Failed to update profile: ${e.toString()}',
      );
    }
  }

  Future<void> _takePhoto() async {
    if (Navigator.of(context).canPop()) Navigator.of(context).pop();

    setState(() {
      _isProcessingImage = true;
    });

    try {
      print('🚀 Starting client-side image processing for profile update...');

      // Take photo with client-side processing
      final imageFile = await ImageProcessingService.takePhoto();
      if (imageFile != null) {
        await _processAndUploadImage(imageFile);
      }
    } catch (e) {
      print('❌ Failed to take photo: $e');
      CustomToast.showError(context, 'Failed to take photo: ${e.toString()}');
    } finally {
      setState(() {
        _isProcessingImage = false;
      });
    }
  }

  Future<void> _pickFromGallery() async {
    if (Navigator.of(context).canPop()) Navigator.of(context).pop();

    setState(() {
      _isProcessingImage = true;
    });

    try {
      print('🚀 Starting client-side image processing for profile update...');

      // Pick from gallery with client-side processing
      final imageFile = await ImageProcessingService.pickFromGallery();
      if (imageFile != null) {
        await _processAndUploadImage(imageFile);
      }
    } catch (e) {
      print('❌ Failed to pick from gallery: $e');
      CustomToast.showError(context, 'Failed to pick image: ${e.toString()}');
    } finally {
      setState(() {
        _isProcessingImage = false;
      });
    }
  }

  Future<void> _processAndUploadImage(XFile imageFile) async {
    try {
      print('🔄 Starting client-side image processing...');
      final originalSize = await imageFile.length();
      print(
        '📁 Original image size: ${(originalSize / 1024 / 1024).toStringAsFixed(2)}MB',
      );

      // Process image completely on client side: resize to 1440x1440 and convert to WebP
      print(
        '✨ Processing strategy: Client-side resize to 1440x1440 and WebP conversion',
      );
      final processedBytes = await ImageProcessingService.processImageFromFile(
        imageFile,
      );

      print(
        '📊 Client-side processing: ${(originalSize / 1024 / 1024).toStringAsFixed(2)}MB → ${(processedBytes.length / 1024 / 1024).toStringAsFixed(2)}MB',
      );
      print('📐 Final dimensions: 1440x1440 pixels');
      print('🎯 Format: WebP (ready for backend storage)');

      // Create temporary file for upload
      final tempDir = await Directory.systemTemp.createTemp('hopen_profile_');
      final fileName = '${const Uuid().v4()}.webp';
      final localPath = path.join(tempDir.path, fileName);
      final localFile = File(localPath);
      await localFile.writeAsBytes(processedBytes);

      // Set local path for immediate display
      setState(() {
        _localImagePath = localPath;
      });

      print('💾 Temporary file created: $localPath');
      print('☁️ Starting upload to backend...');

      // Upload using ProfilePictureRepository
      final repository = di.sl<ProfilePictureRepositoryImpl>();
      final result = await repository.uploadLocalProfilePicture(localPath);

      if (result != null) {
        print('✅ Profile picture uploaded successfully: $result');
        CustomToast.showSuccess(
          context,
          'Profile picture updated successfully!',
        );

        // Reload user profile to get updated avatar URL from backend
        Future.delayed(const Duration(milliseconds: 800), () {
          if (mounted) {
            print(
              '🔄 Reloading user profile to get updated avatar URL from backend',
            );
            final authState = context.read<AuthBloc>().state;
            if (authState.status == AuthStatus.authenticated &&
                authState.userId != null) {
              context.read<UserProfileBloc>().add(
                LoadUserProfileEvent(authState.userId!, forceReload: true),
              );
            }

            // Clear local path after a short delay to allow backend data to load
            Future.delayed(const Duration(milliseconds: 600), () {
              if (mounted) {
                print('🔄 Clearing local image path to use backend URL');
                setState(() {
                  _localImagePath = null;
                });
              }
            });
          }
        });
      } else {
        print('❌ Profile picture upload failed');
        CustomToast.showError(context, 'Failed to upload profile picture');
        setState(() {
          _localImagePath = null;
        });
      }
    } catch (e) {
      print('❌ Failed to process and upload image: $e');
      CustomToast.showError(
        context,
        'Failed to update profile picture: ${e.toString()}',
      );
      setState(() {
        _localImagePath = null;
      });
    }
  }

  Future<void> _removeProfilePicture() async {
    try {
      // Get current user's profile picture URL
      final authState = context.read<AuthBloc>().state;
      final profileState = context.read<UserProfileBloc>().state;

      if (authState.userId != null && profileState is UserProfileLoaded) {
        final currentProfilePictureUrl = profileState.user.profilePictureUrl;

        if (currentProfilePictureUrl != null &&
            currentProfilePictureUrl.isNotEmpty) {
          print('🗑️ Removing profile picture: $currentProfilePictureUrl');

          // Use the ProfilePictureRepository to remove the profile picture
          final repository = di.sl<ProfilePictureRepositoryImpl>();
          final success = await repository.removeProfilePicture(
            currentProfilePictureUrl,
          );

          if (success) {
            print('✅ Profile picture removed successfully');

            // Clear local image path and reload user profile
            setState(() {
              _localImagePath = null;
            });

            // Reload user profile to reflect the removal
            context.read<UserProfileBloc>().add(
              LoadUserProfileEvent(authState.userId!),
            );

            CustomToast.showWarning(
              context,
              'Profile picture removed successfully',
            );
          } else {
            print('❌ Failed to remove profile picture');
            CustomToast.showError(context, 'Failed to remove profile picture');
          }
        } else {
          CustomToast.showInfo(context, 'No profile picture to remove');
        }
      }
    } catch (e) {
      print('❌ Error removing profile picture: $e');
      CustomToast.showError(
        context,
        'Failed to remove profile picture: ${e.toString()}',
      );
    }
  }

  void _saveProfile() {
    if (_formKey.currentState?.validate() ?? false) {
      // TODO: Implement profile update using proper current user profile service
      // For now, just show success message
      CustomToast.showSuccess(context, 'Profile updated successfully!');
    }
  }

  /// Navigates to a sub-page using [MaterialPageRoute].
  ///
  /// Hides the main navigation bar before pushing the new route and
  /// shows it again upon returning, provided the widget is still mounted.
  ///
  /// [page]: The widget for the sub-page route.
  Future<void> _navigateToSubPage(Widget page) async {
    _navBarNotifier.hideNavBar();
    await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => page),
    );
    if (mounted) {
      _navBarNotifier.showNavBar();
    }
  }

  void _handleImageSelected(String imagePath) {
    // TODO: Update user profile picture using proper current user profile service
    CustomToast.showSuccess(context, 'Profile picture updated!');
  }

  void _showImagePicker() {
    _navBarNotifier.hideNavBar();
    showModalBottomSheet(
      context: context,
      builder:
          (context) =>
              ImagePickerBottomSheet(onImageSelected: _handleImageSelected),
    ).then((_) => _navBarNotifier.showNavBar());
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final baseTileHeight = screenHeight / 12;
    final iconContainerSize = baseTileHeight * 0.7;
    final imageSize = iconContainerSize * 0.8;

    // Standard trailing icon for navigation list tiles.
    const trailingArrow = Icon(
      Icons.arrow_forward_ios,
      size: 16,
      color: Colors.white70,
    );

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: const MainAppBar(),
        body: SafeArea(
          bottom:
              false, // Prevent overlap with the persistent bottom navigation bar.
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Centered page title.
                const Center(
                  child: Padding(
                    padding: EdgeInsets.zero,
                    child: Text(
                      'Profile',
                      style: TextStyle(
                        color: Color(0xFF00FFFF), // Cyan
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      // Ensure content scrolls above the navigation bar area.
                      padding: const EdgeInsets.only(bottom: 80),
                      child: Column(
                        children: [
                          // --- User Profile Display (Provider-based with instant cache loading) ---
                          Consumer<UserProfileNotifier>(
                            builder: (context, userProfileNotifier, child) {
                              return BlocBuilder<AuthBloc, AuthState>(
                                builder: (context, authState) {
                                  if (authState.status ==
                                      AuthStatus.authenticated) {
                                    final user =
                                        userProfileNotifier.currentUser;

                                    if (user != null) {
                                      // User data available from cache or network
                                      final avatarUrl =
                                          _localImagePath ??
                                          user.profilePictureUrl;

                                      // Cache user data to prevent glitches during profile reloads
                                      _cachedFirstName = user.firstName ?? '';
                                      _cachedLastName = user.lastName ?? '';
                                      _cachedUsername = user.username ?? '';

                                      print(
                                        '🖼️ ProfilePage - Provider loaded - user.profilePictureUrl: ${user.profilePictureUrl}',
                                      );
                                      print(
                                        '🖼️ ProfilePage - Provider loaded - _localImagePath: $_localImagePath',
                                      );
                                      print(
                                        '🖼️ ProfilePage - Provider loaded - final avatarUrl: $avatarUrl',
                                      );
                                      print(
                                        '🖼️ ProfilePage - Provider loaded - cached data: $_cachedFirstName $_cachedLastName @$_cachedUsername',
                                      );

                                      return UserProfileCard(
                                        firstName: _cachedFirstName!,
                                        lastName: _cachedLastName!,
                                        username: _cachedUsername!,
                                        avatarUrl: avatarUrl,
                                        baseTileHeight: baseTileHeight,
                                        onProfilePictureTap:
                                            _isProcessingImage
                                                ? null
                                                : _handleProfilePictureUpdate,
                                        onNameEdit: _handleNameEdit,
                                      );
                                    } else {
                                      // No user data yet - show loading with cached data or auth data
                                      // This will only show briefly while the cache is being loaded
                                      print(
                                        '🖼️ ProfilePage - Loading state - _localImagePath: $_localImagePath',
                                      );
                                      print(
                                        '🖼️ ProfilePage - Loading state - using cached data: $_cachedFirstName $_cachedLastName @$_cachedUsername',
                                      );

                                      return UserProfileCard(
                                        firstName:
                                            _cachedFirstName ??
                                            authState.firstName ??
                                            'Loading...',
                                        lastName: _cachedLastName ?? '',
                                        username:
                                            _cachedUsername ?? 'Loading...',
                                        avatarUrl: _localImagePath,
                                        baseTileHeight: baseTileHeight,
                                        onProfilePictureTap:
                                            null, // Disable during loading
                                        onNameEdit:
                                            null, // Disable during loading
                                      );
                                    }
                                  }
                                  // Not authenticated - show empty profile
                                  return UserProfileCard(
                                    firstName: '',
                                    lastName: '',
                                    username: '',
                                    baseTileHeight: baseTileHeight,
                                    onProfilePictureTap: null,
                                    onNameEdit:
                                        null, // Disable name edit when not authenticated
                                  );
                                },
                              );
                            },
                          ),
                          // --- Navigation Options ---
                          ProfileOptionTile(
                            title: 'Notifications',
                            subtitle:
                                'Manage notifications and security preferences',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/bell.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap:
                                () => _navigateToSubPage(
                                  const NotificationsPage(),
                                ),
                            trailing: trailingArrow,
                          ),
                          ProfileOptionTile(
                            title: 'Privacy',
                            subtitle:
                                'Control your account privacy and blocked users',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/lock.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap:
                                () => _navigateToSubPage(const PrivacyPage()),
                            trailing: trailingArrow,
                          ),
                          ProfileOptionTile(
                            title: 'Language',
                            subtitle: 'Choose your preferred language',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/text.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap:
                                () => _navigateToSubPage(
                                  const LanguageSelectionPage(),
                                ),
                            trailing: trailingArrow,
                          ),
                          ProfileOptionTile(
                            title: 'Security',
                            subtitle:
                                'Manage your password and account security',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/shield.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap:
                                () => _navigateToSubPage(const SecurityPage()),
                            trailing: trailingArrow,
                          ),
                          ProfileOptionTile(
                            title: 'Help & support',
                            subtitle: 'Get help and report issues',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/chat-text.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap:
                                () =>
                                    _navigateToSubPage(const HelpSupportPage()),
                            trailing: trailingArrow,
                          ),
                          ProfileOptionTile(
                            title: 'About',
                            subtitle: 'Learn more about Hopen',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/zoom.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap: () => _navigateToSubPage(const AboutPage()),
                            trailing: trailingArrow,
                          ),
                          // --- Logout Action ---
                          ProfileOptionTile(
                            title: 'Logout',
                            subtitle: 'Sign out of your account',
                            iconWidget: Image.asset(
                              'assets/images/3d/200px/normal/moon.png',
                              width: imageSize,
                              height: imageSize,
                            ),
                            onTap: () {
                              // Implement actual logout logic
                              context.read<AuthBloc>().add(const LogoutEvent());
                              _navBarNotifier
                                  .showNavBar(); // Ensure nav bar is visible for login screen
                              context.go(
                                AppRoutes.login,
                              ); // Navigate to login via go_router
                            },
                            trailing: trailingArrow,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
