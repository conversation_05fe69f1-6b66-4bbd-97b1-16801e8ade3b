import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../widgets/animated_gradient_background.dart';

class AboutPage extends StatelessWidget {
  const AboutPage({super.key});

  static const String routeName =
      '/profile/about'; // Optional: for named routes

  @override
  Widget build(BuildContext context) {
    // Get screen dimensions for responsive sizing
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;

    // Calculate responsive logo size
    // Let's make the logo height 15% of the screen height
    // and width 30% of screen width as a starting point.
    // Adjust these percentages as needed for desired appearance.
    final logoHeight = screenHeight * 0.30;
    final logoWidth = screenWidth * 0.60;

    return AnimatedGradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('About'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Image.asset(
                    'assets/images/hopen-logo.png',
                    height: logoHeight,
                    width: logoWidth,
                    fit: BoxFit.contain,
                  ),
                  SvgPicture.asset(
                    'assets/icons/hopen-logotype.svg',
                    height: screenWidth * 0.3,
                    colorFilter: const ColorFilter.mode(
                      Colors.white,
                      BlendMode.srcIn,
                    ),
                  ),
                  const Text(
                    'Version 0.1.1',
                    style: TextStyle(fontSize: 18, color: Colors.white70),
                  ),
                  const SizedBox(height: 32),
                  const Text(
                    'Hopen is designed to connect people and build communities through shared experiences and support.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white,
                      height: 1.5,
                    ),
                  ),
                  const SizedBox(height: 24),
                  const Text(
                    '© 2025 Hopen Inc. All rights reserved.',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 14, color: Colors.white70),
                  ),
                  // Add more information as needed (e.g., links to terms, privacy policy)
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
