import 'package:flutter/material.dart';
import '../../widgets/gradient_background.dart';

class LanguageSelectionPage extends StatefulWidget {
  const LanguageSelectionPage({super.key});

  @override
  State<LanguageSelectionPage> createState() => _LanguageSelectionPageState();
}

class _LanguageSelectionPageState extends State<LanguageSelectionPage> {
  // TODO: Replace with actual language management logic
  String _selectedLanguage = 'English'; // Default language

  // Updated list of available languages
  final List<String> _availableLanguages = [
    'English',
    'Spanish', // Español -> Spanish for consistency if using locale codes later
    'French', // Français -> French
    'Portuguese',
    'German', // Deutsch -> German
    'Italian', // Italiano -> Italian
    'Dutch',
    'Swedish',
    'Polish',
    'Turkish',
  ];

  void _changeLanguage(String? language) {
    // Allow null for DropdownButton onChanged
    if (language != null) {
      setState(() {
        _selectedLanguage = language;
      });
      // TODO: Add logic here to actually change the app's locale
      print('Selected language: $language');
      // You might use a Provider, Bloc, or GetX to manage the app's locale state
      // Example: context.read<LocaleProvider>().setLocale(Locale(languageCode));
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final fieldHeight = screenHeight / 16;

    // Calculate available width for the button/menu items
    final availableWidth = MediaQuery.of(context).size.width;

    // Define button styling consistent with login page
    const buttonTextColor = Colors.black;
    const buttonBackgroundColor = Colors.white;
    const borderRadiusValue = 16.0;
    const buttonTextStyle = TextStyle(
      color: buttonTextColor,
      fontSize: 18,
      fontFamily: 'Omnes',
      fontWeight: FontWeight.bold,
    );
    const menuItemTextStyle = TextStyle(
      color: Colors.black,
      fontSize: 16,
      fontFamily: 'Omnes',
    );

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('Language'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // Use PopupMenuButton for more control over menu appearance/position
                PopupMenuButton<String>(
                  offset: Offset(
                    0,
                    fieldHeight,
                  ), // Position menu below the button
                  onSelected: _changeLanguage,
                  color:
                      buttonBackgroundColor, // Set popup menu background color
                  padding: EdgeInsets.zero, // Remove default internal padding
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(borderRadiusValue),
                  ),
                  itemBuilder:
                      (context) =>
                          _availableLanguages
                              .map(
                                (language) => PopupMenuItem<String>(
                                  value: language,
                                  // Wrap child in SizedBox to enforce width
                                  child: SizedBox(
                                    width:
                                        availableWidth, // Use calculated width
                                    child: Text(
                                      language,
                                      style: menuItemTextStyle,
                                    ),
                                  ),
                                ),
                              )
                              .toList(),
                  // This child is the button that triggers the popup
                  child: Container(
                    height: fieldHeight,
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: BoxDecoration(
                      color: buttonBackgroundColor,
                      borderRadius: BorderRadius.circular(borderRadiusValue),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(_selectedLanguage, style: buttonTextStyle),
                        const Icon(
                          Icons.arrow_drop_down,
                          color: buttonTextColor,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
