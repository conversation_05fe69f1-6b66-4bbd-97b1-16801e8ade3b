import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../widgets/gradient_background.dart';
import '../../../statefulbusinesslogic/bloc/notification/notification_bloc.dart';
import '../../../statefulbusinesslogic/bloc/notification/notification_event.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  // Notification settings
  bool _bubbleChatMessages = true;
  bool _bubbleCalls = true;
  bool _friendsMessages = true;
  bool _friendsCalls = true;
  bool _contactRequests = true;
  bool _bubbleInvites = true;
  bool _contactRequestAcceptation = true;
  bool _bubbleInviteAcceptation = true;
  bool _bubbleMembersBirthday = true;
  bool _friendsBirthday = true;
  bool _contactsBirthday = true;
  bool _bubbleEnding = true;

  @override
  Widget build(BuildContext context) => GradientBackground(
    child: Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        title: const Text('Notifications'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: ListView(
          children: [
            _buildNotificationSwitch(
              'Bubble chat messages',
              _bubbleChatMessages,
              (value) => setState(() => _bubbleChatMessages = value),
            ),
            _buildNotificationSwitch(
              'Bubble calls',
              _bubbleCalls,
              (value) => setState(() => _bubbleCalls = value),
            ),
            _buildNotificationSwitch(
              'Friends messages',
              _friendsMessages,
              (value) => setState(() => _friendsMessages = value),
            ),
            _buildNotificationSwitch(
              'Friends calls',
              _friendsCalls,
              (value) => setState(() => _friendsCalls = value),
            ),
            _buildNotificationSwitch(
              'Contact requests',
              _contactRequests,
              (value) => setState(() => _contactRequests = value),
            ),
            _buildNotificationSwitch(
              'Bubble invites',
              _bubbleInvites,
              (value) => setState(() => _bubbleInvites = value),
            ),
            _buildNotificationSwitch(
              'Contact request acceptation',
              _contactRequestAcceptation,
              (value) => setState(() => _contactRequestAcceptation = value),
            ),
            _buildNotificationSwitch(
              'Bubble invite acceptation',
              _bubbleInviteAcceptation,
              (value) => setState(() => _bubbleInviteAcceptation = value),
            ),
            _buildNotificationSwitch(
              'Bubble members birthday',
              _bubbleMembersBirthday,
              (value) => setState(() => _bubbleMembersBirthday = value),
            ),
            _buildNotificationSwitch(
              'Friends birthday',
              _friendsBirthday,
              (value) => setState(() => _friendsBirthday = value),
            ),
            _buildNotificationSwitch(
              'Contacts birthday',
              _contactsBirthday,
              (value) => setState(() => _contactsBirthday = value),
            ),
            _buildNotificationSwitch(
              'Bubble ending',
              _bubbleEnding,
              (value) => setState(() => _bubbleEnding = value),
            ),
            const Divider(height: 32),
            // Test button for contact request notification
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton(
                onPressed: () => _testContactRequestNotification(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('🔔 Test Contact Request Notification'),
              ),
            ),
          ],
        ),
      ),
    ),
  );

  Widget _buildNotificationSwitch(
    String title,
    bool value,
    ValueChanged<bool> onChanged,
  ) => SwitchListTile(title: Text(title), value: value, onChanged: onChanged);

  /// Test method to trigger a contact request notification
  void _testContactRequestNotification(BuildContext context) {
    print('🔔 NotificationsPage: Test button pressed - triggering contact request notification');

    final notificationBloc = context.read<NotificationBloc>();

    // Dispatch a test contact request notification
    notificationBloc.add(ContactRequestNotificationReceivedEvent(
      requestId: 'test_request_${DateTime.now().millisecondsSinceEpoch}',
      requesterId: 'test_user_123',
      requesterName: 'Test User',
      requesterUsername: 'testuser',
      requesterProfilePicUrl: 'https://via.placeholder.com/150',
      requestTimestamp: DateTime.now(),
    ));

    // Show a snackbar to confirm the test was triggered
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('🔔 Contact request notification test triggered!'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
