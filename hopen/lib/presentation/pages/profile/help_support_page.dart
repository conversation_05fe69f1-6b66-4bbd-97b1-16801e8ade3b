import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../widgets/custom_toast.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/profile_option_tile.dart';
import '../../../statefulbusinesslogic/bloc/report/report_bloc.dart';
import '../../../statefulbusinesslogic/bloc/report/report_event.dart';
import '../../../statefulbusinesslogic/bloc/report/report_state.dart';
import '../../../di/injection_container_refactored.dart' as di;
import '../../../statefulbusinesslogic/core/notifiers/nav_bar_visibility_notifier.dart';
import '../../router/app_router.dart';
import '../../widgets/keyboard_dismissible.dart';
import '../../widgets/main_app_bar.dart';

class HelpSupportPage extends StatelessWidget {
  const HelpSupportPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    const Widget trailingArrow = Icon(
      Icons.arrow_forward_ios,
      size: 16,
      color: Colors.white70,
    );

    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('Help & Support'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: SafeArea(
          bottom: false,
          child: KeyboardDismissible(
            child: ListView(
              padding: const EdgeInsets.only(
                bottom: kBottomNavigationBarHeight + 20.0,
              ),
              children: [
                const SizedBox(height: 16),
                ProfileOptionTile(
                  title: 'Report a problem',
                  subtitle: 'Report technical issues or general problems',
                  iconWidget: const Icon(Icons.warning, color: Colors.white),
                  onTap:
                      () => _showReportDialog(
                        context,
                        'Report a problem',
                        "Describe the problem you're experiencing",
                      ),
                  trailing: trailingArrow,
                ),
                ProfileOptionTile(
                  title: 'Report a bug',
                  subtitle: 'Report app bugs or unexpected behavior',
                  iconWidget: const Icon(Icons.bug_report, color: Colors.white),
                  onTap:
                      () => _showReportDialog(
                        context,
                        'Report a bug',
                        "Describe the bug you've encountered",
                      ),
                  trailing: trailingArrow,
                ),
                ProfileOptionTile(
                  title: 'Report a user',
                  subtitle: 'Report inappropriate user behavior',
                  iconWidget: const Icon(Icons.person_off, color: Colors.white),
                  onTap: () => _showUserReportDialog(context),
                  trailing: trailingArrow,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showReportDialog(BuildContext context, String title, String hint) {
    final _reportController = TextEditingController();
    final _selectedCategory = 'general';

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1A2B4D),
          shape: RoundedSuperellipseBorder(
            borderRadius: BorderRadius.circular(18),
          ),
          title: Text(
            title,
            style: const TextStyle(
              color: Color(0xFF00FFFF),
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _reportController,
                style: const TextStyle(color: Colors.white),
                maxLines: 5,
                decoration: InputDecoration(
                  hintText: hint,
                  hintStyle: TextStyle(
                    color: Colors.white.withValues(alpha: 0.5),
                  ),
                  filled: true,
                  fillColor: Colors.white.withValues(alpha: 0.1),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFF00FFFF)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () {
                if (_reportController.text.trim().isNotEmpty) {
                  // Submit through BLoC
                  final reportBloc = di.sl<ReportBloc>();
                  reportBloc.add(SubmitFeedbackEvent(
                    title: _selectedCategory,
                    description: _reportController.text.trim(),
                  ));

                  // Listen for state changes
                  reportBloc.stream.listen((state) {
                    if (state is ReportSuccess) {
                      if (context.mounted) {
                        CustomToast.showSuccess(context, state.message);
                        Navigator.of(context).pop();
                      }
                    } else if (state is ReportError) {
                      if (context.mounted) {
                        CustomToast.showError(context, state.message);
                      }
                    }
                  });
                } else {
                  // Show message to enter text
                  if (context.mounted) {
                    CustomToast.showError(
                      context,
                      'Please enter your report before submitting',
                    );
                  }
                }
              },
              child: const Text(
                'Submit',
                style: TextStyle(color: Color(0xFF00FFFF)),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showUserReportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        final usernameController = TextEditingController();
        final reasonController = TextEditingController();
        return AlertDialog(
          backgroundColor: const Color(0xFF1A2B4D),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          title: const Text(
            'Report a user',
            style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: usernameController,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Enter username',
                  hintStyle: TextStyle(
                    color: Colors.white.withValues(alpha: 0.5),
                  ),
                  filled: true,
                  fillColor: Colors.white.withValues(alpha: 0.1),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFF00FFFF)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: reasonController,
                style: const TextStyle(color: Colors.white),
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'Reason for reporting',
                  hintStyle: TextStyle(
                    color: Colors.white.withValues(alpha: 0.5),
                  ),
                  filled: true,
                  fillColor: Colors.white.withValues(alpha: 0.1),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFF00FFFF)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () {
                if (usernameController.text.isNotEmpty &&
                    reasonController.text.isNotEmpty) {
                  // TODO: Implement user report submission
                  Navigator.pop(context);
                  CustomToast.showSuccess(
                    context,
                    'User report submitted successfully',
                  );
                }
              },
              child: const Text('Report', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
