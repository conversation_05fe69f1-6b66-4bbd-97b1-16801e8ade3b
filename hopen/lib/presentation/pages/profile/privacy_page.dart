import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../statefulbusinesslogic/bloc/activity_status/activity_status_bloc.dart';
import '../../../statefulbusinesslogic/bloc/activity_status/activity_status_event.dart';
import '../../../statefulbusinesslogic/bloc/activity_status/activity_status_state.dart';
import '../../../statefulbusinesslogic/core/models/activity_status_model.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_bloc.dart';
import '../../../statefulbusinesslogic/bloc/auth/auth_state.dart';
import '../../../statefulbusinesslogic/core/services/activity_status_service.dart';
import '../../widgets/custom_toast.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/keyboard_dismissible.dart';

class PrivacyPage extends StatefulWidget {
  const PrivacyPage({super.key});

  @override
  State<PrivacyPage> createState() => _PrivacyPageState();
}

class _PrivacyPageState extends State<PrivacyPage> {
  bool _isAccountPrivate = false;
  final List<String> _blockedUsers = []; // This would be populated from backend

  @override
  void initState() {
    super.initState();
    // Load activity status settings when page loads
    final authState = context.read<AuthBloc>().state;
    if (authState.status == AuthStatus.authenticated && authState.userId != null) {
      context.read<ActivityStatusBloc>().add(LoadActivityStatusEvent(userId: authState.userId!));
    }
  }

  void _syncActivityStatusWithService(ActivityStatusModel activityStatus) {
    // Update the activity status service with the new settings
    ActivityStatusService.instance.updateActivityStatusSettings(activityStatus);
  }

  @override
  Widget build(BuildContext context) {
    return GradientBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('Privacy'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: SafeArea(
          bottom: false,
          child: KeyboardDismissible(
            child: ListView(
              padding: const EdgeInsets.only(
                bottom: kBottomNavigationBarHeight + 20.0,
              ),
              children: [
                // Account Privacy
                const Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'Account Privacy',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                ),
                SwitchListTile(
                  title: const Text('Private Account'),
                  subtitle: const Text(
                    'When enabled, only your contacts can find you in search',
                  ),
                  value: _isAccountPrivate,
                  onChanged: (value) {
                    setState(() => _isAccountPrivate = value);
                  },
                ),
                const Divider(height: 32),

                // Activity Status
                const Padding(
                  padding: EdgeInsets.all(20),
                  child: Text(
                    'Activity Status',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                ),
                BlocConsumer<ActivityStatusBloc, ActivityStatusState>(
                  listener: (context, state) {
                    if (state is ActivityStatusLoaded) {
                      // Sync with service when loaded
                      _syncActivityStatusWithService(state.activityStatus);
                    } else if (state is ActivityStatusUpdated) {
                      // Update the activity status service
                      _syncActivityStatusWithService(state.activityStatus);

                      // Show success message
                      if (state.message != null) {
                        CustomToast.showSuccess(context, state.message!);
                      }
                    } else if (state is ActivityStatusError) {
                      // Show error message
                      CustomToast.showError(context, state.message);
                    }
                  },
                  builder: (context, state) {
                    bool isLoading = state is ActivityStatusLoading || state is ActivityStatusUpdating;
                    bool currentValue = true; // Default value

                    if (state is ActivityStatusLoaded) {
                      currentValue = state.activityStatus.showActivityStatus;
                    } else if (state is ActivityStatusUpdated) {
                      currentValue = state.activityStatus.showActivityStatus;
                    } else if (state is ActivityStatusUpdating) {
                      currentValue = state.currentStatus.showActivityStatus;
                    }

                    return SwitchListTile(
                      title: const Text('Show Activity Status'),
                      subtitle: const Text("Let others see when you're online"),
                      value: currentValue,
                      onChanged: isLoading ? null : (value) {
                        final authState = context.read<AuthBloc>().state;
                        if (authState.status == AuthStatus.authenticated && authState.userId != null) {
                          context.read<ActivityStatusBloc>().add(
                            UpdateActivityStatusEvent(
                              userId: authState.userId!,
                              showActivityStatus: value,
                            ),
                          );
                        }
                      },
                    );
                  },
                ),
                const Divider(height: 32),

                // Blocked Users
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'Blocked Users',
                        style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                      TextButton.icon(
                        onPressed: _showBlockUserDialog,
                        icon: const Icon(Icons.block),
                        label: const Text('Block User'),
                      ),
                    ],
                  ),
                ),
                if (_blockedUsers.isEmpty)
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    child: Card(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Row(
                          children: [
                            Icon(Icons.info_outline, color: Colors.grey),
                            SizedBox(width: 16),
                            Text(
                              'No blocked users',
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                else
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _blockedUsers.length,
                    itemBuilder: (context, index) => Card(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 4,
                      ),
                      child: ListTile(
                        leading: const CircleAvatar(
                          backgroundColor: Colors.grey,
                          child: Icon(Icons.person, color: Colors.white),
                        ),
                        title: Text(_blockedUsers[index]),
                        subtitle: const Text('Blocked'),
                        trailing: TextButton.icon(
                          onPressed: () {
                            _showUnblockConfirmation(context, index);
                          },
                          icon: const Icon(Icons.remove_circle_outline),
                          label: const Text('Unblock'),
                          style: TextButton.styleFrom(
                            foregroundColor: Colors.red,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showBlockUserDialog() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController();
        return AlertDialog(
          backgroundColor: const Color(0xFF1A2B4D),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          title: const Text(
            'Block user',
            style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                "Blocked users won't be able to:",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '• Send you messages or calls',
                style: TextStyle(color: Colors.white70),
              ),
              const Text(
                '• Add you as a contact',
                style: TextStyle(color: Colors.white70),
              ),
              const Text(
                '• See your activity status',
                style: TextStyle(color: Colors.white70),
              ),
              const Text(
                '• Invite you to bubbles',
                style: TextStyle(color: Colors.white70),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Enter username',
                  hintStyle: TextStyle(
                    color: Colors.white.withValues(alpha: 0.5),
                  ),
                  filled: true,
                  fillColor: Colors.white.withValues(alpha: 0.1),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFF00FFFF)),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  prefixIcon: const Icon(Icons.person, color: Colors.white),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancel',
                style: TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () {
                if (controller.text.isNotEmpty) {
                  setState(() {
                    _blockedUsers.add(controller.text);
                  });
                  Navigator.pop(context);
                  CustomToast.showSuccess(
                    context,
                    '${controller.text} has been blocked',
                  );
                }
              },
              child: const Text('Block', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  void _showUnblockConfirmation(BuildContext context, int index) {
    final username = _blockedUsers[index];
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Unblock User'),
            content: Text('Are you sure you want to unblock $username?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _blockedUsers.removeAt(index);
                  });
                  Navigator.pop(context);
                  CustomToast.showSuccess(
                    context,
                    '$username has been unblocked',
                  );
                },
                child: const Text('Unblock'),
              ),
            ],
          ),
    );
  }
}
