# EMQX Configuration for Hopen Backend
# MQTT5 Server with JWT Authentication via Backend API

## Node Configuration
node {
  name = "emqx@127.0.0.1"
  cookie = "hopen_emqx_cluster"
  data_dir = "data"
}

## Cluster Configuration
cluster {
  name = hopen_cluster
  discovery_strategy = manual
}

## Listeners
listeners.tcp.default {
  bind = "0.0.0.0:1883"
  max_connections = 1024000
  max_conn_rate = 1000
}

listeners.ssl.default {
  bind = "0.0.0.0:8883"
  max_connections = 512000
  max_conn_rate = 500
  ssl_options {
    keyfile = "etc/certs/key.pem"
    certfile = "etc/certs/cert.pem"
    cacertfile = "etc/certs/cacert.pem"
  }
}

listeners.ws.default {
  bind = "0.0.0.0:8083"
  max_connections = 102400
  max_conn_rate = 1000
  websocket.mqtt_path = "/mqtt"
}

listeners.wss.default {
  bind = "0.0.0.0:8084"
  max_connections = 51200
  max_conn_rate = 500
  websocket.mqtt_path = "/mqtt"
  ssl_options {
    keyfile = "etc/certs/key.pem"
    certfile = "etc/certs/cert.pem"
    cacertfile = "etc/certs/cacert.pem"
  }
}

## MQTT v5 Configuration - Optimized for Real-time Communication
mqtt {
  max_packet_size = 2MB
  max_clientid_len = 65535
  max_topic_levels = 128
  max_qos_allowed = 2
  max_topic_alias = 65535
  retain_available = true
  wildcard_subscription = true
  shared_subscription = true
  ignore_loop_deliver = false
  strict_mode = false
  response_information = "hopen-mqtt-v5"

  # Enhanced for real-time signaling
  server_keepalive = 60
  keepalive_multiplier = 1.5
  max_awaiting_rel = 1000
  max_inflight_size = 32

  # WebRTC signaling optimization
  use_username_as_clientid = false
  peer_cert_as_username = false
  peer_cert_as_clientid = false
}

## Authentication Configuration
authentication = [
  {
    mechanism = http
    method = post
    url = "http://localhost:8080/api/v1/auth/mqtt"
    headers {
      "Content-Type" = "application/json"
      "Accept" = "application/json"
    }
    body {
      username = "${username}"
      password = "${password}"
      clientid = "${clientid}"
    }
    pool_size = 8
    connect_timeout = 5s
    request_timeout = 5s
    enable_pipelining = 100
  }
]

## Authorization Configuration
authorization {
  sources = [
    {
      type = http
      method = post
      url = "http://localhost:8080/api/v1/auth/mqtt"
      headers {
        "Content-Type" = "application/json"
        "Accept" = "application/json"
      }
      body {
        username = "${username}"
        topic = "${topic}"
        action = "${action}"
        clientid = "${clientid}"
      }
      pool_size = 8
      connect_timeout = 5s
      request_timeout = 5s
      enable_pipelining = 100
    }
  ]
  no_match = deny
  deny_action = ignore
  cache {
    enable = true
    max_size = 32
    ttl = 1m
  }
}

## Session Configuration - Optimized for Real-time & WebRTC
session {
  max_subscriptions = 0
  upgrade_qos = false
  max_inflight = 64
  retry_interval = 15s
  max_awaiting_rel = 500
  await_rel_timeout = 180s
  session_expiry_interval = 4h
  max_mqueue_len = 2000
  mqueue_priorities = enabled
  mqueue_default_priority = normal
  mqueue_store_qos0 = true

  # Real-time signaling optimizations
  force_gc_policy = 16000
  force_shutdown_policy = 8000
}

## Retainer Configuration
retainer {
  enable = true
  msg_expiry_interval = 0s
  msg_clear_interval = 0s
  max_payload_size = 1MB
  stop_publish_clear_msg = false
  backend {
    type = built_in_database
    storage_type = ram
    max_retained_messages = 0
  }
}

## Shared Subscription Configuration
shared_subscription {
  enable = true
  ack_enabled = true
  ack_timeout = 300s
}

## Topic-specific Configuration for Hopen
topic_metrics {
  enable = true
}

## WebRTC Signaling Topic Optimization
zone.external {
  max_packet_size = 2MB
  max_clientid_len = 65535
  max_topic_levels = 128
  max_qos_allowed = 2
  max_topic_alias = 65535
  retain_available = true
  wildcard_subscription = true
  shared_subscription = true

  # Specific optimizations for call signaling
  server_keepalive = 30
  keepalive_multiplier = 1.2
  max_inflight_size = 64
}

## Overload Protection
overload_protection {
  enable = false
  backoff_delay = 1s
  backoff_gc = 1s
  backoff_hibernation = 1s
  backoff_new_conn = 1s
}

## Force Shutdown Configuration
force_shutdown {
  enable = true
  max_mailbox_size = 1000
  max_heap_size = 32MB
}

## Conn Congestion Configuration
conn_congestion {
  enable_alarm = true
  min_alarm_sustain_duration = 1m
}

## Stats Configuration
stats {
  enable = true
}

## Sys Topics Configuration
sys_topics {
  sys_msg_interval = 1m
  sys_heartbeat_interval = 30s
  sys_event_messages {
    client_connected = true
    client_disconnected = true
    client_subscribed = true
    client_unsubscribed = true
  }
}

## Log Configuration
log {
  console_handler {
    enable = true
    level = info
    formatter = text
    time_offset = system
  }
  
  file_handlers.default {
    enable = true
    level = info
    file = "log/emqx.log"
    rotation {
      enable = true
      size = 50MB
      count = 10
    }
    formatter = text
    time_offset = system
  }
}

## Dashboard Configuration
dashboard {
  listeners.http {
    enable = true
    bind = 18083
    num_acceptors = 4
    max_connections = 512
    backlog = 1024
    send_timeout = 10s
    inet6 = false
    ipv6_v6only = false
  }
  
  default_username = "admin"
  default_password = "public"
  
  cors = true
  
  swagger_support = true
  
  i18n_lang = en
}

## API Configuration
api {
  enable = true
  bind = "0.0.0.0:8081"
  num_acceptors = 4
  max_connections = 512
  backlog = 1024
  send_timeout = 10s
  inet6 = false
  ipv6_v6only = false
}

## Prometheus Configuration
prometheus {
  push_gateway_server = "http://127.0.0.1:9091"
  interval = 15s
  headers {}
  job_name = "${name}/instance/${name}~${host}"
  enable = false
}

## Slow Subscription Configuration
slow_subs {
  enable = true
  threshold = 500ms
  expire_interval = 300s
  top_k_num = 10
  stats_type = whole
}

## Topic Metrics Configuration
topic_metrics {
  enable = false
}

## Alarm Configuration
alarm {
  actions = [log, publish]
  size_limit = 1000
  validity_period = 24h
}

## Telemetry Configuration
telemetry {
  enable = true
}
