package middleware

import (
    "net/http"
    "strings"

    "github.com/gin-gonic/gin"
    "go.uber.org/zap"

    "hopenbackend/pkg/ory"
)

// AuthMiddleware returns a Gin middleware that validates OAuth2 access tokens
// issued by Ory Hydra. It sets "user_id" in the Gin context on success.
func AuthMiddleware(oryClient *ory.Client, logger *zap.Logger) gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
            c.Abort()
            return
        }

        parts := strings.SplitN(authHeader, " ", 2)
        if len(parts) != 2 || parts[0] != "Bearer" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header"})
            c.Abort()
            return
        }

        token := parts[1]
        if token == "" {
            c.<PERSON>(http.StatusUnauthorized, gin.H{"error": "Empty bearer token"})
            c.Abort()
            return
        }

        // Pure Kratos: Only validate session tokens
        tokenPrefix := token
        if len(token) > 20 {
            tokenPrefix = token[:20] + "..."
        }
        logger.Debug("🔍 Validating Kratos session token", zap.String("token_prefix", tokenPrefix))
        session, err := oryClient.ValidateSession(c.Request.Context(), token)
        if err != nil || session == nil || session.Active == nil || !*session.Active {
            logger.Warn("🔍 Kratos session validation failed", zap.Error(err))
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid or inactive session"})
            c.Abort()
            return
        }

        logger.Debug("🔍 Kratos session validation successful", zap.String("user_id", session.Identity.Id))
        c.Set("user_id", session.Identity.Id)
        c.Next()
    }
} 