package sync

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/middleware"
	"hopenbackend/pkg/ory"
)

// Service handles initial data synchronization
type Service struct {
	logger   *zap.Logger
	db       *database.PostgreSQLClient
	arangoDB *database.ArangoDBClient
	config   *config.Config
	ory      *ory.Client
}

// NewService creates a new sync service
func NewService(
	logger *zap.Logger,
	db *database.PostgreSQLClient,
	arangoDB *database.ArangoDBClient,
	config *config.Config,
	ory *ory.Client,
) *Service {
	return &Service{
		logger:   logger.With(zap.String("service", "sync")),
		db:       db,
		arangoDB: arangoDB,
		config:   config,
		ory:      ory,
	}
}

// RegisterRoutes registers the sync service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	router.GET("", s.authMiddleware(), s.syncInitialState)
}

// authMiddleware validates JWT tokens
func (s *Service) authMiddleware() gin.HandlerFunc {
	return middleware.NewAuthMiddleware(s.ory, s.logger)
}

// SyncResponse represents the complete initial state response
type SyncResponse struct {
	UserProfile              *UserProfile              `json:"userProfile"`
	Contacts                 []Contact                 `json:"contacts"`
	Friends                  []Friend                  `json:"friends"`
	PendingContactRequests   []ContactRequest          `json:"pendingContactRequests"`
	PendingFriendRequests    []FriendRequest           `json:"pendingFriendRequests"`
	PendingBubbleRequests    []BubbleRequest           `json:"pendingBubbleRequests"`
	ActiveBubbles            []Bubble                  `json:"activeBubbles"`
	ChatConversations        []ChatConversation        `json:"chatConversations"`
	NotificationSettings     *NotificationSettings     `json:"notificationSettings"`
	SyncTimestamp            time.Time                 `json:"syncTimestamp"`
}

// UserProfile represents user profile data
type UserProfile struct {
	ID                                    string                 `json:"id"`
	Username                              string                 `json:"username"`
	Email                                 string                 `json:"email"`
	FirstName                             string                 `json:"firstName"`
	LastName                              string                 `json:"lastName"`
	AvatarURL                             string                 `json:"avatarUrl,omitempty"`
	Bio                                   string                 `json:"bio,omitempty"`
	DateOfBirth                           *time.Time             `json:"dateOfBirth,omitempty"`
	IsPrivate                             bool                   `json:"isPrivate"`
	NotificationSettings                  map[string]interface{} `json:"notificationSettings,omitempty"`
	PendingSentContactRequestUserIds      []string               `json:"pendingSentContactRequestUserIds,omitempty"`
	PendingSentBubbleStartRequestUserIds  []string               `json:"pendingSentBubbleStartRequestUserIds,omitempty"`
	PendingSentFriendRequestUserIds       []string               `json:"pendingSentFriendRequestUserIds,omitempty"`
}

// Contact represents a contact relationship
type Contact struct {
	ID        string    `json:"id"`
	Username  string    `json:"username"`
	FirstName string    `json:"firstName"`
	LastName  string    `json:"lastName"`
	AvatarURL string    `json:"avatarUrl,omitempty"`
	Status    string    `json:"status"` // "accepted"
	CreatedAt time.Time `json:"createdAt"`
}

// Friend represents a friendship
type Friend struct {
	ID             string    `json:"id"`
	Username       string    `json:"username"`
	FirstName      string    `json:"firstName"`
	LastName       string    `json:"lastName"`
	AvatarURL      string    `json:"avatarUrl,omitempty"`
	SourceBubbleID string    `json:"sourceBubbleId"`
	CreatedAt      time.Time `json:"createdAt"`
}

// ContactRequest represents a pending contact request
type ContactRequest struct {
	ID              string    `json:"id"`
	RequesterID     string    `json:"requesterId"`
	RequesterName   string    `json:"requesterName"`
	RequesterAvatar string    `json:"requesterAvatar,omitempty"`
	RequestType     string    `json:"requestType"` // "received", "sent"
	Status          string    `json:"status"`      // "pending"
	CreatedAt       time.Time `json:"createdAt"`
}

// FriendRequest represents a pending friend request (auto-generated)
type FriendRequest struct {
	ID              string    `json:"id"`
	RequesterID     string    `json:"requesterId"`
	RequesterName   string    `json:"requesterName"`
	RequesterAvatar string    `json:"requesterAvatar,omitempty"`
	SourceBubbleID  string    `json:"sourceBubbleId"`
	Status          string    `json:"status"` // "pending"
	CreatedAt       time.Time `json:"createdAt"`
}

// BubbleRequest represents a pending bubble request
type BubbleRequest struct {
	ID              string    `json:"id"`
	BubbleID        string    `json:"bubbleId,omitempty"`
	RequesterID     string    `json:"requesterId"`
	RequesterName   string    `json:"requesterName"`
	RequesterAvatar string    `json:"requesterAvatar,omitempty"`
	RequestType     string    `json:"requestType"` // "invite", "join", "start"
	Status          string    `json:"status"`      // "pending"
	CreatedAt       time.Time `json:"createdAt"`
}

// Bubble represents an active bubble
type Bubble struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description,omitempty"`
	CreatorID   string    `json:"creatorId"`
	MemberCount int       `json:"memberCount"`
	ExpiresAt   time.Time `json:"expiresAt"`
	CreatedAt   time.Time `json:"createdAt"`
}

// ChatConversation represents a chat conversation
type ChatConversation struct {
	ID            string    `json:"id"`
	Type          string    `json:"type"` // "bubble", "direct"
	Name          string    `json:"name,omitempty"`
	LastMessage   string    `json:"lastMessage,omitempty"`
	LastMessageAt time.Time `json:"lastMessageAt,omitempty"`
	UnreadCount   int       `json:"unreadCount"`
	Participants  []string  `json:"participants"`
}

// NotificationSettings represents user notification preferences
type NotificationSettings struct {
	PushEnabled    bool                   `json:"pushEnabled"`
	EmailEnabled   bool                   `json:"emailEnabled"`
	Preferences    map[string]interface{} `json:"preferences"`
}

// syncInitialState handles the bulk data synchronization endpoint
func (s *Service) syncInitialState(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		s.logger.Error("User ID not found in context")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
		return
	}

	userIDStr := userID.(string)
	s.logger.Info("Starting initial sync", zap.String("user_id", userIDStr))

	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	defer cancel()

	// Use a single database transaction for data consistency
	tx, err := s.db.Pool.Begin(ctx)
	if err != nil {
		s.logger.Error("Failed to begin transaction", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	defer tx.Rollback(ctx)

	response := &SyncResponse{
		SyncTimestamp: time.Now(),
	}

	// Fetch all data in parallel for better performance
	errChan := make(chan error, 8)
	
	// Fetch user profile
	go func() {
		profile, err := s.fetchUserProfile(ctx, userIDStr)
		if err != nil {
			errChan <- fmt.Errorf("failed to fetch user profile: %w", err)
			return
		}
		response.UserProfile = profile
		errChan <- nil
	}()

	// Fetch contacts
	go func() {
		contacts, err := s.fetchContacts(ctx, userIDStr)
		if err != nil {
			errChan <- fmt.Errorf("failed to fetch contacts: %w", err)
			return
		}
		response.Contacts = contacts
		errChan <- nil
	}()

	// Fetch friends
	go func() {
		friends, err := s.fetchFriends(ctx, userIDStr)
		if err != nil {
			errChan <- fmt.Errorf("failed to fetch friends: %w", err)
			return
		}
		response.Friends = friends
		errChan <- nil
	}()

	// Fetch pending requests
	go func() {
		contactReqs, friendReqs, bubbleReqs, err := s.fetchPendingRequests(ctx, userIDStr)
		if err != nil {
			errChan <- fmt.Errorf("failed to fetch pending requests: %w", err)
			return
		}
		response.PendingContactRequests = contactReqs
		response.PendingFriendRequests = friendReqs
		response.PendingBubbleRequests = bubbleReqs
		errChan <- nil
	}()

	// Wait for all goroutines to complete
	for i := 0; i < 4; i++ {
		if err := <-errChan; err != nil {
			s.logger.Error("Sync operation failed", zap.Error(err))
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to sync data"})
			return
		}
	}

	// Commit transaction
	if err := tx.Commit(ctx); err != nil {
		s.logger.Error("Failed to commit transaction", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	s.logger.Info("Initial sync completed successfully", 
		zap.String("user_id", userIDStr),
		zap.Int("contacts", len(response.Contacts)),
		zap.Int("friends", len(response.Friends)),
		zap.Int("pending_contact_requests", len(response.PendingContactRequests)),
		zap.Int("pending_friend_requests", len(response.PendingFriendRequests)),
		zap.Int("pending_bubble_requests", len(response.PendingBubbleRequests)))

	c.JSON(http.StatusOK, response)
}

// fetchUserProfile retrieves the user's profile data
func (s *Service) fetchUserProfile(ctx context.Context, userID string) (*UserProfile, error) {
	query := `
		SELECT id, username, email, first_name, last_name, avatar_url, bio,
		       date_of_birth, is_private, notification_settings
		FROM users WHERE id = $1`

	var profile UserProfile
	var avatarURL, bio *string
	var dateOfBirth *time.Time
	var notificationSettings []byte

	err := s.db.Pool.QueryRow(ctx, query, userID).Scan(
		&profile.ID, &profile.Username, &profile.Email, &profile.FirstName,
		&profile.LastName, &avatarURL, &bio, &dateOfBirth, &profile.IsPrivate,
		&notificationSettings,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch user profile: %w", err)
	}

	if avatarURL != nil {
		profile.AvatarURL = *avatarURL
	}
	if bio != nil {
		profile.Bio = *bio
	}
	if dateOfBirth != nil {
		profile.DateOfBirth = dateOfBirth
	}

	// Parse notification settings
	if len(notificationSettings) > 0 {
		if err := json.Unmarshal(notificationSettings, &profile.NotificationSettings); err != nil {
			s.logger.Warn("Failed to parse notification settings", zap.Error(err))
		}
	}

	// Fetch pending sent request user IDs
	profile.PendingSentContactRequestUserIds, _ = s.fetchPendingSentContactRequestUserIds(ctx, userID)
	profile.PendingSentBubbleStartRequestUserIds, _ = s.fetchPendingSentBubbleStartRequestUserIds(ctx, userID)
	profile.PendingSentFriendRequestUserIds, _ = s.fetchPendingSentFriendRequestUserIds(ctx, userID)

	return &profile, nil
}

// fetchContacts retrieves the user's accepted contacts
func (s *Service) fetchContacts(ctx context.Context, userID string) ([]Contact, error) {
	// This is a simplified version - in reality, you'd need to implement proper ArangoDB queries
	// to get the contact's profile information
	contacts := []Contact{}

	// For now, return empty slice - this would be implemented with proper ArangoDB queries
	return contacts, nil
}

// fetchFriends retrieves the user's friends
func (s *Service) fetchFriends(ctx context.Context, userID string) ([]Friend, error) {
	// This is a simplified version - in reality, you'd need to implement proper ArangoDB queries
	// to join with users table
	friends := []Friend{}

	// For now, return empty slice - this would be implemented with proper ArangoDB queries
	return friends, nil
}

// fetchPendingRequests retrieves all pending requests for the user
func (s *Service) fetchPendingRequests(ctx context.Context, userID string) ([]ContactRequest, []FriendRequest, []BubbleRequest, error) {
	contactReqs := []ContactRequest{}
	friendReqs := []FriendRequest{}
	bubbleReqs := []BubbleRequest{}

	// Fetch pending contact requests
	contactQuery := `
		SELECT cr.id, cr.requester_id, u.first_name || ' ' || u.last_name as requester_name,
		       u.avatar_url, cr.created_at
		FROM contact_requests cr
		JOIN users u ON cr.requester_id = u.id
		WHERE cr.recipient_id = $1 AND cr.status = 'pending'
		ORDER BY cr.created_at DESC`

	rows, err := s.db.Pool.Query(ctx, contactQuery, userID)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to fetch contact requests: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var req ContactRequest
		var avatarURL *string
		err := rows.Scan(&req.ID, &req.RequesterID, &req.RequesterName, &avatarURL, &req.CreatedAt)
		if err != nil {
			continue
		}
		if avatarURL != nil {
			req.RequesterAvatar = *avatarURL
		}
		req.RequestType = "received"
		req.Status = "pending"
		contactReqs = append(contactReqs, req)
	}

	// Fetch pending friend requests (auto-generated from bubble expiry)
	friendQuery := `
		SELECT fr.id, fr.requester_id, u.first_name || ' ' || u.last_name as requester_name,
		       u.avatar_url, fr.source_bubble_id, fr.created_at
		FROM friend_requests fr
		JOIN users u ON fr.requester_id = u.id
		WHERE fr.recipient_id = $1 AND fr.status = 'pending'
		ORDER BY fr.created_at DESC`

	rows, err = s.db.Pool.Query(ctx, friendQuery, userID)
	if err != nil {
		return contactReqs, nil, nil, fmt.Errorf("failed to fetch friend requests: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var req FriendRequest
		var avatarURL *string
		err := rows.Scan(&req.ID, &req.RequesterID, &req.RequesterName, &avatarURL, &req.SourceBubbleID, &req.CreatedAt)
		if err != nil {
			continue
		}
		if avatarURL != nil {
			req.RequesterAvatar = *avatarURL
		}
		req.Status = "pending"
		friendReqs = append(friendReqs, req)
	}

	// Fetch pending bubble requests
	bubbleQuery := `
		SELECT br.id, br.bubble_id, br.requester_id, u.first_name || ' ' || u.last_name as requester_name,
		       u.avatar_url, br.request_type, br.created_at
		FROM bubble_requests br
		JOIN users u ON br.requester_id = u.id
		WHERE br.recipient_id = $1 AND br.status = 'pending'
		ORDER BY br.created_at DESC`

	rows, err = s.db.Pool.Query(ctx, bubbleQuery, userID)
	if err != nil {
		return contactReqs, friendReqs, nil, fmt.Errorf("failed to fetch bubble requests: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var req BubbleRequest
		var avatarURL *string
		var bubbleID *string
		err := rows.Scan(&req.ID, &bubbleID, &req.RequesterID, &req.RequesterName, &avatarURL, &req.RequestType, &req.CreatedAt)
		if err != nil {
			continue
		}
		if avatarURL != nil {
			req.RequesterAvatar = *avatarURL
		}
		if bubbleID != nil {
			req.BubbleID = *bubbleID
		}
		req.Status = "pending"
		bubbleReqs = append(bubbleReqs, req)
	}

	return contactReqs, friendReqs, bubbleReqs, nil
}

// fetchPendingSentContactRequestUserIds retrieves user IDs for pending sent contact requests
func (s *Service) fetchPendingSentContactRequestUserIds(ctx context.Context, userID string) ([]string, error) {
	query := `
		SELECT recipient_id FROM contact_requests
		WHERE requester_id = $1 AND status = 'pending'`

	rows, err := s.db.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var userIds []string
	for rows.Next() {
		var recipientID string
		if err := rows.Scan(&recipientID); err != nil {
			continue
		}
		userIds = append(userIds, recipientID)
	}

	return userIds, nil
}

// fetchPendingSentBubbleStartRequestUserIds retrieves user IDs for pending sent bubble start requests
func (s *Service) fetchPendingSentBubbleStartRequestUserIds(ctx context.Context, userID string) ([]string, error) {
	query := `
		SELECT recipient_id FROM bubble_requests
		WHERE requester_id = $1 AND status = 'pending' AND request_type = 'start'`

	rows, err := s.db.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var userIds []string
	for rows.Next() {
		var recipientID string
		if err := rows.Scan(&recipientID); err != nil {
			continue
		}
		userIds = append(userIds, recipientID)
	}

	return userIds, nil
}

// fetchPendingSentFriendRequestUserIds retrieves user IDs for pending sent friend requests
func (s *Service) fetchPendingSentFriendRequestUserIds(ctx context.Context, userID string) ([]string, error) {
	query := `
		SELECT recipient_id FROM friend_requests
		WHERE requester_id = $1 AND status = 'pending'`

	rows, err := s.db.Pool.Query(ctx, query, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var userIds []string
	for rows.Next() {
		var recipientID string
		if err := rows.Scan(&recipientID); err != nil {
			continue
		}
		userIds = append(userIds, recipientID)
	}

	return userIds, nil
}
