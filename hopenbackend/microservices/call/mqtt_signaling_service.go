package call

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
)

// MqttSignalingService handles WebRTC signaling via MQTT v5
type MqttSignalingService struct {
	logger     *zap.Logger
	config     *config.Config
	mqttClient mqtt.Client
	
	// Active call tracking
	activeCalls map[string]*CallSession
	callsMutex  sync.RWMutex
	
	// Message handlers
	messageHandlers map[string]MessageHandler
	
	// Service state
	isInitialized bool
	ctx           context.Context
	cancel        context.CancelFunc
}

// CallSession represents an active call session
type CallSession struct {
	CallID       string                 `json:"call_id"`
	Participants map[string]*Participant `json:"participants"`
	CreatedAt    time.Time              `json:"created_at"`
	LastActivity time.Time              `json:"last_activity"`
	CallType     string                 `json:"call_type"` // "voice" or "video"
	State        string                 `json:"state"`     // "initiating", "ringing", "active", "ended"
}

// Participant represents a call participant
type Participant struct {
	UserID       string    `json:"user_id"`
	IsInitiator  bool      `json:"is_initiator"`
	JoinedAt     time.Time `json:"joined_at"`
	IsPresent    bool      `json:"is_present"`
	HasVideo     bool      `json:"has_video"`
	HasAudio     bool      `json:"has_audio"`
	LastSeen     time.Time `json:"last_seen"`
}

// SignalingMessage represents a WebRTC signaling message
type SignalingMessage struct {
	Type          string                 `json:"type"`
	CallID        string                 `json:"call_id"`
	FromUserID    string                 `json:"from_user_id"`
	ToUserID      string                 `json:"to_user_id"`
	Data          map[string]interface{} `json:"data"`
	Timestamp     time.Time              `json:"timestamp"`
	CorrelationID string                 `json:"correlation_id,omitempty"`
	UserProperties map[string]string     `json:"user_properties,omitempty"`
}

// MessageHandler defines the signature for message handlers
type MessageHandler func(topic string, message *SignalingMessage) error

// MQTT topic constants
const (
	BaseTopicPrefix    = "hopen/webrtc"
	CallSignalingTopic = BaseTopicPrefix + "/calls"
	CallStateTopic     = BaseTopicPrefix + "/state"
	CallEventsTopic    = BaseTopicPrefix + "/events"
)

// NewMqttSignalingService creates a new MQTT signaling service
func NewMqttSignalingService(logger *zap.Logger, config *config.Config) *MqttSignalingService {
	ctx, cancel := context.WithCancel(context.Background())
	
	service := &MqttSignalingService{
		logger:          logger,
		config:          config,
		activeCalls:     make(map[string]*CallSession),
		messageHandlers: make(map[string]MessageHandler),
		ctx:             ctx,
		cancel:          cancel,
	}
	
	// Register message handlers
	service.registerMessageHandlers()
	
	return service
}

// Initialize initializes the MQTT signaling service
func (s *MqttSignalingService) Initialize() error {
	if s.isInitialized {
		s.logger.Warn("MQTT signaling service already initialized")
		return nil
	}
	
	s.logger.Info("Initializing MQTT WebRTC signaling service")
	
	// Create MQTT client
	if err := s.createMqttClient(); err != nil {
		return fmt.Errorf("failed to create MQTT client: %w", err)
	}
	
	// Connect to MQTT broker
	if err := s.connectMqtt(); err != nil {
		return fmt.Errorf("failed to connect to MQTT broker: %w", err)
	}
	
	// Subscribe to signaling topics
	if err := s.subscribeToTopics(); err != nil {
		return fmt.Errorf("failed to subscribe to topics: %w", err)
	}
	
	// Start background cleanup routine
	go s.cleanupRoutine()
	
	s.isInitialized = true
	s.logger.Info("MQTT WebRTC signaling service initialized successfully")
	
	return nil
}

// createMqttClient creates and configures the MQTT client
func (s *MqttSignalingService) createMqttClient() error {
	clientID := fmt.Sprintf("hopen-webrtc-signaling-%d", time.Now().Unix())
	
	opts := mqtt.NewClientOptions()
	opts.AddBroker(s.config.MQTT.Broker)
	opts.SetClientID(clientID)
	opts.SetUsername(s.config.MQTT.Username)
	opts.SetPassword(s.config.MQTT.Password)
	opts.SetKeepAlive(time.Duration(s.config.MQTT.KeepAlive) * time.Second)
	opts.SetCleanSession(s.config.MQTT.CleanSession)
	opts.SetAutoReconnect(true)
	opts.SetMaxReconnectInterval(30 * time.Second)
	
	// Set connection handlers
	opts.SetConnectionLostHandler(s.onConnectionLost)
	opts.SetReconnectingHandler(s.onReconnecting)
	opts.SetOnConnectHandler(s.onConnected)
	
	s.mqttClient = mqtt.NewClient(opts)
	return nil
}

// connectMqtt connects to the MQTT broker
func (s *MqttSignalingService) connectMqtt() error {
	s.logger.Info("Connecting to MQTT broker", zap.String("broker", s.config.MQTT.Broker))
	
	token := s.mqttClient.Connect()
	if token.Wait() && token.Error() != nil {
		return fmt.Errorf("MQTT connection failed: %w", token.Error())
	}
	
	s.logger.Info("Successfully connected to MQTT broker")
	return nil
}

// subscribeToTopics subscribes to all necessary signaling topics
func (s *MqttSignalingService) subscribeToTopics() error {
	topics := map[string]byte{
		CallSignalingTopic + "/+/+/+": 1, // QoS 1 for reliable delivery
		CallStateTopic + "/+":         1,
		CallEventsTopic + "/+/+":      1,
	}
	
	for topic, qos := range topics {
		token := s.mqttClient.Subscribe(topic, qos, s.messageRouter)
		if token.Wait() && token.Error() != nil {
			return fmt.Errorf("failed to subscribe to topic %s: %w", topic, token.Error())
		}
		s.logger.Info("Subscribed to topic", zap.String("topic", topic))
	}
	
	return nil
}

// messageRouter routes incoming MQTT messages to appropriate handlers
func (s *MqttSignalingService) messageRouter(client mqtt.Client, msg mqtt.Message) {
	topic := msg.Topic()
	payload := msg.Payload()
	
	s.logger.Debug("Received MQTT message", 
		zap.String("topic", topic),
		zap.Int("payload_size", len(payload)))
	
	// Parse signaling message
	var signalingMsg SignalingMessage
	if err := json.Unmarshal(payload, &signalingMsg); err != nil {
		s.logger.Error("Failed to parse signaling message", 
			zap.String("topic", topic),
			zap.Error(err))
		return
	}
	
	// Route to appropriate handler based on message type
	if handler, exists := s.messageHandlers[signalingMsg.Type]; exists {
		if err := handler(topic, &signalingMsg); err != nil {
			s.logger.Error("Message handler failed",
				zap.String("type", signalingMsg.Type),
				zap.String("topic", topic),
				zap.Error(err))
		}
	} else {
		s.logger.Warn("No handler for message type",
			zap.String("type", signalingMsg.Type),
			zap.String("topic", topic))
	}
}

// registerMessageHandlers registers all message handlers
func (s *MqttSignalingService) registerMessageHandlers() {
	s.messageHandlers["call_initiation"] = s.handleCallInitiation
	s.messageHandlers["offer"] = s.handleOffer
	s.messageHandlers["answer"] = s.handleAnswer
	s.messageHandlers["ice_candidate"] = s.handleIceCandidate
	s.messageHandlers["call_end"] = s.handleCallEnd
	s.messageHandlers["participant_join"] = s.handleParticipantJoin
	s.messageHandlers["participant_leave"] = s.handleParticipantLeave
	s.messageHandlers["media_state_change"] = s.handleMediaStateChange
}

// handleCallInitiation handles call initiation messages
func (s *MqttSignalingService) handleCallInitiation(topic string, msg *SignalingMessage) error {
	s.logger.Info("Handling call initiation",
		zap.String("call_id", msg.CallID),
		zap.String("from", msg.FromUserID),
		zap.String("to", msg.ToUserID))
	
	s.callsMutex.Lock()
	defer s.callsMutex.Unlock()
	
	// Create new call session
	session := &CallSession{
		CallID:       msg.CallID,
		Participants: make(map[string]*Participant),
		CreatedAt:    time.Now(),
		LastActivity: time.Now(),
		CallType:     getCallType(msg.Data),
		State:        "initiating",
	}
	
	// Add initiator as participant
	session.Participants[msg.FromUserID] = &Participant{
		UserID:      msg.FromUserID,
		IsInitiator: true,
		JoinedAt:    time.Now(),
		IsPresent:   true,
		HasVideo:    getBoolFromData(msg.Data, "has_video"),
		HasAudio:    getBoolFromData(msg.Data, "has_audio"),
		LastSeen:    time.Now(),
	}
	
	s.activeCalls[msg.CallID] = session
	
	// Publish call state update
	return s.publishCallState(msg.CallID, session)
}

// handleOffer handles WebRTC offer messages
func (s *MqttSignalingService) handleOffer(topic string, msg *SignalingMessage) error {
	s.logger.Debug("Handling WebRTC offer",
		zap.String("call_id", msg.CallID),
		zap.String("from", msg.FromUserID),
		zap.String("to", msg.ToUserID))
	
	// Forward offer to target user
	return s.forwardSignalingMessage(msg)
}

// handleAnswer handles WebRTC answer messages
func (s *MqttSignalingService) handleAnswer(topic string, msg *SignalingMessage) error {
	s.logger.Debug("Handling WebRTC answer",
		zap.String("call_id", msg.CallID),
		zap.String("from", msg.FromUserID),
		zap.String("to", msg.ToUserID))
	
	// Update call state to active
	s.updateCallState(msg.CallID, "active")
	
	// Forward answer to target user
	return s.forwardSignalingMessage(msg)
}

// handleIceCandidate handles ICE candidate messages
func (s *MqttSignalingService) handleIceCandidate(topic string, msg *SignalingMessage) error {
	s.logger.Debug("Handling ICE candidate",
		zap.String("call_id", msg.CallID),
		zap.String("from", msg.FromUserID),
		zap.String("to", msg.ToUserID))
	
	// Forward ICE candidate to target user
	return s.forwardSignalingMessage(msg)
}

// handleCallEnd handles call end messages
func (s *MqttSignalingService) handleCallEnd(topic string, msg *SignalingMessage) error {
	s.logger.Info("Handling call end",
		zap.String("call_id", msg.CallID),
		zap.String("from", msg.FromUserID))
	
	// Update call state and cleanup
	s.updateCallState(msg.CallID, "ended")
	
	// Broadcast call end to all participants
	return s.broadcastToCall(msg.CallID, msg)
}

// forwardSignalingMessage forwards a signaling message to the target user
func (s *MqttSignalingService) forwardSignalingMessage(msg *SignalingMessage) error {
	topic := fmt.Sprintf("%s/%s/%s/%s", CallSignalingTopic, msg.ToUserID, msg.CallID, msg.Type)
	
	payload, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}
	
	token := s.mqttClient.Publish(topic, 1, false, payload)
	if token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to publish message: %w", token.Error())
	}
	
	return nil
}

// broadcastToCall broadcasts a message to all participants in a call
func (s *MqttSignalingService) broadcastToCall(callID string, msg *SignalingMessage) error {
	s.callsMutex.RLock()
	session, exists := s.activeCalls[callID]
	s.callsMutex.RUnlock()
	
	if !exists {
		return fmt.Errorf("call session not found: %s", callID)
	}
	
	for userID := range session.Participants {
		if userID != msg.FromUserID {
			msg.ToUserID = userID
			if err := s.forwardSignalingMessage(msg); err != nil {
				s.logger.Error("Failed to broadcast to participant",
					zap.String("call_id", callID),
					zap.String("user_id", userID),
					zap.Error(err))
			}
		}
	}
	
	return nil
}

// publishCallState publishes call state updates
func (s *MqttSignalingService) publishCallState(callID string, session *CallSession) error {
	topic := fmt.Sprintf("%s/%s", CallStateTopic, callID)
	
	payload, err := json.Marshal(session)
	if err != nil {
		return fmt.Errorf("failed to marshal call state: %w", err)
	}
	
	token := s.mqttClient.Publish(topic, 1, true, payload) // Retained message
	if token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to publish call state: %w", token.Error())
	}
	
	return nil
}

// updateCallState updates the state of a call
func (s *MqttSignalingService) updateCallState(callID, state string) {
	s.callsMutex.Lock()
	defer s.callsMutex.Unlock()
	
	if session, exists := s.activeCalls[callID]; exists {
		session.State = state
		session.LastActivity = time.Now()
		
		// Publish state update
		go func() {
			if err := s.publishCallState(callID, session); err != nil {
				s.logger.Error("Failed to publish call state update",
					zap.String("call_id", callID),
					zap.Error(err))
			}
		}()
	}
}

// Helper functions
func getCallType(data map[string]interface{}) string {
	if callType, ok := data["call_type"].(string); ok {
		return callType
	}
	return "voice"
}

func getBoolFromData(data map[string]interface{}, key string) bool {
	if value, ok := data[key].(bool); ok {
		return value
	}
	return false
}

// Connection event handlers
func (s *MqttSignalingService) onConnectionLost(client mqtt.Client, err error) {
	s.logger.Error("MQTT connection lost", zap.Error(err))
}

func (s *MqttSignalingService) onReconnecting(client mqtt.Client, opts *mqtt.ClientOptions) {
	s.logger.Info("MQTT reconnecting...")
}

func (s *MqttSignalingService) onConnected(client mqtt.Client) {
	s.logger.Info("MQTT connected successfully")
}

// cleanupRoutine performs periodic cleanup of old call sessions
func (s *MqttSignalingService) cleanupRoutine() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			s.cleanupOldCalls()
		case <-s.ctx.Done():
			return
		}
	}
}

// cleanupOldCalls removes old inactive call sessions
func (s *MqttSignalingService) cleanupOldCalls() {
	s.callsMutex.Lock()
	defer s.callsMutex.Unlock()
	
	cutoff := time.Now().Add(-2 * time.Hour)
	
	for callID, session := range s.activeCalls {
		if session.LastActivity.Before(cutoff) || session.State == "ended" {
			delete(s.activeCalls, callID)
			s.logger.Info("Cleaned up old call session", zap.String("call_id", callID))
		}
	}
}

// Shutdown gracefully shuts down the service
func (s *MqttSignalingService) Shutdown() error {
	s.logger.Info("Shutting down MQTT WebRTC signaling service")
	
	s.cancel()
	
	if s.mqttClient != nil && s.mqttClient.IsConnected() {
		s.mqttClient.Disconnect(250)
	}
	
	s.isInitialized = false
	s.logger.Info("MQTT WebRTC signaling service shut down successfully")
	
	return nil
}

// Additional handler implementations would continue here...
func (s *MqttSignalingService) handleParticipantJoin(topic string, msg *SignalingMessage) error {
	// Implementation for participant join
	return nil
}

func (s *MqttSignalingService) handleParticipantLeave(topic string, msg *SignalingMessage) error {
	// Implementation for participant leave
	return nil
}

func (s *MqttSignalingService) handleMediaStateChange(topic string, msg *SignalingMessage) error {
	// Implementation for media state changes
	return nil
}
