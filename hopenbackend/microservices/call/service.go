package call

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"

	authmw "hopenbackend/pkg/middleware"
	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
)

// Service handles call operations using PostgreSQL and MQTT-based WebRTC signaling
type Service struct {
	logger           *zap.Logger
	db               *database.PostgreSQLClient
	config           *config.Config
	rateLimiter      *ratelimit.RateLimiter
	mqttSignaling    *MqttSignalingService // MQTT-based WebRTC signaling
	calls            map[string]*Call      // active calls
	oryClient        *ory.Client           // Ory client for authentication
	oryClient   *ory.Client
}

// Dependencies holds the dependencies for the call service
type Dependencies struct {
	Logger      *zap.Logger
	DB          *database.PostgreSQLClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
}

// New creates a new call service instance
func New(deps *Dependencies) *Service {
	// Initialize MQTT signaling service
	mqttSignaling := NewMqttSignalingService(deps.Logger, deps.Config)

	service := &Service{
		logger:        deps.Logger,
		db:            deps.DB,
		config:        deps.Config,
		rateLimiter:   deps.RateLimiter,
		mqttSignaling: mqttSignaling,
		calls:         make(map[string]*Call),
		oryClient:     deps.OryClient,
	}

	// Initialize MQTT signaling
	if err := mqttSignaling.Initialize(); err != nil {
		deps.Logger.Error("Failed to initialize MQTT signaling", zap.Error(err))
	}

	return service
}

// RegisterRoutes registers the call service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	// Basic call operations
	router.POST("/start", s.authMiddleware(), s.startCall)
	router.POST("/:callId/join", s.authMiddleware(), s.joinCall)
	router.POST("/:callId/leave", s.authMiddleware(), s.leaveCall)
	router.POST("/:callId/end", s.authMiddleware(), s.endCall)
	router.GET("/:callId", s.authMiddleware(), s.getCall)
	router.GET("/:callId/participants", s.authMiddleware(), s.getCallParticipants)
	router.GET("/active", s.authMiddleware(), s.getActiveCalls)

	// MQTT signaling status endpoint (replaces WebSocket)
	router.GET("/signaling/status", s.authMiddleware(), s.getSignalingStatus)

	// Participant controls
	router.POST("/:callId/mute", s.authMiddleware(), s.muteParticipant)
	router.POST("/:callId/unmute", s.authMiddleware(), s.unmuteParticipant)

	// Call recording
	router.POST("/:callId/recording/start", s.authMiddleware(), s.startRecording)
	router.POST("/:callId/recording/stop", s.authMiddleware(), s.stopRecording)
	router.POST("/:callId/recording/pause", s.authMiddleware(), s.pauseRecording)
	router.POST("/:callId/recording/resume", s.authMiddleware(), s.resumeRecording)
	router.GET("/:callId/recording", s.authMiddleware(), s.getRecordingStatus)
	router.GET("/:callId/recording/download", s.authMiddleware(), s.downloadRecording)

	// Advanced group call features
	router.POST("/:callId/layout", s.authMiddleware(), s.setCallLayout)
	router.POST("/:callId/speaker", s.authMiddleware(), s.setActiveSpeaker)
	router.POST("/:callId/presentation/start", s.authMiddleware(), s.startPresentation)
	router.POST("/:callId/presentation/stop", s.authMiddleware(), s.stopPresentation)
	router.POST("/:callId/bandwidth", s.authMiddleware(), s.setBandwidthLimits)
	router.POST("/:callId/quality", s.authMiddleware(), s.setQualityProfiles)
	router.GET("/:callId/analytics", s.authMiddleware(), s.getCallAnalytics)
}

// Call represents a call session
type Call struct {
	ID           string                 `json:"id"`
	BubbleID     string                 `json:"bubble_id"`
	InitiatorID  string                 `json:"initiator_id"`
	CallType     string                 `json:"call_type"` // audio, video, screen_share
	Status       string                 `json:"status"`    // starting, active, ended
	Participants []CallParticipant      `json:"participants"`
	StartedAt    time.Time              `json:"started_at"`
	EndedAt      *time.Time             `json:"ended_at,omitempty"`
	Duration     *int                   `json:"duration,omitempty"` // in seconds
	Metadata     map[string]interface{} `json:"metadata"`
	Recording    *CallRecording         `json:"recording,omitempty"`
	GroupFeatures *GroupCallFeatures    `json:"group_features,omitempty"`
}

// CallRecording represents call recording information
type CallRecording struct {
	ID           string    `json:"id"`
	Status       string    `json:"status"` // recording, paused, stopped, processing, completed, failed
	StartedAt    time.Time `json:"started_at"`
	EndedAt      *time.Time `json:"ended_at,omitempty"`
	Duration     *int      `json:"duration,omitempty"` // in seconds
	FileURL      string    `json:"file_url,omitempty"`
	FileSize     *int64    `json:"file_size,omitempty"` // in bytes
	Format       string    `json:"format"` // webm, mp4, etc.
	Quality      string    `json:"quality"` // low, medium, high
	InitiatedBy  string    `json:"initiated_by"` // user ID who started recording
}

// GroupCallFeatures represents advanced group call features
type GroupCallFeatures struct {
	MaxParticipants    int                    `json:"max_participants"`
	ActiveSpeaker      *string                `json:"active_speaker,omitempty"` // current active speaker user ID
	SpeakerHistory     []SpeakerEvent         `json:"speaker_history"`
	Layout             string                 `json:"layout"` // grid, spotlight, presentation
	PresentationMode   bool                   `json:"presentation_mode"`
	PresenterID        *string                `json:"presenter_id,omitempty"`
	BandwidthLimits    map[string]int         `json:"bandwidth_limits"` // user_id -> max_bitrate
	QualityProfiles    map[string]string      `json:"quality_profiles"` // user_id -> quality_level
	ScreenShareActive  bool                   `json:"screen_share_active"`
	ScreenShareUserID  *string                `json:"screen_share_user_id,omitempty"`
}

// SpeakerEvent represents a speaker change event
type SpeakerEvent struct {
	UserID    string    `json:"user_id"`
	StartedAt time.Time `json:"started_at"`
	EndedAt   *time.Time `json:"ended_at,omitempty"`
	Duration  *int      `json:"duration,omitempty"` // in seconds
}

// CallParticipant represents a participant in a call
type CallParticipant struct {
	UserID     string    `json:"user_id"`
	JoinedAt   time.Time `json:"joined_at"`
	LeftAt     *time.Time `json:"left_at,omitempty"`
	IsMuted    bool      `json:"is_muted"`
	HasVideo   bool      `json:"has_video"`
	IsPresent  bool      `json:"is_present"`
}

// StartCallRequest represents a request to start a call
type StartCallRequest struct {
	BubbleID string `json:"bubble_id" binding:"required"`
	CallType string `json:"call_type" binding:"required"`
}

// JoinCallRequest represents a request to join a call
type JoinCallRequest struct {
	HasVideo bool `json:"has_video"`
	HasAudio bool `json:"has_audio"`
}

// MqttSignalingMessage represents a signaling message sent via MQTT
// This replaces the old WebRTCMessage for MQTT-based signaling
type MqttSignalingMessage struct {
	Type          string                 `json:"type"`
	CallID        string                 `json:"call_id"`
	UserID        string                 `json:"user_id"`
	TargetID      string                 `json:"target_id,omitempty"`
	Data          map[string]interface{} `json:"data"`
	Timestamp     time.Time              `json:"timestamp"`
	CorrelationID string                 `json:"correlation_id,omitempty"`
}

// startCall handles starting a new call
func (s *Service) startCall(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req StartCallRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Rate limiting for call initiation
	allowed, err := s.rateLimiter.AllowSocialOperation(c.Request.Context(), userID.(string), "call_start")
	if err != nil {
		s.logger.Error("Rate limit check failed", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if !allowed {
		c.JSON(http.StatusTooManyRequests, gin.H{"error": "Call initiation rate limit exceeded"})
		return
	}

	// Validate call type
	if req.CallType != "audio" && req.CallType != "video" && req.CallType != "screen_share" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid call type"})
		return
	}

	// Check if there's already an active call for this bubble
	existingCall := s.getActiveBubbleCall(req.BubbleID)
	if existingCall != nil {
		c.JSON(http.StatusConflict, gin.H{
			"error": "Call already active for this bubble",
			"call_id": existingCall.ID,
		})
		return
	}

	// Create new call
	callID := uuid.New().String()
	call := &Call{
		ID:          callID,
		BubbleID:    req.BubbleID,
		InitiatorID: userID.(string),
		CallType:    req.CallType,
		Status:      "starting",
		Participants: []CallParticipant{
			{
				UserID:    userID.(string),
				JoinedAt:  time.Now(),
				IsMuted:   false,
				HasVideo:  req.CallType == "video" || req.CallType == "screen_share",
				IsPresent: true,
			},
		},
		StartedAt: time.Now(),
		Metadata:  make(map[string]interface{}),
	}

	// Save call to database
	if err := s.saveCall(c.Request.Context(), call); err != nil {
		s.logger.Error("Failed to save call", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start call"})
		return
	}

	// Store in memory for active management
	s.calls[callID] = call

	s.logger.Info("Call started successfully", 
		zap.String("call_id", callID),
		zap.String("bubble_id", req.BubbleID),
		zap.String("initiator_id", userID.(string)),
		zap.String("call_type", req.CallType))

	c.JSON(http.StatusCreated, call)
}

// joinCall handles joining an existing call
func (s *Service) joinCall(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	if callID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Call ID is required"})
		return
	}

	var req JoinCallRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get call from memory or database
	call, exists := s.calls[callID]
	if !exists {
		var err error
		call, err = s.getCallFromDB(c.Request.Context(), callID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
			return
		}
		s.calls[callID] = call
	}

	// Check if call is active
	if call.Status != "starting" && call.Status != "active" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Call is not active"})
		return
	}

	// Check if user is already in the call
	for i, participant := range call.Participants {
		if participant.UserID == userID.(string) {
			if participant.IsPresent {
				c.JSON(http.StatusConflict, gin.H{"error": "Already in call"})
				return
			}
			// User rejoining
			call.Participants[i].IsPresent = true
			call.Participants[i].JoinedAt = time.Now()
			call.Participants[i].LeftAt = nil
			break
		}
	}

	// Add new participant if not already in call
	found := false
	for _, participant := range call.Participants {
		if participant.UserID == userID.(string) {
			found = true
			break
		}
	}

	if !found {
		participant := CallParticipant{
			UserID:    userID.(string),
			JoinedAt:  time.Now(),
			IsMuted:   false,
			HasVideo:  req.HasVideo,
			IsPresent: true,
		}
		call.Participants = append(call.Participants, participant)
	}

	// Update call status to active if it was starting
	if call.Status == "starting" {
		call.Status = "active"
	}

	// Update call in database
	if err := s.updateCall(c.Request.Context(), call); err != nil {
		s.logger.Error("Failed to update call", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to join call"})
		return
	}

	s.logger.Info("User joined call successfully", 
		zap.String("call_id", callID),
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, call)
}

// leaveCall handles leaving a call
func (s *Service) leaveCall(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	if callID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Call ID is required"})
		return
	}

	// Get call
	call, exists := s.calls[callID]
	if !exists {
		var err error
		call, err = s.getCallFromDB(c.Request.Context(), callID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
			return
		}
	}

	// Find and update participant
	participantFound := false
	for i, participant := range call.Participants {
		if participant.UserID == userID.(string) && participant.IsPresent {
			now := time.Now()
			call.Participants[i].IsPresent = false
			call.Participants[i].LeftAt = &now
			participantFound = true
			break
		}
	}

	if !participantFound {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Not in call"})
		return
	}

	// Check if call should end (no active participants)
	activeParticipants := 0
	for _, participant := range call.Participants {
		if participant.IsPresent {
			activeParticipants++
		}
	}

	if activeParticipants == 0 {
		// End the call
		now := time.Now()
		call.Status = "ended"
		call.EndedAt = &now
		duration := int(now.Sub(call.StartedAt).Seconds())
		call.Duration = &duration

		// Remove from active calls
		delete(s.calls, callID)
	}

	// Update call in database
	if err := s.updateCall(c.Request.Context(), call); err != nil {
		s.logger.Error("Failed to update call", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to leave call"})
		return
	}

	s.logger.Info("User left call successfully", 
		zap.String("call_id", callID),
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{
		"message": "Left call successfully",
		"call_ended": call.Status == "ended",
	})
}

// endCall handles ending a call (only initiator can end)
func (s *Service) endCall(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	if callID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Call ID is required"})
		return
	}

	// Get call
	call, exists := s.calls[callID]
	if !exists {
		var err error
		call, err = s.getCallFromDB(c.Request.Context(), callID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
			return
		}
	}

	// Check if user is the initiator
	if call.InitiatorID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only call initiator can end the call"})
		return
	}

	// End the call
	now := time.Now()
	call.Status = "ended"
	call.EndedAt = &now
	duration := int(now.Sub(call.StartedAt).Seconds())
	call.Duration = &duration

	// Mark all participants as left
	for i := range call.Participants {
		if call.Participants[i].IsPresent {
			call.Participants[i].IsPresent = false
			call.Participants[i].LeftAt = &now
		}
	}

	// Update call in database
	if err := s.updateCall(c.Request.Context(), call); err != nil {
		s.logger.Error("Failed to update call", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to end call"})
		return
	}

	// Remove from active calls
	delete(s.calls, callID)

	s.logger.Info("Call ended successfully", 
		zap.String("call_id", callID),
		zap.String("initiator_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "Call ended successfully"})
}

// getCall handles getting call information
func (s *Service) getCall(c *gin.Context) {
	callID := c.Param("callId")
	if callID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Call ID is required"})
		return
	}

	// Get call from memory or database
	call, exists := s.calls[callID]
	if !exists {
		var err error
		call, err = s.getCallFromDB(c.Request.Context(), callID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
			return
		}
	}

	c.JSON(http.StatusOK, call)
}

// getCallParticipants handles getting call participants
func (s *Service) getCallParticipants(c *gin.Context) {
	callID := c.Param("callId")
	if callID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Call ID is required"})
		return
	}

	// Get call
	call, exists := s.calls[callID]
	if !exists {
		var err error
		call, err = s.getCallFromDB(c.Request.Context(), callID)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"participants": call.Participants,
		"count":        len(call.Participants),
	})
}

// getActiveCalls handles getting active calls for a user
func (s *Service) getActiveCalls(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var activeCalls []*Call
	for _, call := range s.calls {
		if call.Status == "active" || call.Status == "starting" {
			// Check if user is a participant
			for _, participant := range call.Participants {
				if participant.UserID == userID.(string) && participant.IsPresent {
					activeCalls = append(activeCalls, call)
					break
				}
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"active_calls": activeCalls,
		"count":        len(activeCalls),
	})
}

// muteParticipant handles muting a participant
func (s *Service) muteParticipant(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	var req struct {
		TargetUserID string `json:"target_user_id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// If no target specified, mute self
	if req.TargetUserID == "" {
		req.TargetUserID = userID.(string)
	}

	// Get call
	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	// Check permissions (can only mute self or if initiator)
	if req.TargetUserID != userID.(string) && call.InitiatorID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only mute yourself or if you're the call initiator"})
		return
	}

	// Find and mute participant
	participantFound := false
	for i, participant := range call.Participants {
		if participant.UserID == req.TargetUserID && participant.IsPresent {
			call.Participants[i].IsMuted = true
			participantFound = true
			break
		}
	}

	if !participantFound {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Participant not found in call"})
		return
	}

	s.logger.Info("Participant muted",
		zap.String("call_id", callID),
		zap.String("muted_by", userID.(string)),
		zap.String("muted_user", req.TargetUserID))

	c.JSON(http.StatusOK, gin.H{"message": "Participant muted successfully"})
}

// unmuteParticipant handles unmuting a participant
func (s *Service) unmuteParticipant(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	var req struct {
		TargetUserID string `json:"target_user_id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// If no target specified, unmute self
	if req.TargetUserID == "" {
		req.TargetUserID = userID.(string)
	}

	// Get call
	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	// Check permissions (can only unmute self)
	if req.TargetUserID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Can only unmute yourself"})
		return
	}

	// Find and unmute participant
	participantFound := false
	for i, participant := range call.Participants {
		if participant.UserID == req.TargetUserID && participant.IsPresent {
			call.Participants[i].IsMuted = false
			participantFound = true
			break
		}
	}

	if !participantFound {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Participant not found in call"})
		return
	}

	s.logger.Info("Participant unmuted",
		zap.String("call_id", callID),
		zap.String("user_id", req.TargetUserID))

	c.JSON(http.StatusOK, gin.H{"message": "Participant unmuted successfully"})
}

// getSignalingStatus returns the status of MQTT signaling service
func (s *Service) getSignalingStatus(c *gin.Context) {
	userID, _ := c.Get("user_id")

	status := gin.H{
		"signaling_type": "mqtt",
		"mqtt_connected": s.mqttSignaling != nil && s.mqttSignaling.isInitialized,
		"user_id":        userID,
		"timestamp":      time.Now().UTC(),
		"topics": gin.H{
			"call_signaling": "hopen/webrtc/calls",
			"call_state":     "hopen/webrtc/state",
			"call_events":    "hopen/webrtc/events",
		},
		"instructions": gin.H{
			"message": "WebRTC signaling is now handled via MQTT v5. Use the MQTT client to subscribe to signaling topics.",
			"migration": "WebSocket signaling has been completely replaced with MQTT for better reliability and performance.",
		},
	}

	c.JSON(http.StatusOK, status)
}

// startRecording starts recording a call
func (s *Service) startRecording(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	var req struct {
		Quality string `json:"quality"` // low, medium, high
		Format  string `json:"format"`  // webm, mp4
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if user is in the call
	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	// Check if user is a participant
	isParticipant := false
	for _, participant := range call.Participants {
		if participant.UserID == userID.(string) {
			isParticipant = true
			break
		}
	}
	if !isParticipant {
		c.JSON(http.StatusForbidden, gin.H{"error": "Not a call participant"})
		return
	}

	// Check if recording is already active
	if call.Recording != nil && call.Recording.Status == "recording" {
		c.JSON(http.StatusConflict, gin.H{"error": "Recording already active"})
		return
	}

	// Create recording record
	recordingID := uuid.New().String()
	recording := &CallRecording{
		ID:          recordingID,
		Status:      "recording",
		StartedAt:   time.Now(),
		Format:      req.Format,
		Quality:     req.Quality,
		InitiatedBy: userID.(string),
	}

	call.Recording = recording
	s.calls[callID] = call

	// Broadcast recording started event to all participants via MQTT
	recordingEvent := &MqttSignalingMessage{
		Type:   "recording_started",
		CallID: callID,
		UserID: userID.(string),
		Data: map[string]interface{}{
			"recording_id": recordingID,
			"quality":      req.Quality,
			"format":       req.Format,
		},
	}
	s.broadcastToCall(callID, "", recordingEvent)

	s.logger.Info("Call recording started",
		zap.String("call_id", callID),
		zap.String("recording_id", recordingID),
		zap.String("initiated_by", userID.(string)))

	c.JSON(http.StatusOK, gin.H{
		"message":   "Recording started",
		"recording": recording,
	})
}

// stopRecording stops recording a call
func (s *Service) stopRecording(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	if call.Recording == nil || call.Recording.Status != "recording" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active recording"})
		return
	}

	// Only the initiator or call creator can stop recording
	if call.Recording.InitiatedBy != userID.(string) && call.InitiatorID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Not authorized to stop recording"})
		return
	}

	// Update recording status
	now := time.Now()
	duration := int(now.Sub(call.Recording.StartedAt).Seconds())
	call.Recording.Status = "processing"
	call.Recording.EndedAt = &now
	call.Recording.Duration = &duration

	// Generate file URL (in production, this would be a MinIO/S3 URL)
	call.Recording.FileURL = fmt.Sprintf("/api/v1/calls/%s/recording/download", callID)

	s.calls[callID] = call

	// Broadcast recording stopped event via MQTT
	recordingEvent := &MqttSignalingMessage{
		Type:   "recording_stopped",
		CallID: callID,
		UserID: userID.(string),
		Data: map[string]interface{}{
			"recording_id": call.Recording.ID,
			"duration":     duration,
		},
	}
	s.broadcastToCall(callID, "", recordingEvent)

	s.logger.Info("Call recording stopped",
		zap.String("call_id", callID),
		zap.String("recording_id", call.Recording.ID),
		zap.Int("duration", duration))

	c.JSON(http.StatusOK, gin.H{
		"message":   "Recording stopped",
		"recording": call.Recording,
	})
}

// pauseRecording pauses an active recording
func (s *Service) pauseRecording(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	if call.Recording == nil || call.Recording.Status != "recording" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active recording"})
		return
	}

	if call.Recording.InitiatedBy != userID.(string) && call.InitiatorID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Not authorized to pause recording"})
		return
	}

	call.Recording.Status = "paused"
	s.calls[callID] = call

	// Broadcast recording paused event via MQTT
	recordingEvent := &MqttSignalingMessage{
		Type:   "recording_paused",
		CallID: callID,
		UserID: userID.(string),
		Data: map[string]interface{}{
			"recording_id": call.Recording.ID,
		},
	}
	s.broadcastToCall(callID, "", recordingEvent)

	c.JSON(http.StatusOK, gin.H{
		"message":   "Recording paused",
		"recording": call.Recording,
	})
}

// resumeRecording resumes a paused recording
func (s *Service) resumeRecording(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	if call.Recording == nil || call.Recording.Status != "paused" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No paused recording"})
		return
	}

	if call.Recording.InitiatedBy != userID.(string) && call.InitiatorID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Not authorized to resume recording"})
		return
	}

	call.Recording.Status = "recording"
	s.calls[callID] = call

	// Broadcast recording resumed event via MQTT
	recordingEvent := &MqttSignalingMessage{
		Type:   "recording_resumed",
		CallID: callID,
		UserID: userID.(string),
		Data: map[string]interface{}{
			"recording_id": call.Recording.ID,
		},
	}
	s.broadcastToCall(callID, "", recordingEvent)

	c.JSON(http.StatusOK, gin.H{
		"message":   "Recording resumed",
		"recording": call.Recording,
	})
}

// getRecordingStatus gets the current recording status
func (s *Service) getRecordingStatus(c *gin.Context) {
	callID := c.Param("callId")

	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	if call.Recording == nil {
		c.JSON(http.StatusOK, gin.H{
			"recording": nil,
			"status":    "not_recording",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"recording": call.Recording,
		"status":    call.Recording.Status,
	})
}

// downloadRecording provides download link for completed recording
func (s *Service) downloadRecording(c *gin.Context) {
	callID := c.Param("callId")

	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	if call.Recording == nil || call.Recording.Status != "completed" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Recording not available"})
		return
	}

	// In production, this would generate a signed URL for MinIO/S3
	downloadURL := fmt.Sprintf("https://storage.hopen.app/recordings/%s.%s",
		call.Recording.ID, call.Recording.Format)

	c.JSON(http.StatusOK, gin.H{
		"download_url": downloadURL,
		"expires_at":   time.Now().Add(24 * time.Hour),
		"file_size":    call.Recording.FileSize,
		"format":       call.Recording.Format,
		"duration":     call.Recording.Duration,
	})
}

// setCallLayout sets the layout for a group call
func (s *Service) setCallLayout(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	var req struct {
		Layout string `json:"layout"` // grid, spotlight, presentation
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	// Initialize group features if not exists
	if call.GroupFeatures == nil {
		call.GroupFeatures = &GroupCallFeatures{
			MaxParticipants: 10,
			Layout:          "grid",
			BandwidthLimits: make(map[string]int),
			QualityProfiles: make(map[string]string),
		}
	}

	call.GroupFeatures.Layout = req.Layout
	s.calls[callID] = call

	// Broadcast layout change to all participants via MQTT
	layoutEvent := &MqttSignalingMessage{
		Type:   "layout_changed",
		CallID: callID,
		UserID: userID.(string),
		Data: map[string]interface{}{
			"layout": req.Layout,
		},
	}
	s.broadcastToCall(callID, "", layoutEvent)

	s.logger.Info("Call layout changed",
		zap.String("call_id", callID),
		zap.String("layout", req.Layout),
		zap.String("changed_by", userID.(string)))

	c.JSON(http.StatusOK, gin.H{
		"message": "Layout updated",
		"layout":  req.Layout,
	})
}

// setActiveSpeaker sets the active speaker for spotlight mode
func (s *Service) setActiveSpeaker(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	var req struct {
		SpeakerID string `json:"speaker_id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	if call.GroupFeatures == nil {
		call.GroupFeatures = &GroupCallFeatures{
			MaxParticipants: 10,
			Layout:          "grid",
			BandwidthLimits: make(map[string]int),
			QualityProfiles: make(map[string]string),
		}
	}

	// End previous speaker session
	if call.GroupFeatures.ActiveSpeaker != nil {
		now := time.Now()
		for i := range call.GroupFeatures.SpeakerHistory {
			if call.GroupFeatures.SpeakerHistory[i].UserID == *call.GroupFeatures.ActiveSpeaker &&
			   call.GroupFeatures.SpeakerHistory[i].EndedAt == nil {
				duration := int(now.Sub(call.GroupFeatures.SpeakerHistory[i].StartedAt).Seconds())
				call.GroupFeatures.SpeakerHistory[i].EndedAt = &now
				call.GroupFeatures.SpeakerHistory[i].Duration = &duration
				break
			}
		}
	}

	// Set new active speaker
	call.GroupFeatures.ActiveSpeaker = &req.SpeakerID

	// Add to speaker history
	speakerEvent := SpeakerEvent{
		UserID:    req.SpeakerID,
		StartedAt: time.Now(),
	}
	call.GroupFeatures.SpeakerHistory = append(call.GroupFeatures.SpeakerHistory, speakerEvent)

	s.calls[callID] = call

	// Broadcast active speaker change via MQTT
	speakerMsg := &MqttSignalingMessage{
		Type:   "active_speaker_changed",
		CallID: callID,
		UserID: userID.(string),
		Data: map[string]interface{}{
			"active_speaker": req.SpeakerID,
		},
	}
	s.broadcastToCall(callID, "", speakerMsg)

	c.JSON(http.StatusOK, gin.H{
		"message":        "Active speaker updated",
		"active_speaker": req.SpeakerID,
	})
}

// startPresentation starts presentation mode
func (s *Service) startPresentation(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	if call.GroupFeatures == nil {
		call.GroupFeatures = &GroupCallFeatures{
			MaxParticipants: 10,
			Layout:          "grid",
			BandwidthLimits: make(map[string]int),
			QualityProfiles: make(map[string]string),
		}
	}

	if call.GroupFeatures.PresentationMode {
		c.JSON(http.StatusConflict, gin.H{"error": "Presentation already active"})
		return
	}

	call.GroupFeatures.PresentationMode = true
	userIDStr := userID.(string)
	call.GroupFeatures.PresenterID = &userIDStr
	call.GroupFeatures.Layout = "presentation"
	s.calls[callID] = call

	// Broadcast presentation started via MQTT
	presentationEvent := &MqttSignalingMessage{
		Type:   "presentation_started",
		CallID: callID,
		UserID: userID.(string),
		Data: map[string]interface{}{
			"presenter_id": userID.(string),
		},
	}
	s.broadcastToCall(callID, "", presentationEvent)

	c.JSON(http.StatusOK, gin.H{
		"message":      "Presentation started",
		"presenter_id": userID.(string),
	})
}

// stopPresentation stops presentation mode
func (s *Service) stopPresentation(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	if call.GroupFeatures == nil || !call.GroupFeatures.PresentationMode {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No active presentation"})
		return
	}

	// Only presenter or call initiator can stop presentation
	if call.GroupFeatures.PresenterID == nil ||
	   (*call.GroupFeatures.PresenterID != userID.(string) && call.InitiatorID != userID.(string)) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Not authorized to stop presentation"})
		return
	}

	call.GroupFeatures.PresentationMode = false
	call.GroupFeatures.PresenterID = nil
	call.GroupFeatures.Layout = "grid"
	s.calls[callID] = call

	// Broadcast presentation stopped via MQTT
	presentationEvent := &MqttSignalingMessage{
		Type:   "presentation_stopped",
		CallID: callID,
		UserID: userID.(string),
		Data:   map[string]interface{}{},
	}
	s.broadcastToCall(callID, "", presentationEvent)

	c.JSON(http.StatusOK, gin.H{
		"message": "Presentation stopped",
	})
}

// setBandwidthLimits sets bandwidth limits for participants
func (s *Service) setBandwidthLimits(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	var req struct {
		Limits map[string]int `json:"limits"` // user_id -> max_bitrate_kbps
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	// Only call initiator can set bandwidth limits
	if call.InitiatorID != userID.(string) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Only call initiator can set bandwidth limits"})
		return
	}

	if call.GroupFeatures == nil {
		call.GroupFeatures = &GroupCallFeatures{
			MaxParticipants: 10,
			Layout:          "grid",
			BandwidthLimits: make(map[string]int),
			QualityProfiles: make(map[string]string),
		}
	}

	call.GroupFeatures.BandwidthLimits = req.Limits
	s.calls[callID] = call

	// Broadcast bandwidth limits update via MQTT
	bandwidthEvent := &MqttSignalingMessage{
		Type:   "bandwidth_limits_updated",
		CallID: callID,
		UserID: userID.(string),
		Data: map[string]interface{}{
			"limits": req.Limits,
		},
	}
	s.broadcastToCall(callID, "", bandwidthEvent)

	c.JSON(http.StatusOK, gin.H{
		"message": "Bandwidth limits updated",
		"limits":  req.Limits,
	})
}

// setQualityProfiles sets quality profiles for participants
func (s *Service) setQualityProfiles(c *gin.Context) {
	userID, _ := c.Get("user_id")
	callID := c.Param("callId")

	var req struct {
		Profiles map[string]string `json:"profiles"` // user_id -> quality_level (low, medium, high)
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	if call.GroupFeatures == nil {
		call.GroupFeatures = &GroupCallFeatures{
			MaxParticipants: 10,
			Layout:          "grid",
			BandwidthLimits: make(map[string]int),
			QualityProfiles: make(map[string]string),
		}
	}

	call.GroupFeatures.QualityProfiles = req.Profiles
	s.calls[callID] = call

	// Broadcast quality profiles update via MQTT
	qualityEvent := &MqttSignalingMessage{
		Type:   "quality_profiles_updated",
		CallID: callID,
		UserID: userID.(string),
		Data: map[string]interface{}{
			"profiles": req.Profiles,
		},
	}
	s.broadcastToCall(callID, "", qualityEvent)

	c.JSON(http.StatusOK, gin.H{
		"message":  "Quality profiles updated",
		"profiles": req.Profiles,
	})
}

// getCallAnalytics provides detailed call analytics
func (s *Service) getCallAnalytics(c *gin.Context) {
	callID := c.Param("callId")

	call, exists := s.calls[callID]
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{"error": "Call not found"})
		return
	}

	// Calculate analytics
	analytics := map[string]interface{}{
		"call_id":           callID,
		"duration":          time.Since(call.StartedAt).Seconds(),
		"participant_count": len(call.Participants),
		"active_participants": func() int {
			count := 0
			for _, p := range call.Participants {
				if p.IsPresent {
					count++
				}
			}
			return count
		}(),
		"call_type":    call.CallType,
		"call_status":  call.Status,
		"recording":    call.Recording != nil,
		"group_features": call.GroupFeatures,
	}

	// Add speaker analytics if available
	if call.GroupFeatures != nil && len(call.GroupFeatures.SpeakerHistory) > 0 {
		speakerStats := make(map[string]interface{})
		totalSpeakingTime := 0

		for _, speaker := range call.GroupFeatures.SpeakerHistory {
			duration := 0
			if speaker.Duration != nil {
				duration = *speaker.Duration
			} else if speaker.EndedAt == nil {
				// Currently speaking
				duration = int(time.Since(speaker.StartedAt).Seconds())
			}

			if existing, ok := speakerStats[speaker.UserID]; ok {
				speakerStats[speaker.UserID] = existing.(int) + duration
			} else {
				speakerStats[speaker.UserID] = duration
			}
			totalSpeakingTime += duration
		}

		analytics["speaker_stats"] = speakerStats
		analytics["total_speaking_time"] = totalSpeakingTime
	}

	// Add recording analytics if available
	if call.Recording != nil {
		recordingAnalytics := map[string]interface{}{
			"status":       call.Recording.Status,
			"duration":     call.Recording.Duration,
			"file_size":    call.Recording.FileSize,
			"format":       call.Recording.Format,
			"quality":      call.Recording.Quality,
			"initiated_by": call.Recording.InitiatedBy,
		}
		analytics["recording_analytics"] = recordingAnalytics
	}

	c.JSON(http.StatusOK, gin.H{
		"analytics": analytics,
	})
}

// Note: WebRTC signaling is now handled entirely by MqttSignalingService
// The old handleWebRTCMessage function has been removed as all signaling
// now goes through MQTT v5 for better reliability and performance.

// handleScreenShareEvent handles screen sharing events
func (s *Service) handleScreenShareEvent(callID, userID string, msg *MqttSignalingMessage) {
	call, exists := s.calls[callID]
	if !exists {
		return
	}

	if call.GroupFeatures == nil {
		call.GroupFeatures = &GroupCallFeatures{
			MaxParticipants: 10,
			Layout:          "grid",
			BandwidthLimits: make(map[string]int),
			QualityProfiles: make(map[string]string),
		}
	}

	switch msg.Type {
	case "screen_share_started":
		call.GroupFeatures.ScreenShareActive = true
		call.GroupFeatures.ScreenShareUserID = &userID
		call.GroupFeatures.Layout = "presentation"
	case "screen_share_stopped":
		call.GroupFeatures.ScreenShareActive = false
		call.GroupFeatures.ScreenShareUserID = nil
		call.GroupFeatures.Layout = "grid"
	}

	s.calls[callID] = call
	s.broadcastToCall(callID, userID, msg)
}

// handleSpeakerActivity handles speaker detection and activity
func (s *Service) handleSpeakerActivity(callID, userID string, msg *MqttSignalingMessage) {
	call, exists := s.calls[callID]
	if !exists {
		return
	}

	if call.GroupFeatures == nil {
		call.GroupFeatures = &GroupCallFeatures{
			MaxParticipants: 10,
			Layout:          "grid",
			BandwidthLimits: make(map[string]int),
			QualityProfiles: make(map[string]string),
		}
	}

	// Auto-detect active speaker based on audio activity
	if data, ok := msg.Data.(map[string]interface{}); ok {
		if speaking, ok := data["speaking"].(bool); ok && speaking {
			// User started speaking
			if call.GroupFeatures.ActiveSpeaker == nil || *call.GroupFeatures.ActiveSpeaker != userID {
				// End previous speaker session
				if call.GroupFeatures.ActiveSpeaker != nil {
					now := time.Now()
					for i := range call.GroupFeatures.SpeakerHistory {
						if call.GroupFeatures.SpeakerHistory[i].UserID == *call.GroupFeatures.ActiveSpeaker &&
						   call.GroupFeatures.SpeakerHistory[i].EndedAt == nil {
							duration := int(now.Sub(call.GroupFeatures.SpeakerHistory[i].StartedAt).Seconds())
							call.GroupFeatures.SpeakerHistory[i].EndedAt = &now
							call.GroupFeatures.SpeakerHistory[i].Duration = &duration
							break
						}
					}
				}

				// Set new active speaker
				call.GroupFeatures.ActiveSpeaker = &userID
				speakerEvent := SpeakerEvent{
					UserID:    userID,
					StartedAt: time.Now(),
				}
				call.GroupFeatures.SpeakerHistory = append(call.GroupFeatures.SpeakerHistory, speakerEvent)

				s.calls[callID] = call

				// Broadcast active speaker change via MQTT
				speakerMsg := &MqttSignalingMessage{
					Type:   "active_speaker_changed",
					CallID: callID,
					UserID: userID,
					Data: map[string]interface{}{
						"active_speaker": userID,
					},
				}
				s.broadcastToCall(callID, "", speakerMsg)
			}
		}
	}
}

// handleQualityUpdate handles quality metrics updates
func (s *Service) handleQualityUpdate(callID, userID string, msg *MqttSignalingMessage) {
	// Log quality metrics for analytics
	if data, ok := msg.Data.(map[string]interface{}); ok {
		s.logger.Debug("Quality update received",
			zap.String("call_id", callID),
			zap.String("user_id", userID),
			zap.Any("metrics", data))
	}

	// Forward quality updates to other participants for adaptive streaming
	s.broadcastToCall(callID, userID, msg)
}

// handleBandwidthRequest handles bandwidth adaptation requests
func (s *Service) handleBandwidthRequest(callID, userID string, msg *MqttSignalingMessage) {
	call, exists := s.calls[callID]
	if !exists {
		return
	}

	if call.GroupFeatures != nil && call.GroupFeatures.BandwidthLimits != nil {
		if limit, exists := call.GroupFeatures.BandwidthLimits[userID]; exists {
			// Send bandwidth limit response via MQTT
			response := &MqttSignalingMessage{
				Type:     "bandwidth_limit",
				CallID:   callID,
				UserID:   "system",
				TargetID: userID,
				Data: map[string]interface{}{
					"max_bitrate": limit,
				},
			}

			// Send directly to the requesting user via MQTT
			if err := s.publishMqttMessage(response); err != nil {
				s.logger.Error("Failed to send bandwidth limit response",
					zap.String("call_id", callID),
					zap.String("user_id", userID),
					zap.Error(err))
			}
		}
	}
}

// publishMqttMessage publishes a signaling message via MQTT
func (s *Service) publishMqttMessage(msg *MqttSignalingMessage) error {
	if s.mqttSignaling == nil {
		return fmt.Errorf("MQTT signaling service not initialized")
	}

	// Set timestamp if not already set
	if msg.Timestamp.IsZero() {
		msg.Timestamp = time.Now()
	}

	// Generate correlation ID if not set
	if msg.CorrelationID == "" {
		msg.CorrelationID = fmt.Sprintf("%d_%s", time.Now().UnixNano(), msg.UserID)
	}

	// Convert to SignalingMessage format expected by MQTT service
	signalingMsg := &SignalingMessage{
		Type:          msg.Type,
		CallID:        msg.CallID,
		FromUserID:    msg.UserID,
		ToUserID:      msg.TargetID,
		Data:          msg.Data,
		Timestamp:     msg.Timestamp,
		CorrelationID: msg.CorrelationID,
	}

	// Use the MQTT signaling service to forward the message
	return s.mqttSignaling.forwardSignalingMessage(signalingMsg)
}

// broadcastToCall broadcasts a message to all participants in a call via MQTT
func (s *Service) broadcastToCall(callID, excludeUserID string, msg *MqttSignalingMessage) {
	call, exists := s.calls[callID]
	if !exists {
		return
	}

	// Broadcast to all participants via MQTT
	for _, participant := range call.Participants {
		if participant.IsPresent && participant.UserID != excludeUserID {
			// Create a copy of the message for each participant
			participantMsg := *msg
			participantMsg.TargetID = participant.UserID

			if err := s.publishMqttMessage(&participantMsg); err != nil {
				s.logger.Error("Failed to send MQTT signaling message",
					zap.String("call_id", callID),
					zap.String("user_id", participant.UserID),
					zap.String("message_type", msg.Type),
					zap.Error(err))
			}
		}
	}
}

// Helper methods

// getActiveBubbleCall gets active call for a bubble
func (s *Service) getActiveBubbleCall(bubbleID string) *Call {
	for _, call := range s.calls {
		if call.BubbleID == bubbleID && (call.Status == "active" || call.Status == "starting") {
			return call
		}
	}
	return nil
}

// saveCall saves a call to the database
func (s *Service) saveCall(ctx context.Context, call *Call) error {
	query := `
		INSERT INTO calls (id, bubble_id, initiator_id, call_type, status,
						  participants, started_at, ended_at, duration, metadata)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`

	participantsJSON, _ := json.Marshal(call.Participants)
	metadataJSON, _ := json.Marshal(call.Metadata)

	_, err := s.db.Pool.Exec(ctx, query,
		call.ID, call.BubbleID, call.InitiatorID, call.CallType, call.Status,
		participantsJSON, call.StartedAt, call.EndedAt, call.Duration, metadataJSON,
	)

	return err
}

// updateCall updates a call in the database
func (s *Service) updateCall(ctx context.Context, call *Call) error {
	query := `
		UPDATE calls
		SET status = $2, participants = $3, ended_at = $4, duration = $5, metadata = $6
		WHERE id = $1`

	participantsJSON, _ := json.Marshal(call.Participants)
	metadataJSON, _ := json.Marshal(call.Metadata)

	_, err := s.db.Pool.Exec(ctx, query,
		call.ID, call.Status, participantsJSON, call.EndedAt, call.Duration, metadataJSON,
	)

	return err
}

// getCallFromDB retrieves a call from the database
func (s *Service) getCallFromDB(ctx context.Context, callID string) (*Call, error) {
	query := `
		SELECT id, bubble_id, initiator_id, call_type, status, participants,
			   started_at, ended_at, duration, metadata
		FROM calls
		WHERE id = $1`

	var call Call
	var participantsJSON, metadataJSON []byte

	err := s.db.Pool.QueryRow(ctx, query, callID).Scan(
		&call.ID, &call.BubbleID, &call.InitiatorID, &call.CallType, &call.Status,
		&participantsJSON, &call.StartedAt, &call.EndedAt, &call.Duration, &metadataJSON,
	)

	if err != nil {
		return nil, err
	}

	json.Unmarshal(participantsJSON, &call.Participants)
	json.Unmarshal(metadataJSON, &call.Metadata)

	return &call, nil
}

// authMiddleware provides authentication middleware
func (s *Service) authMiddleware() gin.HandlerFunc {
	return authmw.NewAuthMiddleware(s.oryClient, s.logger)
}
