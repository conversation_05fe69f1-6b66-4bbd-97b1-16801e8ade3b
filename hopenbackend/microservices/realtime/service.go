package realtime

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"hopenbackend/pkg/config"
	"hopenbackend/pkg/database"
	authmw "hopenbackend/pkg/middleware"
	"hopenbackend/pkg/ory"
	"hopenbackend/pkg/ratelimit"
)

// Service handles real-time messaging using Cassandra + MQTT v5
type Service struct {
	logger      *zap.Logger
	cassandra   *database.CassandraClient
	db          *database.PostgreSQLClient
	config      *config.Config
	rateLimiter *ratelimit.RateLimiter
	mqttClient  mqtt.Client
	oryClient   *ory.Client
}

// Dependencies holds the dependencies for the realtime service
type Dependencies struct {
	Logger      *zap.Logger
	Cassandra   *database.CassandraClient
	DB          *database.PostgreSQLClient
	Config      *config.Config
	RateLimiter *ratelimit.RateLimiter
	OryClient   *ory.Client
}

// New creates a new realtime service instance
func New(deps *Dependencies) *Service {
	service := &Service{
		logger:      deps.Logger,
		cassandra:   deps.Cassandra,
		db:          deps.DB,
		config:      deps.Config,
		rateLimiter: deps.RateLimiter,
		mqttClient:  nil,
		oryClient:   deps.OryClient,
	}

	// Initialize MQTT client for pure MQTT-based real-time messaging
	service.initMQTTClient()

	return service
}

// RegisterRoutes registers the realtime service routes
func (s *Service) RegisterRoutes(router *gin.RouterGroup) {
	// MQTT status endpoint
	router.GET("/mqtt/status", s.authMiddleware(), s.getMqttStatus)
	router.POST("/messages", s.authMiddleware(), s.sendMessage)
	router.GET("/messages/:bubbleId", s.authMiddleware(), s.getMessages)
	router.PUT("/messages/:messageId", s.authMiddleware(), s.editMessage)
	router.DELETE("/messages/:messageId", s.authMiddleware(), s.deleteMessage)
	router.POST("/typing", s.authMiddleware(), s.sendTypingIndicator)
	router.GET("/conversations", s.authMiddleware(), s.getConversations)
	router.POST("/conversations/:conversationId/messages", s.authMiddleware(), s.sendDirectMessage)
	router.GET("/conversations/:conversationId/messages", s.authMiddleware(), s.getConversationMessages)
}

// Message represents a chat message
type Message struct {
	MessageID   string    `json:"message_id"`
	BubbleID    string    `json:"bubble_id"`
	SenderID    string    `json:"sender_id"`
	Content     string    `json:"content"`
	MessageType string    `json:"message_type"` // text, image, video, audio, file
	MediaURL    *string   `json:"media_url,omitempty"`
	ReplyToID   *string   `json:"reply_to_id,omitempty"`
	IsEdited    bool      `json:"is_edited"`
	IsDeleted   bool      `json:"is_deleted"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ConversationMessage represents a direct message
type ConversationMessage struct {
	MessageID      string    `json:"message_id"`
	ConversationID string    `json:"conversation_id"`
	SenderID       string    `json:"sender_id"`
	RecipientID    string    `json:"recipient_id"`
	Content        string    `json:"content"`
	MessageType    string    `json:"message_type"`
	MediaURL       *string   `json:"media_url,omitempty"`
	ReplyToID      *string   `json:"reply_to_id,omitempty"`
	IsEdited       bool      `json:"is_edited"`
	IsDeleted      bool      `json:"is_deleted"`
	IsRead         bool      `json:"is_read"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

// SendMessageRequest represents a message sending request
type SendMessageRequest struct {
	BubbleID    string  `json:"bubble_id" binding:"required"`
	Content     string  `json:"content" binding:"required"`
	MessageType string  `json:"message_type"`
	MediaURL    *string `json:"media_url"`
	ReplyToID   *string `json:"reply_to_id"`
}

// TypingIndicator represents a typing indicator
type TypingIndicator struct {
	BubbleID string `json:"bubble_id"`
	UserID   string `json:"user_id"`
	IsTyping bool   `json:"is_typing"`
}

// MqttRealtimeMessage represents an MQTT real-time message
// This replaces WebSocketMessage for pure MQTT-based messaging
type MqttRealtimeMessage struct {
	Type      string                 `json:"type"`
	Payload   map[string]interface{} `json:"payload"`
	Timestamp time.Time              `json:"timestamp"`
	UserID    string                 `json:"user_id,omitempty"`
}

// initMQTTClient initializes the MQTT client for real-time messaging
func (s *Service) initMQTTClient() {
	opts := mqtt.NewClientOptions()
	opts.AddBroker(s.config.MQTT.Broker)
	opts.SetClientID(s.config.MQTT.ClientID)
	opts.SetUsername(s.config.MQTT.Username)
	opts.SetPassword(s.config.MQTT.Password)
	opts.SetKeepAlive(time.Duration(s.config.MQTT.KeepAlive) * time.Second)
	opts.SetCleanSession(s.config.MQTT.CleanSession)

	opts.SetOnConnectHandler(func(client mqtt.Client) {
		s.logger.Info("Connected to MQTT broker")
	})

	opts.SetConnectionLostHandler(func(client mqtt.Client, err error) {
		s.logger.Error("Lost connection to MQTT broker", zap.Error(err))
	})

	s.mqttClient = mqtt.NewClient(opts)
	if token := s.mqttClient.Connect(); token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to connect to MQTT broker", zap.Error(token.Error()))
	}
}

// getMqttStatus returns the status of MQTT real-time messaging
func (s *Service) getMqttStatus(c *gin.Context) {
	userID, _ := c.Get("user_id")

	status := gin.H{
		"messaging_type": "mqtt",
		"mqtt_connected": s.mqttClient != nil && s.mqttClient.IsConnected(),
		"user_id":        userID,
		"timestamp":      time.Now().UTC(),
		"topics": gin.H{
			"messages":        "bubbles/{bubble_id}/messages",
			"typing":          "bubbles/{bubble_id}/typing",
			"direct_messages": "users/{user_id}/messages",
			"presence":        "users/{user_id}/presence",
		},
		"instructions": gin.H{
			"message":   "Real-time messaging is now handled via MQTT v5. Use the MQTT client to subscribe to messaging topics.",
			"migration": "WebSocket messaging has been completely replaced with MQTT for better reliability and performance.",
		},
	}

	c.JSON(http.StatusOK, status)
}

// Note: Real-time messaging is now handled entirely via MQTT v5
// now goes through MQTT for better reliability and performance.

// sendMessage handles sending a message to a bubble
func (s *Service) sendMessage(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req SendMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Rate limiting for message sending
	allowed, err := s.rateLimiter.AllowSocialOperation(c.Request.Context(), userID.(string), "message_send")
	if err != nil {
		s.logger.Error("Rate limit check failed", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if !allowed {
		c.JSON(http.StatusTooManyRequests, gin.H{"error": "Message sending rate limit exceeded"})
		return
	}

	// Validate message content
	if len(req.Content) > s.config.Social.Chat.MaxMessageLength {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Message content too long"})
		return
	}

	// Set default message type
	if req.MessageType == "" {
		req.MessageType = "text"
	}

	// Create message
	message := &Message{
		MessageID:   uuid.New().String(),
		BubbleID:    req.BubbleID,
		SenderID:    userID.(string),
		Content:     req.Content,
		MessageType: req.MessageType,
		MediaURL:    req.MediaURL,
		ReplyToID:   req.ReplyToID,
		IsEdited:    false,
		IsDeleted:   false,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Save message to Cassandra
	cassandraMsg := &database.Message{
		BubbleID:    message.BubbleID,
		MessageID:   message.MessageID,
		SenderID:    message.SenderID,
		Content:     message.Content,
		MessageType: message.MessageType,
		MediaURL:    message.MediaURL,
		ReplyToID:   message.ReplyToID,
		IsEdited:    message.IsEdited,
		IsDeleted:   message.IsDeleted,
		CreatedAt:   message.CreatedAt,
		UpdatedAt:   message.UpdatedAt,
	}

	if err := s.cassandra.CreateMessage(c.Request.Context(), cassandraMsg); err != nil {
		s.logger.Error("Failed to save message to Cassandra", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send message"})
		return
	}

	// Publish message via MQTT
	s.publishMessage(message)

	s.logger.Info("Message sent successfully",
		zap.String("message_id", message.MessageID),
		zap.String("bubble_id", req.BubbleID),
		zap.String("sender_id", userID.(string)))

	c.JSON(http.StatusCreated, message)
}

// getMessages handles getting messages for a bubble with pagination
func (s *Service) getMessages(c *gin.Context) {
	bubbleID := c.Param("bubbleId")
	if bubbleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Bubble ID is required"})
		return
	}

	// Parse pagination parameters
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	pageState := c.Query("page_state")

	if limit > 100 {
		limit = 100 // Cap at 100 messages per request
	}

	// Get messages from Cassandra
	messages, nextPageState, err := s.cassandra.GetMessages(c.Request.Context(), bubbleID, limit, []byte(pageState))
	if err != nil {
		s.logger.Error("Failed to get messages", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get messages"})
		return
	}

	// Convert to API format
	apiMessages := make([]*Message, len(messages))
	for i, msg := range messages {
		apiMessages[i] = &Message{
			MessageID:   msg.MessageID,
			BubbleID:    msg.BubbleID,
			SenderID:    msg.SenderID,
			Content:     msg.Content,
			MessageType: msg.MessageType,
			MediaURL:    msg.MediaURL,
			ReplyToID:   msg.ReplyToID,
			IsEdited:    msg.IsEdited,
			IsDeleted:   msg.IsDeleted,
			CreatedAt:   msg.CreatedAt,
			UpdatedAt:   msg.UpdatedAt,
		}
	}

	response := gin.H{
		"messages": apiMessages,
		"count":    len(apiMessages),
	}

	if len(nextPageState) > 0 {
		response["next_page_state"] = string(nextPageState)
	}

	c.JSON(http.StatusOK, response)
}

// verifyMessageOwnership checks if a user owns a message or has permission to modify it
func (s *Service) verifyMessageOwnership(ctx context.Context, userID, messageID, bubbleID string) (bool, error) {
	// First check if the user is the sender of the message
	query := `SELECT sender_id FROM messages WHERE id = ? AND bubble_id = ?`

	var senderID string
	err := s.cassandra.Session.Query(query, messageID, bubbleID).Scan(&senderID)
	if err != nil {
		s.logger.Error("Failed to get message sender", zap.Error(err))
		return false, err
	}

	// User owns the message
	if senderID == userID {
		return true, nil
	}

	// Check if user is bubble creator/admin (optional additional permission)
	bubbleQuery := `
		SELECT creator_id FROM bubbles
		WHERE id = $1`

	var creatorID string
	err = s.db.Pool.QueryRow(ctx, bubbleQuery, bubbleID).Scan(&creatorID)
	if err != nil {
		s.logger.Error("Failed to get bubble creator", zap.Error(err))
		return false, err
	}

	// User is bubble creator, allow modification
	if creatorID == userID {
		return true, nil
	}

	return false, nil
}

// editMessage handles editing a message
func (s *Service) editMessage(c *gin.Context) {
	userID, _ := c.Get("user_id")
	messageID := c.Param("messageId")

	if messageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Message ID is required"})
		return
	}

	var req struct {
		Content string `json:"content" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate message content
	if len(req.Content) > s.config.Social.Chat.MaxMessageLength {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Message content too long"})
		return
	}

	bubbleID := c.Param("bubbleId")
	if bubbleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Bubble ID is required"})
		return
	}

	// Verify message ownership
	hasPermission, err := s.verifyMessageOwnership(c.Request.Context(), userID.(string), messageID, bubbleID)
	if err != nil {
		s.logger.Error("Failed to verify message ownership", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify permissions"})
		return
	}

	if !hasPermission {
		c.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to edit this message"})
		return
	}

	err = s.cassandra.UpdateMessage(c.Request.Context(), bubbleID, messageID, req.Content)
	if err != nil {
		s.logger.Error("Failed to update message", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to edit message"})
		return
	}

	// Publish the edit via MQTT
	updated := &database.Message{
		BubbleID:  bubbleID,
		MessageID: messageID,
		Content:   req.Content,
		IsEdited:  true,
		UpdatedAt: time.Now(),
	}
	s.publishMessageEdit(bubbleID, updated)

	s.logger.Info("Message edited successfully",
		zap.String("message_id", messageID),
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "Message edited successfully"})
}

// deleteMessage handles deleting a message
func (s *Service) deleteMessage(c *gin.Context) {
	userID, _ := c.Get("user_id")
	messageID := c.Param("messageId")

	if messageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Message ID is required"})
		return
	}

	bubbleID := c.Param("bubbleId")
	if bubbleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Bubble ID is required"})
		return
	}

	// Verify message ownership
	hasPermission, err := s.verifyMessageOwnership(c.Request.Context(), userID.(string), messageID, bubbleID)
	if err != nil {
		s.logger.Error("Failed to verify message ownership", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to verify permissions"})
		return
	}

	if !hasPermission {
		c.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to delete this message"})
		return
	}

	err = s.cassandra.DeleteMessage(c.Request.Context(), bubbleID, messageID)
	if err != nil {
		s.logger.Error("Failed to delete message", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete message"})
		return
	}

	// Publish the deletion via MQTT
	s.publishMessageDeletion(bubbleID, messageID)

	s.logger.Info("Message deleted successfully",
		zap.String("message_id", messageID),
		zap.String("user_id", userID.(string)))

	c.JSON(http.StatusOK, gin.H{"message": "Message deleted successfully"})
}

// sendTypingIndicator handles sending typing indicators
func (s *Service) sendTypingIndicator(c *gin.Context) {
	userID, _ := c.Get("user_id")

	var req struct {
		BubbleID string `json:"bubble_id" binding:"required"`
		IsTyping bool   `json:"is_typing"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Create typing indicator
	indicator := &TypingIndicator{
		BubbleID: req.BubbleID,
		UserID:   userID.(string),
		IsTyping: req.IsTyping,
	}

	// Publish typing indicator via MQTT
	s.publishTypingIndicator(req.BubbleID, indicator)

	c.JSON(http.StatusOK, gin.H{"message": "Typing indicator sent"})
}

// getConversations handles getting user's direct message conversations
func (s *Service) getConversations(c *gin.Context) {
	userID, _ := c.Get("user_id")

	// Parse pagination limit
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	if limit <= 0 || limit > 100 {
		limit = 50
	}

	conversations, err := s.cassandra.GetUserConversations(c.Request.Context(), userID.(string), limit)
	if err != nil {
		s.logger.Error("Failed to retrieve conversations", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch conversations"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"conversations": conversations,
		"count":         len(conversations),
	})
}

// sendDirectMessage handles sending a direct message
func (s *Service) sendDirectMessage(c *gin.Context) {
	userID, _ := c.Get("user_id")
	conversationID := c.Param("conversationId")

	var req struct {
		Content     string  `json:"content" binding:"required"`
		MessageType string  `json:"message_type"`
		MediaURL    *string `json:"media_url"`
		ReplyToID   *string `json:"reply_to_id"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Rate limiting for direct messages
	allowed, err := s.rateLimiter.AllowSocialOperation(c.Request.Context(), userID.(string), "message_send")
	if err != nil {
		s.logger.Error("Rate limit check failed", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if !allowed {
		c.JSON(http.StatusTooManyRequests, gin.H{"error": "Message sending rate limit exceeded"})
		return
	}

	// Validate conversation and determine recipient
	conv, err := s.cassandra.GetConversationByID(c.Request.Context(), conversationID)
	if err != nil {
		s.logger.Error("Failed to fetch conversation", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	if conv == nil || (conv.Participant1ID != userID.(string) && conv.Participant2ID != userID.(string)) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Not a participant of the conversation"})
		return
	}

	var recipientID string
	if conv.Participant1ID == userID.(string) {
		recipientID = conv.Participant2ID
	} else {
		recipientID = conv.Participant1ID
	}

	// Create direct message
	message := &ConversationMessage{
		MessageID:      uuid.New().String(),
		ConversationID: conversationID,
		SenderID:       userID.(string),
		RecipientID:    recipientID,
		Content:        req.Content,
		MessageType:    req.MessageType,
		MediaURL:       req.MediaURL,
		ReplyToID:      req.ReplyToID,
		IsEdited:       false,
		IsDeleted:      false,
		IsRead:         false,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Save to Cassandra (conversation_messages table)
	cassandraMsg := &database.ConversationMessage{
		ConversationID: message.ConversationID,
		MessageID:      message.MessageID,
		SenderID:       message.SenderID,
		RecipientID:    message.RecipientID,
		Content:        message.Content,
		MessageType:    message.MessageType,
		MediaURL:       message.MediaURL,
		ReplyToID:      message.ReplyToID,
		IsEdited:       message.IsEdited,
		IsDeleted:      message.IsDeleted,
		IsRead:         message.IsRead,
		CreatedAt:      message.CreatedAt,
		UpdatedAt:      message.UpdatedAt,
	}

	if err := s.cassandra.CreateConversationMessage(c.Request.Context(), cassandraMsg); err != nil {
		s.logger.Error("Failed to save direct message", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send message"})
		return
	}

	// Update conversation metadata
	if err := s.cassandra.UpdateConversationLastMessage(c.Request.Context(), conversationID, message.MessageID, message.CreatedAt); err != nil {
		s.logger.Warn("Failed to update conversation metadata", zap.Error(err))
	}

	// Publish direct message via MQTT
	s.publishConversationMessage(message)

	s.logger.Info("Direct message sent successfully",
		zap.String("message_id", message.MessageID),
		zap.String("conversation_id", conversationID),
		zap.String("sender_id", userID.(string)))

	c.JSON(http.StatusCreated, message)
}

// getConversationMessages handles getting messages for a conversation
func (s *Service) getConversationMessages(c *gin.Context) {
	conversationID := c.Param("conversationId")
	if conversationID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Conversation ID is required"})
		return
	}

	// Pagination
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	if limit <= 0 || limit > 100 {
		limit = 50
	}

	var pageState []byte
	if psStr := c.Query("page_state"); psStr != "" {
		decoded, err := base64.StdEncoding.DecodeString(psStr)
		if err == nil {
			pageState = decoded
		}
	}

	msgs, nextState, err := s.cassandra.GetConversationMessages(c.Request.Context(), conversationID, limit, pageState)
	if err != nil {
		s.logger.Error("Failed to retrieve conversation messages", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch messages"})
		return
	}

	var nextStateStr string
	if len(nextState) > 0 {
		nextStateStr = base64.StdEncoding.EncodeToString(nextState)
	}

	c.JSON(http.StatusOK, gin.H{
		"messages":        msgs,
		"count":           len(msgs),
		"next_page_state": nextStateStr,
	})
}

// Helper methods

// publishMessage publishes a message via MQTT
func (s *Service) publishMessage(message *Message) {
	topic := fmt.Sprintf("bubbles/%s/messages", message.BubbleID)

	payload, err := json.Marshal(message)
	if err != nil {
		s.logger.Error("Failed to marshal message for MQTT", zap.Error(err))
		return
	}

	token := s.mqttClient.Publish(topic, s.config.MQTT.QoS, false, payload)
	if token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to publish message via MQTT", zap.Error(token.Error()))
	}
}

// publishTypingIndicator publishes typing indicator via MQTT
func (s *Service) publishTypingIndicator(bubbleID string, indicator *TypingIndicator) {
	topic := fmt.Sprintf("bubbles/%s/typing", bubbleID)
	payload, err := json.Marshal(indicator)
	if err != nil {
		s.logger.Error("Failed to marshal typing indicator", zap.Error(err))
		return
	}

	token := s.mqttClient.Publish(topic, s.config.MQTT.QoS, false, payload)
	if token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to publish typing indicator via MQTT", zap.Error(token.Error()))
	}
}

// publishDirectMessage publishes direct message via MQTT
func (s *Service) publishDirectMessage(message *database.Message) {
	topic := fmt.Sprintf("users/%s/messages", message.RecipientID)
	payload, err := json.Marshal(message)
	if err != nil {
		s.logger.Error("Failed to marshal direct message", zap.Error(err))
		return
	}

	token := s.mqttClient.Publish(topic, s.config.MQTT.QoS, false, payload)
	if token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to publish direct message via MQTT", zap.Error(token.Error()))
	}
}

// Note: Typing indicators are now handled entirely via MQTT v5
// The old handleTypingIndicator function has been removed as all real-time
// communication now goes through MQTT for better reliability and performance.

// Note: Bubble join/leave functionality is now handled entirely via MQTT v5
// Users subscribe directly to MQTT topics for real-time updates.
// The old handleJoinBubble and handleLeaveBubble functions have been removed
// as all real-time communication now goes through MQTT for better reliability.

// authMiddleware provides authentication middleware
func (s *Service) authMiddleware() gin.HandlerFunc {
	return authmw.AuthMiddleware(s.oryClient, s.logger)
}

// publishMessageEdit publishes a message edit event via MQTT
func (s *Service) publishMessageEdit(bubbleID string, message *database.Message) {
	topic := fmt.Sprintf("hopen/bubbles/%s/chat", bubbleID)

	payload := map[string]interface{}{
		"type":       "message_edited",
		"message_id": message.MessageID,
		"content":    message.Content,
		"is_edited":  message.IsEdited,
		"updated_at": message.UpdatedAt,
	}

	data, err := json.Marshal(payload)
	if err != nil {
		s.logger.Error("Failed to marshal message edit payload", zap.Error(err))
		return
	}

	if token := s.mqttClient.Publish(topic, 1, false, data); token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to publish message edit via MQTT", zap.Error(token.Error()))
	}
}

// publishMessageDeletion publishes a message deletion event via MQTT
func (s *Service) publishMessageDeletion(bubbleID, messageID string) {
	topic := fmt.Sprintf("hopen/bubbles/%s/chat", bubbleID)

	payload := map[string]interface{}{
		"type":       "message_deleted",
		"message_id": messageID,
		"deleted_at": time.Now(),
	}

	data, err := json.Marshal(payload)
	if err != nil {
		s.logger.Error("Failed to marshal message deletion payload", zap.Error(err))
		return
	}

	if token := s.mqttClient.Publish(topic, 1, false, data); token.Wait() && token.Error() != nil {
		s.logger.Error("Failed to publish message deletion via MQTT", zap.Error(token.Error()))
	}
}
